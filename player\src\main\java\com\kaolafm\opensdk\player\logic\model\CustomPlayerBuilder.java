package com.kaolafm.opensdk.player.logic.model;

import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * <AUTHOR> on 2019/4/1.
 */

public class CustomPlayerBuilder extends PlayerBuilder{
    /**
     * 单曲id
     */
    private String mChildId;

    /**
     * 需要seek的点
     */
    private long mSeekPosition;

    /**
     * 排序方式
     */
    private int mSort = PlayerConstants.SORT_ACS;


    public CustomPlayerBuilder() {
    }

    public String getChildId() {
        return mChildId;
    }

    public CustomPlayerBuilder setChildId(String sonId) {
        this.mChildId = sonId;
        return CustomPlayerBuilder.this;
    }

    public long getSeekPosition() {
        return mSeekPosition;
    }

    public CustomPlayerBuilder setSeekPosition(long seekPosition) {
        this.mSeekPosition = seekPosition;
        return CustomPlayerBuilder.this;
    }

    public int getSort() {
        return mSort;
    }

    public CustomPlayerBuilder setSort(int mSort) {
        this.mSort = mSort;
        return CustomPlayerBuilder.this;
    }
}
