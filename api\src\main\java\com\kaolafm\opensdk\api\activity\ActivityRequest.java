package com.kaolafm.opensdk.api.activity;

import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.activity.model.Activity;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.List;

public class ActivityRequest extends BaseRequest {

    private final ActivityService mService;

    public ActivityRequest() {
        this.mService = obtainRetrofitService(ActivityService.class);
    }

    /**
     * 活动列表
     */
    private BasePageResult<List<Activity>> getInfoListSync(String appid) {
        BaseResult<BasePageResult<List<Activity>>> baseResult = doHttpDealSync(mService.getInfoListSync(appid));
        return baseResult != null? baseResult.getResult() : null;
    }

    /**
     * 活动列表
     */
    public void getInfoList(String appid, HttpCallback<BasePageResult<List<Activity>>> callback){
        if(mService==null){
            return;
        }
        doHttpDeal(mService.getInfoList(appid), BaseResult::getResult, callback);
    }

    /**
     * 活动列表
     */
    public void getInfoList(HttpCallback<BasePageResult<List<Activity>>> callback){
        String appId = "";
        try {
            ApplicationInfo applicationInfo = ComponentKit.getInstance().getApplication().getPackageManager().getApplicationInfo(ComponentKit.getInstance().getApplication().getPackageName(), PackageManager.GET_META_DATA);
            appId = applicationInfo.metaData.getString("com.kaolafm.open.sdk.AppId");
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        doHttpDeal(mService.getInfoList(appId), BaseResult::getResult, callback);
    }
}
