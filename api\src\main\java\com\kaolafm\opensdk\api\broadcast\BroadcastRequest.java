package com.kaolafm.opensdk.api.broadcast;

import android.support.annotation.Nullable;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.http.core.HttpCallback;
import java.util.List;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: BroadcastRequest.java                                               
 *                                                                  *
 * Created in 2018/8/13 下午4:06                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class BroadcastRequest extends BaseRequest {


    private BroadcastService mService;

    public BroadcastRequest() {
        mService = obtainRetrofitService(BroadcastService.class);
    }

    /**
     * 获取广播电台详情
     *
     * @param broadcastId 在线广播ID
     * @param callback    获取数据回调
     */
    public void getBroadcastDetails(long broadcastId, HttpCallback<BroadcastDetails> callback) {
        doHttpDeal(mService.getBroadcastDetails(broadcastId), BaseResult::getResult, callback);
    }


    /**
     * 获取广播电台节目单列表
     *
     * @param broadcastId 在线广播ID
     * @param date        日期
     * @param callback    获取数据回调
     */
    public void getBroadcastProgramList(long broadcastId, @Nullable String date, HttpCallback<List<ProgramDetails>> callback) {
        doHttpDeal(mService.getBroadcastProgramList(broadcastId, date), BaseResult::getResult, callback);
    }

//    /**
//     * 获取明天电台节目单列表
//     *
//     * @param broadcastId 在线广播电台ID
//     * @param date        日期
//     * @param callback    获取数据回调
//     */
//    public void getBroadcastTomorrowProgramList(long broadcastId, @Nullable String date, HttpCallback<List<ProgramDetails>> callback) {
//        doHttpDeal(mService.getBroadcastTomorrowProgramList(broadcastId, date), BaseResult::getResult, callback);
//    }

    /**
     * 获取广播节目详情
     *
     * @param programId 广播节目ID
     * @param callback  获取数据回调
     */
    public void getBroadcastProgramDetails(long programId, HttpCallback<ProgramDetails> callback) {
        doHttpDeal(mService.getBroadcastProgramDetails(programId), BaseResult::getResult, callback);

    }

    /**
     * 获取广播电台当前节目对象
     *
     * @param broadcastId 在线广播电台ID
     * @param callback    获取数据回调
     */
    public void getBroadcastCurrentProgramDetails(long broadcastId, HttpCallback<ProgramDetails> callback) {
        doHttpDeal(mService.getBroadcastCurrentProgramDetails(broadcastId), BaseResult::getResult, callback);
    }

    /**
     * 根据广播id返回地方台或国家台广播列表
     *
     * @param broadcastId 在线广播电台ID
     * @param pagenum
     * @param pagesize
     * @param callback    获取数据回调
     */
    public void getBroadcastNeighborList(long broadcastId,int pagenum,int pagesize, HttpCallback<BasePageResult<List<BroadcastDetails>>> callback) {
        doHttpDeal(mService.getBroadcastNeighborList(broadcastId,pagenum,pagesize), BaseResult::getResult, callback);
    }
}
