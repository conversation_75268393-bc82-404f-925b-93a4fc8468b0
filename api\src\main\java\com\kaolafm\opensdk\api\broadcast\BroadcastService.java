package com.kaolafm.opensdk.api.broadcast;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.HostConstant;
import com.kaolafm.opensdk.api.KaolaApiConstant;

import java.util.List;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: BroadcastService.java                                               
 *                                                                  *
 * Created in 2018/8/13 下午4:06                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
/*package*/ interface BroadcastService {

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.GET_BROADCAST_LIST)
    Single<BaseResult<BasePageResult<List<BroadcastDetails>>>> getBroadcastList(@Query("type") int type, @Query("classifyid") int classifyId, @Query("pagenum") int pageNum, @Query("pagesize") int pageSize, @Query("area") int area);

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.GET_BROADCAST_NEIGHBOR_LIST)
    Single<BaseResult<BasePageResult<List<BroadcastDetails>>>> getBroadcastNeighborList(@Query("id") long id, @Query("pagenum") int pagenum, @Query("pagesize") int pagesize);


    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.GET_BROADCAST_DETAILS)
    Single<BaseResult<BroadcastDetails>> getBroadcastDetails(@Query("bid") long broadcastId);

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.GET_BROADCAST_PROGRAM_LIST)
    Single<BaseResult<List<ProgramDetails>>> getBroadcastProgramList(@Query("bid") long broadcastId, @Query("date") String date);

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.GET_BROADCAST_DETAILS_TOMORROW)
    Single<BaseResult<List<ProgramDetails>>> getBroadcastTomorrowProgramList(@Query("bid") long broadcastId, @Query("date") String date);

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.GET_BROADCAST_PROGRAM_DETAILS)
    Single<BaseResult<ProgramDetails>> getBroadcastProgramDetails(@Query("programid") long programId);

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.GET_BROADCAST_CURRENT_PROGRAM)
    Single<BaseResult<ProgramDetails>> getBroadcastCurrentProgramDetails(@Query("bid") long broadcastId);


    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.GET_BROADCAST_AREA_LIST)
    Single<BaseResult<List<BroadcastArea>>> getBroadcastAreaList();

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.GET_BROADCAST_AREA)
    Single<BaseResult<BroadcastArea>> getBroadcastArea(@Query("lon") float longitude, @Query("lat") float latitude);

}
