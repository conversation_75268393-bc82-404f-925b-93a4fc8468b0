package com.kaolafm.opensdk.demo.live.ui;

import android.content.Context;
import android.support.v7.app.AlertDialog.Builder;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Toast;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.demo.R;

/**
 * <AUTHOR>
 * @date 2018/11/19
 */

public class InputInfoDialog {

    public static final int TYPE_THRID = 2;

    public static final int TYPE_PHONE = 1;

    @BindView(R.id.et_live_avatar)
    EditText mEtLiveAvatar;

    @BindView(R.id.et_live_nickname)
    EditText mEtLiveNickname;

    @BindView(R.id.et_live_uid)
    EditText mEtLiveUid;

    @BindView(R.id.rb_live_kradio)
    RadioButton mRbLiveKradio;

    @BindView(R.id.rb_live_third)
    RadioButton mRbLiveThird;

    @BindView(R.id.rg_live_account_type)
    RadioGroup mRgLiveAccountType;

    private Builder mBuilder;

    private Context mContext;

    private OnPositiveClickListener mListener;

    private int mAccountType = TYPE_PHONE;

    public InputInfoDialog(Context context) {
        mContext = context;
        View view = LayoutInflater.from(context).inflate(R.layout.dialog_input_live_info, null);
        ButterKnife.bind(this, view);

        mBuilder = new Builder(context).setView(view);
        mBuilder.setOnDismissListener(dialog -> {
            mListener = null;
            mBuilder = null;
            mContext = null;
        });
        mRgLiveAccountType.setOnCheckedChangeListener((group, checkedId) -> showLayout());
        showLayout();
    }
    private void showLayout() {
        if (mRbLiveKradio.isChecked()) {
            mAccountType = TYPE_PHONE;
            mEtLiveAvatar.setVisibility(View.GONE);
            mEtLiveUid.setHint("请输入用户id，默认100028");
            mEtLiveNickname.setHint("请输入手机号，默认***********");
        }else if (mRbLiveThird.isChecked()) {
            mAccountType = TYPE_THRID;
            mEtLiveAvatar.setVisibility(View.VISIBLE);
            mEtLiveUid.setHint("请输入用户唯一标识，默认已登录的uid");
            mEtLiveNickname.setHint("请输入昵称，默认已登录的昵称");
        }
    }

    public void show() {

        mBuilder.setPositiveButton("确定", (dialog, which) -> {
            String id = mEtLiveUid.getText().toString().trim();
            if (TextUtils.isEmpty(id)) {
                if (mAccountType == TYPE_PHONE) {
                    id = "100028";
                }else {
                    id = AccessTokenManager.getInstance().getKaolaAccessToken().getUserId();
                }
            }
            String nickName = mEtLiveNickname.getText().toString().trim();
            if (TextUtils.isEmpty(nickName)) {
                if (mAccountType == TYPE_PHONE) {
                    nickName = "***********";
                }else {
                    nickName = UserInfoManager.getInstance().getNickName();
                }
            }
            String avatar = mEtLiveAvatar.getText().toString().trim();
            if (TextUtils.isEmpty(avatar)) {
                avatar = UserInfoManager.getInstance().getAvatar();
            }
            if (mListener != null) {
                mListener.onClick(mAccountType, id, nickName, avatar);
            }
            dialog.dismiss();
        }).setNegativeButton("取消", (dialog, which) -> dialog.dismiss()).create().show();
    }

    private void showToast(String msg) {
        Toast.makeText(mContext, msg, Toast.LENGTH_LONG).show();
    }

    public InputInfoDialog setPositiveButton(OnPositiveClickListener listener) {
        mListener = listener;
        return this;
    }

    public interface OnPositiveClickListener {

        void onClick(int type, String id, String nicknameOrPhone, String avatar);
    }


}
