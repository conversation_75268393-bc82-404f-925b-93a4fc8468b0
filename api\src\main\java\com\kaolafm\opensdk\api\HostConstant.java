package com.kaolafm.opensdk.api;

import com.kaolafm.opensdk.Constant;
import com.kaolafm.opensdk.http.urlmanager.UrlManager;

/**
 * host地址及对应的domain name和headers(用于区分域名)
 *
 * <AUTHOR> on 2018/5/21.
 */

public final class HostConstant {

    public static final String OPEN_KAOLA_HOST = Constant.OPEN_KAOLA_HOST;
    /**
     * open.kaolafm
     */
    public static final String BASE_HOST_URL_OPEN_KAOLA_FM = Constant.BASE_HOST_URL_OPEN_KAOLA_FM;

    public static final String DOMAIN_NAME_OPEN_KAOLA = Constant.DOMAIN_NAME_OPEN_KAOLA;

    public static final String DOMAIN_HEADER_OPEN_KAOLA = Constant.DOMAIN_HEADER_OPEN_KAOLA;

    /**
     * 搜索HTTPS
     */
    public static final String BASE_HOST_SEARCH_HTTPS = "https://iovsearch.radio.cn";
    /**
     * 搜索http
     */
    public static final String BASE_HOST_SEARCH_HTTP = "http://api.search.kaolafm.com";

    public static final String DOMAIN_NAME_SEARCH = "voice_search";

    public static final String DOMAIN_HEADER_SEARCH = UrlManager.DOMAIN_NAME_HEADER
            + DOMAIN_NAME_SEARCH;

    /**
     * QQ音乐的host地址
     */
    public static final String QQMUSIC_HOST = "http://open.music.qq.com";

    public static final String QQMUSIC_DOMAIN_NAME = "QQMusic";

    public static final String QQMUSIC_DOMAIN_HEADER = UrlManager.DOMAIN_NAME_HEADER + QQMUSIC_DOMAIN_NAME;

    /**
     * 品牌信息的Host地址
     */
    public static final String BRAND_HOST = "https://iovopen.radio.cn";

    public static final String BRAND_DOMAIN_NAME = "BRAND";

    public static final String BRAND_DOMAIN_HEADER = UrlManager.DOMAIN_NAME_HEADER + BRAND_DOMAIN_NAME;

    /**
     * 添加到头部信息，表示使用https
     */
    public static final String DOMAIN_HEADER_HTTPS_PROTOCOL = UrlManager.HTTPS_PROTOCOL + ":"+ "https";
}
