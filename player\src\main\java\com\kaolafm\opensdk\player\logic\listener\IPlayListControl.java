package com.kaolafm.opensdk.player.logic.listener;

import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlaylistInfo;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.List;

/**
 * <AUTHOR> on 2019/3/18.
 */

public interface IPlayListControl {

    /**
     * 初始化播单
     */
    void initPlayList(PlayerBuilder playerBuilder, IPlayListGetListener iPlayListGetListener);

    /**
     * 是否有下一页数据
     *
     * @return
     */
    boolean hasNextPage();

    /**
     * 是否有上一页
     *
     * @return
     */
    boolean hasPrePage();

    /**
     * 获取下一个PlayItem
     *
     * @return
     */
    void getNextPlayItem(IPlayListGetListener iPlayListGetListener);

    /**
     * 获取上一个PlayItem
     *
     * @return
     */
    void getPrePlayItem(IPlayListGetListener iPlayListGetListener);

    /**
     * 获取当前播放PlayItem
     *
     * @return
     */
    PlayItem getCurPlayItem();

    /**
     * PlayItem
     *
     * @param playerBuilder
     * @return
     */
    PlayItem getPlayItem(PlayerBuilder playerBuilder);

    /**
     * 当前list里面是否存在
     *
     * @param id
     * @return
     */
    boolean isExistPlayItem(long id);

    /**
     * 获取当前播放播单位置
     *
     * @return
     */
    int getCurPosition();

    /**
     * 设置播单当前索引值
     *
     * @param position
     */
    void setCurPosition(int position);

    /**
     * 设置当前播放位置
     *
     * @param playItem
     */
    void setCurPosition(PlayItem playItem);

    /**
     * 是否存在上一曲目
     *
     * @return true为有上一曲目，false为没有上一曲目
     */
    boolean hasPre();

    /**
     * 是否存在下一曲目
     *
     * @return true为有下一个曲目，false为没有下一个曲目
     */
    boolean hasNext();

    /**
     * 获取播放列表
     *
     * @return
     */
    List<PlayItem> getPlayList();

    /**
     * 获取播单详情
     *
     * @return
     */
    PlaylistInfo getPlayListInfo();


    /**
     * 获取下一页数据
     */
    void loadNextPage(IPlayListGetListener iPlayListGetListener);

    /**
     * 获取下一页数据
     */
    void loadNextPage(long audioId, IPlayListGetListener iPlayListGetListener);

    /**
     * 获取上一页数据
     */
    void loadPrePage(IPlayListGetListener iPlayListGetListener);

    /**
     * 资讯类型刷新获取数据
     */
    void loadPageData(int type, long audioId, int pageNum, IPlayListGetListener iPlayListGetListener);

    /**
     * 设置播单变化IPlayListControlListener
     */
    void setCallback(IPlayListStateListener iPlayListControlListener);

    void release();

    /**
     * 清空播单
     */
    void clearPlayList();
}
