<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="10dp"
    >
    <TextView
        android:id="@+id/tv_category_member_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="100个分类成员"
        />
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/colorBlack"
        app:layout_constraintTop_toBottomOf="@id/tv_category_member_num"
        />
    <com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout
        android:id="@+id/trf_category_member_refresh"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_category_member_num"
        >

        <android.support.v7.widget.RecyclerView
            android:id="@+id/rv_category_member_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout>


</android.support.constraint.ConstraintLayout>
