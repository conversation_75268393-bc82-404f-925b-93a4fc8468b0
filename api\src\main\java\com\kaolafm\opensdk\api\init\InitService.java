package com.kaolafm.opensdk.api.init;

import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.HostConstant;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.report.model.KaolaActivateData;

import java.util.Map;

import io.reactivex.Single;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.QueryMap;

/**
 * 初始化网络请求接口
 *
 * <AUTHOR>
 * @date 2018/7/25
 */
interface InitService {

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.REQUEST_KAOLA_INIT)
    Single<BaseResult<KaolaActivateData>> initKaola();

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.REQUEST_KAOLA_INIT)
    Call<BaseResult<KaolaActivateData>> initKaolaSync();

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @POST(KaolaApiConstant.REQUEST_KAOLA_ACTIVATE)
    Single<BaseResult<KaolaActivateData>> activeKaola(@QueryMap Map<String, String> map);

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @POST(KaolaApiConstant.REQUEST_KAOLA_ACTIVATE)
    Call<BaseResult<KaolaActivateData>> activeKaolaSync(@QueryMap Map<String, String> map);

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.GET_BRAND)
    Single<BaseResult<String>> getBrand();
}
