package com.kaolafm.opensdk.apt;

import com.google.auto.service.AutoService;

import java.util.Set;
import java.util.function.Consumer;

import javax.annotation.processing.AbstractProcessor;
import javax.annotation.processing.Messager;
import javax.annotation.processing.ProcessingEnvironment;
import javax.annotation.processing.Processor;
import javax.annotation.processing.RoundEnvironment;
import javax.annotation.processing.SupportedAnnotationTypes;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.Element;
import javax.lang.model.element.TypeElement;
import javax.lang.model.util.Elements;
import javax.tools.Diagnostic;

/**
 * <AUTHOR>
 * @date 2020/8/10
 */
@SupportedAnnotationTypes({"com.kaolafm.opensdk.apt.DBOpt"})
@AutoService(Processor.class)
public class DataBaseProcessor extends AbstractProcessor {

    private Messager messager;

    private Elements mElementUtils;

    @Override
    public synchronized void init(ProcessingEnvironment processingEnvironment) {
        messager = processingEnvironment.getMessager();
        messager.printMessage(Diagnostic.Kind.NOTE, "初始化");
        mElementUtils = processingEnvironment.getElementUtils();
    }

    @Override
    public boolean process(Set<? extends TypeElement> set, RoundEnvironment roundEnvironment) {
        log("process");
        Set<? extends Element> elements = roundEnvironment.getElementsAnnotatedWith(DBOpt.class);

        elements.forEach((Consumer<Element>) element -> {
            log("element=");
        });

        return false;
    }
    @Override
    public SourceVersion getSupportedSourceVersion() {
        return SourceVersion.latestSupported();
    }

    private void log(String msg) {
        messager.printMessage(Diagnostic.Kind.NOTE, msg);
    }
}
