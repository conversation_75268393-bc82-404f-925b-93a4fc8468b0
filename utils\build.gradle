apply plugin: 'com.android.library'
apply plugin: "build-jar"

def and = rootProject.ext.android
def dependent = rootProject.ext.dependencies

def VERSION_CODE = and.versionCode
def VERSION_NAME = and.versionName


android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion
    defaultConfig {
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode VERSION_CODE
        versionName VERSION_NAME
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-utils-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility and.javaSourceVersion
        targetCompatibility and.javaTargetVersion
    }
    packagingOptions {
        exclude 'LICENSE.txt'
    }

}

dependencies {
    testImplementation 'junit:junit:4.12'
    androidTestImplementation('com.android.support.test.espresso:espresso-core:3.0.2', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    testImplementation 'org.mockito:mockito-core:2.19.0'
    androidTestImplementation 'com.android.support:support-annotations:27.1.1'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test:rules:1.0.2'

    compileOnly 'com.android.support:appcompat-v7:27.0.2'
    compileOnly fileTree(dir: 'libs', include: ['*.jar'])
}

upload {
    sdkFlavor {
        proguardConfigFile = ["proguard-utils-rules.pro"]
        includePackage = ["com/kaolafm/base"]
        versionName = VERSION_NAME
        outputFileName = "Utils"
        mavenConfig {
            artifactId     'utils'
            groupId        'com.kaolafm'
            libType        'jar'
            libDescription 'SDK Utils'
//                repository      readLocalProperties('local.repo.url')
        }
    }
}
