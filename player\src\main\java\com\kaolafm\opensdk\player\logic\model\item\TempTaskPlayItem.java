package com.kaolafm.opensdk.player.logic.model.item;

import android.os.Parcel;

import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.UIThreadUtil;

/**
 * 临时任务
 */
public class TempTaskPlayItem extends PlayItem {
    /**
     * 是否需要临时任务播放状态回调
     */
    private boolean isNeedPlayStateCallBack;

    private BasePlayStateListener mPlayStateListener;

    private boolean playerIsPlaying;

    /**
     * 临时任务类型
     */
    private int tempTaskType;

    /**
     * 是否需要处理内部逻辑
     */
    private boolean isNeedNextInnerAction = true;

    public TempTaskPlayItem() {

    }

    @Override
    public String getRadioId() {
        return null;
    }

    @Override
    public String getAlbumId() {
        return null;
    }

    @Override
    public String getTitle() {
        return null;
    }

    @Override
    public String getPicUrl() {
        return null;
    }

    @Override
    public String getHost() {
        return null;
    }

    @Override
    public String getAlbumTitle() {
        return null;
    }

    @Override
    public int getType() {
        return PlayerConstants.RESOURCES_TYPE_TEMP_TASK;
    }

    public boolean isNeedNextInnerAction() {
        return isNeedNextInnerAction;
    }

    public void setNeedNextInnerAction(boolean needNextInnerAction) {
        isNeedNextInnerAction = needNextInnerAction;
    }

    public boolean getPlayerIsPlaying() {
        return playerIsPlaying;
    }

    public void setPlayerIsPlaying(boolean playerIsPlaying) {
        this.playerIsPlaying = playerIsPlaying;
    }

    public BasePlayStateListener getPlayStateListener() {
        return mPlayStateListener;
    }

    public void setPlayStateListener(BasePlayStateListener playStateListener) {
        this.mPlayStateListener = playStateListener;
    }

    public boolean isNeedPlayStateCallBack() {
        return isNeedPlayStateCallBack;
    }

    public void setNeedPlayStateCallBack(boolean needPlayStateCallBack) {
        isNeedPlayStateCallBack = needPlayStateCallBack;
    }

    public int getTempTaskType() {
        return tempTaskType;
    }

    public void setTempTaskType(int tempTaskType) {
        this.tempTaskType = tempTaskType;
    }

    private TempTaskPlayItem(Parcel parcel) {

    }

    public static final Creator<TempTaskPlayItem> CREATOR = new Creator<TempTaskPlayItem>() {

        @Override
        public TempTaskPlayItem createFromParcel(Parcel source) {
            return new TempTaskPlayItem(source);
        }

        @Override
        public TempTaskPlayItem[] newArray(int size) {
            return new TempTaskPlayItem[size];
        }
    };

    public void notifyStateChange(int type) {
        UIThreadUtil.runUIThread(() -> notifyStateChangeInner(type, 0, 0));
    }


    public void notifyStateChange(int type, long position, long total) {
        UIThreadUtil.runUIThread(() -> notifyStateChangeInner(type, position, total));
    }

    private void notifyStateChangeInner(int type, long position, long total) {
        if (mPlayStateListener == null) {
            return;
        }
        switch (type) {
            case PlayerConstants.TYPE_PLAYER_IDLE:
                mPlayStateListener.onIdle(this);
                break;
            case PlayerConstants.TYPE_PLAYER_PREPARING:
                mPlayStateListener.onPlayerPreparing(this);
                break;
            case PlayerConstants.TYPE_PLAYER_PLAYING:
                mPlayStateListener.onPlayerPlaying(this);
                break;
            case PlayerConstants.TYPE_PLAYER_PROGRESS: {
                setPosition((int) position);
                setDuration((int) total);
                mPlayStateListener.onProgress(this, position, total);
            }
            break;
            case PlayerConstants.TYPE_PLAYER_PAUSED:
                mPlayStateListener.onPlayerPaused(this);
                break;
            case PlayerConstants.TYPE_SEEK_START:
                mPlayStateListener.onSeekStart(this);
                break;
            case PlayerConstants.TYPE_SEEK_COMPLETE:
                mPlayStateListener.onSeekComplete(this);
                break;
            case PlayerConstants.TYPE_BUFFERING_START:
                mPlayStateListener.onBufferingStart(this);
                break;
            case PlayerConstants.TYPE_BUFFERING_END:
                mPlayStateListener.onBufferingEnd(this);
                break;
            case PlayerConstants.TYPE_PLAYER_END:
                mPlayStateListener.onPlayerEnd(this);
                break;
            case PlayerConstants.TYPE_PLAYER_FAILED:
                mPlayStateListener.onPlayerFailed(this, 0, 0);
                break;
            case PlayerConstants.TYPE_PLAYER_DOWNLOAD_PROGRESS:
                //listener.OnDownloadProgress(playItem, 0, 0);
                break;
            default:
                break;
        }
    }

}
