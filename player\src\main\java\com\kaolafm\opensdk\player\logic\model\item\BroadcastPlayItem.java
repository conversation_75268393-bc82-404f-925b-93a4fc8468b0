package com.kaolafm.opensdk.player.logic.model.item;

import android.os.Parcel;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.player.logic.model.item.model.InfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.TimeInfoData;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * 在线广播-播放对象
 */
public class BroadcastPlayItem extends PlayItem {

    /**
     * 信息相关数据
     */
    private InfoData mInfoData;

    /**
     * 播放时间相关数据
     */
    private TimeInfoData mTimeInfoData;

    /**
     * 广播频段
     */
    private String frequencyChannel;

    /**
     * 在线广播定制状态
     */
    private int status;

    /** 广播回放的状态 1开启节目，0关闭节目*/
    private int programEnable;

    public BroadcastPlayItem() {
        mInfoData = new InfoData();
        mTimeInfoData = new TimeInfoData();
    }

    @Override
    public String getRadioId() {
        return String.valueOf(mInfoData.getAlbumId());
    }


    @Override
    public String getAlbumId() {
        return String.valueOf(mInfoData.getAlbumId());
    }

    @Override
    public String getTitle() {
        return mInfoData.getTitle();
    }

    @Override
    public String getPicUrl() {
        String picUrl = mInfoData.getAudioPic();
        if (StringUtil.isEmpty(picUrl)) {
            picUrl = mInfoData.getAlbumPic();
        }
        return picUrl;
    }

    @Override
    public String getHost() {
        return mInfoData.getHosts();
    }
    @Override
    public String getUpdateTime() {
        return mInfoData.getUpdateTime();
    }

    @Override
    public String getAlbumTitle() {
        return mInfoData.getAlbumName();
    }

    @Override
    public String getBeginTime() {
        return mTimeInfoData.getBeginTime();
    }

    @Override
    public String getEndTime() {
        return mTimeInfoData.getEndTime();
    }

    @Override
    public int getType() {
        return PlayerConstants.RESOURCES_TYPE_BROADCAST;
    }

    public String getFrequencyChannel() {
        return frequencyChannel;
    }

    public void setFrequencyChannel(String frequencyChannel) {
        this.frequencyChannel = frequencyChannel;
    }

    @Override
    public int getStatus() {
        return status;
    }

    public int getProgramEnable() {
        return programEnable;
    }

    public void setProgramEnable(int programEnable) {
        this.programEnable = programEnable;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public boolean isLivingUrl() {
        return status == PlayerConstants.BROADCAST_STATUS_LIVING;
    }

    @Override
    public boolean isLiving() {
        return isLivingUrl();
    }

    public InfoData getInfoData() {
        return mInfoData;
    }

    public void setInfoData(InfoData infoData) {
        this.mInfoData = infoData;
    }

    public TimeInfoData getTimeInfoData() {
        return mTimeInfoData;
    }

    public void setTimeInfoData(TimeInfoData timeInfoData) {
        this.mTimeInfoData = timeInfoData;
    }

    private BroadcastPlayItem(Parcel parcel) {

    }

    public static final Creator<BroadcastPlayItem> CREATOR = new Creator<BroadcastPlayItem>() {

        @Override
        public BroadcastPlayItem createFromParcel(Parcel source) {
            return new BroadcastPlayItem(source);
        }

        @Override
        public BroadcastPlayItem[] newArray(int size) {
            return new BroadcastPlayItem[size];
        }
    };

    @Override
    public long getFinishTime() {
        return mTimeInfoData.getFinishTime();
    }
}
