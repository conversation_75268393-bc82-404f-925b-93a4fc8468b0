package com.kaolafm.opensdk.api.history;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.HostConstant;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.history.model.ListeningHistory;
import com.kaolafm.opensdk.api.history.model.SyncHistoryStatus;

import java.util.List;
import java.util.Map;

import io.reactivex.Single;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.QueryMap;

/**
 * <AUTHOR>
 * @date 2019/3/8
 */
public interface HistoryService {

    /**
     * 获取收听历史列表，一次获取所有的，最多99条。附：Headers第二个value是备用url，因为收听历史列表登录前和登录后不一样，这里放的是登录后的url
     * @return
     */
    @Headers({HostConstant.DOMAIN_HEADER_OPEN_KAOLA})
    @POST(KaolaApiConstant.KRADIO_GET_HISTORY_LIST_STRONG)
    Single<BaseResult<BasePageResult<List<ListeningHistory>>>> getHistoryList();

    /**
     * 同步收听历史到服务器
     * @param params 同步的收听信息
     * @return
     */
    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @POST(KaolaApiConstant.KRADIO_SAVE_LISTENING_HISTORY_STRONG)
    Single<BaseResult<SyncHistoryStatus>> saveListeningHistory(@Body Map<String, Object> params);

    /**
     * 清空收听历史
     * @return
     */
    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @POST(KaolaApiConstant.KRADIO_CLEAR_LISTENING_HISTORY_STRONG)
    Single<BaseResult<SyncHistoryStatus>> clearListeningHistory();
}
