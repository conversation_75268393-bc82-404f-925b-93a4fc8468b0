package com.kaolafm.opensdk.demo.purchase;

import android.view.View;
import android.widget.TextView;

import com.kaolafm.opensdk.api.purchase.model.QRCodeInfo;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;

import butterknife.BindView;

/**
 */
public class CommonAdapter extends BaseAdapter<Object> {

    @Override
    protected BaseHolder<Object> getViewHolder(View view, int viewType) {
        return new Holder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_common;
    }

    static class Holder extends BaseHolder<Object> {


        @BindView(R.id.tv_common_item)
        TextView mCommonItem;


        public Holder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(Object common, int position) {
            mCommonItem.setText(common.toString());
        }
    }
}
