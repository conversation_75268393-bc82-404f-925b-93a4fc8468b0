//package com.kaolafm.opensdk.player.core.exo;
//
//import android.content.Context;
//import android.net.Uri;
//
//import com.google.android.exoplayer2.ExoPlayerFactory;
//import com.google.android.exoplayer2.SimpleExoPlayer;
//import com.google.android.exoplayer2.source.ProgressiveMediaSource;
//import com.google.android.exoplayer2.upstream.DataSource;
//import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory;
//import com.google.android.exoplayer2.util.Util;
//
///**
// * <AUTHOR> on 2019-05-27.
// */
//
//public class ExoMediaPlayer {
//    private SimpleExoPlayer player;
//    private Context mContext;
//
//    public ExoMediaPlayer(Context context) {
//        mContext = context;
//        initializePlayer();
//    }
//
//    private void initializePlayer() {
//        if (player == null) {
//            player = ExoPlayerFactory.newSimpleInstance(mContext);
//        }
//    }
//
//    public void start(String source) {
//        DataSource.Factory dataSourceFactory = new DefaultDataSourceFactory(mContext,
//                Util.getUserAgent(mContext, "happy"));
//        player.prepare(new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(Uri.parse(source)));
//        player.setPlayWhenReady(true);
//    }
//
//    public String getUrl() {
//        return null;
//    }
//
//    public long getDuration() {
//        return 0;
//    }
//
//    public long getCurrentPosition() {
//        return 0;
//    }
//
//    public void pause() {
//        player.stop(false);
//    }
//
//    public void play() {
//    }
//
//    public void preload(String url) {
//
//    }
//
//    public void reset() {
//        player.stop();
//    }
//
//    public void release() {
//        player.release();
//    }
//
//    public void prepare(int needSeek) {
//
//    }
//
//    public void seek(long msec) {
//        player.seekTo(msec);
//    }
//
//    public int getPlayState() {
//        return player.getPlaybackState();
//    }
//}
