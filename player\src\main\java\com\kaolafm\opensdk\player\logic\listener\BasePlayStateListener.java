package com.kaolafm.opensdk.player.logic.listener;

import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/**
 * <AUTHOR> on 2019/3/19.
 */

public abstract class BasePlayStateListener implements IPlayerStateListener {

    @Override
    public void onIdle(PlayItem playItem) {

    }

    @Override
    public void onPlayerPreparing(PlayItem playItem) {

    }

    @Override
    public void onPlayerPlaying(PlayItem playItem) {

    }

    @Override
    public void onPlayerPaused(PlayItem playItem) {

    }

    @Override
    public void onProgress(PlayItem playItem, long progress, long total) {

    }

    @Override
    public void onPlayerFailed(PlayItem playItem, int what, int extra) {

    }

    @Override
    public void onPlayerEnd(PlayItem playItem) {

    }

    @Override
    public void onSeekStart(PlayItem playItem) {

    }

    @Override
    public void onSeekComplete(PlayItem playItem) {

    }

    @Override
    public void onBufferingStart(PlayItem playItem) {

    }

    @Override
    public void onBufferingEnd(PlayItem playItem) {

    }

    @Override
    public void onDownloadProgress(PlayItem playItem, long progress, long total) {

    }
}
