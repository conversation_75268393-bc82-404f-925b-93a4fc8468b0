package com.kaolafm.opensdk.di.component;

import android.app.Application;

import com.kaolafm.opensdk.Options;
import com.kaolafm.opensdk.account.token.RealAccessTokenManager;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.http.socket.SocketManager;

/**
 * 依赖注入Component的工具类。用于快速注入使用。
 * <AUTHOR>
 * @date 2020-03-02
 */
public class ComponentKit {

    private static volatile ComponentKit mInstance;
    private CoreComponent mComponent;

    private ComponentKit() {
    }

    public static ComponentKit getInstance() {
        if (mInstance == null) {
            synchronized (ComponentKit.class) {
                if (mInstance == null) {
                    mInstance = new ComponentKit();
                }
            }
        }
        return mInstance;
    }

    public <C extends CoreComponent, O extends Options, B extends CoreComponent.Builder<C, O>, T> C
    inject(B builder, Application application, O options, T instance) {
        C component = (C) builder
                .application(application)
                .options(options != null ? options : Options.DEFAULT)
                .build();
        if (component instanceof Injector) {
            ((Injector<T>) component).inject(instance);
        }
        mComponent = component;
        return component;
    }

    public void inject(BaseRequest request) {
        if (mComponent != null) {
            mComponent.requestComponent().inject(request);
        }
    }

    public void inject(SocketManager socketManager) {
        if (mComponent != null) {
            mComponent.requestComponent().inject(socketManager);
        }
    }

    public void inject(RealAccessTokenManager manager) {
        if (mComponent != null) {
            mComponent.inject(manager);
        }
    }

//    public void inject(BaseDBManager dbManager) {
//        if (mComponent != null) {
//            mComponent.requestComponent().inject(dbManager);
//        }
//    }

    public Application getApplication() {
        return mComponent != null ? mComponent.application() : null;
    }

    public <S extends BaseSubcomponent> S getSubcomponent() {
        return mComponent != null? (S) mComponent.subComponent() : null;
    }

    public <C extends CoreComponent> C getComponent() {
        return (C) mComponent;
    }
}
