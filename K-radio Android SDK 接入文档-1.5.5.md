# K-radio Android SDK 接入文档  V1.5.5

* * *

+ [1 概述及名词解释](#1)

  * [1.1 概述](#1.1)
  * [1.2 名词解释](#1.2)

+ [2 接入准备](#2)

  + [2.1 获取AppId、<PERSON><PERSON><PERSON><PERSON>](#2.1)
  + [2.2 环境搭建](#2.2)
  	 * [2.2.1 集成播放器动态库](#2.2.1)
  	 * [<Font color="#ff0000">2.2.2 添加依赖</Font>](#2.2.2)
  + [2.3 配置和权限](#2.3)
  	 * [2.3.1 添加权限](#2.3.1)
  	 * [2.3.2 配置播放器](#2.3.2)
  	 * [2.3.3 其他相关配置](#2.3.3)
  * [2.4 混淆](#2.4)

+ [3 接入详情说明](#3)

  + [3.1 初始化](#3.1)
     * [3.1.1 应用初始化](#3.1.1)
     * [3.1.2 应用激活](#3.1.2)
     * [3.1.3 是否激活](#3.1.3)
     * [3.1.4 初始化并激活](#3.1.4)
  + [3.2 账号](#3.2)
     * [3.2.1 获取听伴登录二维码](#3.2.1)
     * [3.2.2 检查听伴登录二维码状态](#3.2.2)
     * [3.2.3 获取绑定设备的Code值](#3.2.3)
     * [3.2.4 设备与听伴账号绑定](#3.2.4)
     * [3.2.5 获取二维码后自动绑定](#3.2.5)
     * [3.2.6 退出听伴](#3.2.6)
     * [3.2.7 账号管理](#3.2.7)
  + [3.3 运营](#3.3)
     * [3.3.1 获取指定类型的整颗分类树](#3.3.1)
     * [3.3.2 获取指定类型的根分类列表](#3.3.2)
     * [3.3.3 获取指定类型的子分类列表](#3.3.3)
     * [3.3.4 获取分类成员列表](#3.3.4)
     * [3.3.5 获取分类成员数量](#3.3.5)
     * [3.3.6 获取整颗栏目树](#3.3.6)
     * [3.3.7 获取子栏目列表](#3.3.7)
     * [3.3.8 获取栏目成员列表](#3.3.8)
     * [3.3.9 获取多层级的子分类列表](#3.3.9)
  + [3.4 搜索](#3.4)
     * [3.4.1 语义搜索](#3.4.1)
     * [3.4.2 关键词搜索](#3.4.2)
     * [3.4.3 获取联想词](#3.4.3)
     * [3.4.4 获取热词](#3.4.4)
  + [3.5 专辑](#3.5)
     * [3.5.1 获取专辑详情](#3.5.1)
     * [3.5.2 获取专辑的播单列表](#3.5.2)
  + [3.6 智能电台](#3.6)
     * [3.6.1 获取PGC详情](#3.6.1)
     * [3.6.2 获取PGC播单列表](#3.6.2)
     * [3.6.3 根据城市名称或编码获取PGC播单列表](#3.6.3)
  + [3.7 单曲](#3.7)
     * [3.7.1 获取单曲详情](#3.7.1)
     * [3.7.2 获取报时单曲](#3.7.2)
  + [3.8 广播](#3.8)
     * [3.8.1 获取广播电台详情](#3.8.1)
     * [3.8.2 获取广播电台节目单列表](#3.8.2)
     * [3.8.3 获取广播单个节目详情](#3.8.3)
     * [3.8.4 获取广播当前正在播放的节目](#3.8.4)
  + [3.9 直播](#3.9)
     * [3.9.1 获取直播信息](#3.9.1)
     * [3.9.2 使用手机号获取进入聊天室Token](#3.9.2)
     * [3.9.3 使用车厂账号获取进入聊天室token](#3.9.3)
  + [3.10 订阅](#3.10)
     * [3.10.1 获取用户订阅列表](#3.10.1)
     * [3.10.2 订阅](#3.10.2)
     * [3.10.3 取消订阅](#3.10.3)
     * [3.10.4 检查是否订阅](#3.10.4)
  + [3.11 收听历史](#3.11)
     * [3.11.1 获取收听历史列表](#3.11.1)
     * [3.11.2 保存收听历史到服务器](#3.11.2)
     * [3.11.3 清除收听历史](#3.11.3)
  + [3.12 场景推荐](#3.12)
     * [3.12.1 获取场景信息](#3.12.1)
  + [3.13 品牌信息](#3.13)
     * [3.13.1 获取品牌相关信息](#3.13.1)
  
+ [4 播放器说明](#4)

  * [4.1 播放器概述](#4.1)
  * [4.2 播放器状态介绍](#4.2)
  * [4.3 播放器初始化和释放](#4.3)
  * [4.4 通过播放器播放](#4.4)
  * [4.5 播放器控制](#4.5)
  * [4.6 播放器回调](#4.6)
  + [4.7 点播播放器相关数据获取](#4.7)
  	 * [4.7.1 PlayerListManager相关函数说明](#4.7.1)
  	 * [4.7.2 PlayerRadioListManager相关函数说明](#4.7.2)
  	 * [4.7.3 BroadcastRadioListManager相关函数说明](#4.7.3)
  * [4.8 音频焦点](#4.8)
  
+ [5.ErrorCode](#5)

+ [6 Android SDK相关问题反馈](#6)

  * [6.1 关于接口返回图片URL使用说明](#6.1)
  * [6.2 关于专辑、单曲、PGC电台、广播的说明](#6.2)
  * [6.3 关于运营数据的说明](#6.3)
  * [6.4 关于使用<获取整颗栏目树>接口说明](#6.4)
  
+ [7 Model](#7)

  * [7.1 BasePageResult](#7.1)
  * [7.2 Success](#7.2)
  * [7.3 ColumnGrp](#7.3)
  * [7.4 Column](#7.4)
  + [7.5 ColumnMember](#7.5)
  	 * [7.5.1 AlbumDetailColumnMember](#7.5.1)
  	 * [7.5.2 AudioDetailColumnMember](#7.5.2)
  	 * [7.5.3 BroadcastDetailColumnMember](#7.5.3)
  	 * [7.5.4 CategoryColumnMember](#7.5.4)
  	 * [7.5.5 LiveProgramDetailColumnMember](#7.5.5)
  	 * [7.5.6 RadioDetailColumnMember](#7.5.6)
  	 * [7.5.7 SearchResultColumnMember](#7.5.8)
  	 * [7.5.8 WebViewColumnMember](#7.5.9)
  + [7.6 Category](#7.6)
  	 * [7.6.1 LeafCategory](#7.6.1)
  + [7.7 CategoryMember](#7.7)
  	 * [7.7.1 AlbumCategoryMember](#7.7.1)
  	 * [7.7.2 BroadcastCategoryMember](#7.7.2)
  	 * [7.7.3 LiveProgramCategoryMember](#7.7.3)
  	 * [7.7.4 RadioCategoryMember](#7.7.4)
  * [7.8 ImageFile](#7.8)
  * [7.9 VoiceSearchResult](#7.9)
  + [7.10 SearchProgramBean](#7.10)
  	 * [7.10.1 Compere](#7.10.1)
  * [7.11 AlbumDetails](#7.11)
  * [7.12 RadioDetails](#7.12)
  * [7.13 AudioDetails](#7.13)
  * [7.14 Host](#7.14)
  * [7.15 BroadcastDetails](#7.15)
  * [7.16 ProgramDetails](#7.16)
  * [7.17 PlayItem](#7.17)
  * [7.18 UserInfo](#7.18)
  * [7.19 QRCodeInfo](#7.19)
  * [7.20 LiveInfoDetail](#7.20)
  * [7.21 ChatRoomTokenDetail](#7.21)
  * [7.22 ResType](#7.22)
  * [7.23 SubscribeInfo](#7.23)
  + [7.24 SceneInfo](#7.24)
  	 * [7.24.1 AccScene](#7.24.1)
  	 * [7.24.2 SpeedScene](#7.24.2)
  * [7.25 ListeningHistory](#7.25)
  * [7.26 BrandDetails](#7.26)
  * [7.27 SoundQuality](#7.27)
  
+ [8 工具类](#8)

  + [8.1 OperationAssister](#8.1)
     * [8.1.1 获取成员的Id](#8.1.1)
     * [8.1.2 获取收听数](#8.1.2)
     * [8.1.3 获取成员类型](#8.1.3)
     * [8.1.4 获取图标和封面](#8.1.4)
  + [8.2 BeanUtil](#8.2)
     * [8.2.1 单曲转PlayItem](#8.2.1)
  + [8.3 Logging](#8.3)
  
+ [附录](#附录)

  + [更新说明](#1000)

<h1 id="1" name="1">1 概述及名词解释</h1>
--

<h2 id="1.1" name="1.1">1.1 概述 </h2>
####K-radio Andriod SDK 文档面向听伴开放平台开发者。用于指导开发者快速接入听伴开放平台提供的功能服务。包含获取运营位、分类列表、语义搜索、专辑、智能电台，传统广播内容等功能。

<h2 id="1.2" name="1.2">1.2 名词解释</h2>
- **AI电台：** 通过智能化的编排规则，将不同主播的节目串成的一个节目流。AI电台支持个性化，同一个电台不同的用户听到的内容会有差异。K-radio 目前有 150+AI 电台。

- **专辑：** 某一个主播或播客的某一档节目，也可能是一部有声读物。比如《二货一箩筐》。

- **单曲：** 专辑或者AI电台下面的每一条音频。是K-radio内容构成的最小粒度。比如《二货一箩筐》的第一期节目。

- **在线广播：** K-radio整合了数千个传统广播电台，用户可以回听，点播广播节目。也可以广播信号不好的时候，收听网络直播。

示意图如下:
![](http://ww3.sinaimg.cn/large/006tNc79ly1g4sgayl69pj313u080myc.jpg)

<h1 id="2" name="2">2 接入准备</h1>
<h2 id="2.1" name="2.1">2.1 获取AppId、AppKey</h2>
#### 需要获取到K-radio平台的AppId和AppKey，如需要请与产品经理联系。

<h2 id="2.2" name="2.2">2.2 环境搭建</h2>
K-radio SDK 是以远程仓库的方式接入，不支持其他方式(如添加jar包的方式)。   
故支持Android Studio接入，不支持Eclipse接入。

<h3 id="2.2.1">2.2.1 集成播放器动态库</h3>
##### 如需运行在arm平台，则需要将SDK armeabi文件下的`libkaolafmffmpeg.so`、`libkaolafmplayer.so`、`libkaolafmsdl.so`、 `libkaolafmsoundtouch.so`放置项目的==/libs/armeabi==目录下；  

##### 如需运行在x86平台，则需要将SDK x86文件下的`libkaolafmffmpeg.so`、`libkaolafmplayer.so`、`libkaolafmsdl.so`、 `libkaolafmsoundtouch.so`放置项目的==/libs/x86==目录下 

==注意：所使用SDK动态库均不能更改其名称。所需要的SO动态库在提供的Demo的libs目录下，该目录下的wcs-android-sdk-1.6.4.jar是直播用到的库，如果不使用直播功能可以不添加该jar。==

你可能还要在引用的module的build.gradle中添加

```gradle
sourceSets {
    main {
        jniLibs.srcDirs = ['libs']
    }
}
```
放置好后的效果如下:
![](https://upload.cc/i1/2019/12/24/p6UZLK.png)

<h3 id="2.2.2">2.2.2 添加依赖</h3>
#### 在项目根目录的build.gradle中设置中央仓库

```gradle
maven { 
	url "http://pub.nexus.kaolafm.com:8082/repository/github-self/" 
}
```

#### 在需要引入SDK的module目录的build.gradle中引入open-sdk依赖

```gradle
implementation('com.kaolafm:open-sdk:版本号'){
	changing=true
}

```

SDK中使用到了第三方的依赖，如果与接入方的项目的依赖库产生冲突可以使用exclude排除， 比如：

```gradle
implementation('com.kaolafm:open-sdk:版本号'){
        exclude group: 'com.android.volley'
        exclude module: 'gson'
    }

```

<h2 id="2.3">2.3 配置和权限</h2>
##### 播放器核心Service配置 AndroidManifest.xml

<h3 id="2.3.1">2.3.1 添加权限</h3>
``` xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

<h3 id="2.3.2">2.3.2 配置播放器</h3>
``` xml
<service android:name="com.kaolafm.sdk.core.mediaplayer.PlayerService"  />
<service android:name="com.kaolafm.sdk.core.mediaplayer.VLCMediaPlayService"  />

```

<h3 id="2.3.3">2.3.3 其他相关配置</h3>
``` xml      
<meta-data
     android:name="com.kaolafm.open.sdk.AppKey"
     android:value="*****" /> <!--申请开发所需APP_KEY配置-->
<meta-data
	 android:name="com.kaolafm.open.sdk.AppId"
	 android:value="*****" /> <!--申请开发所需APP_ID配置-->
<meta-data
	 android:name="com.kaolafm.open.sdk.Channel"
	 android:value="******" /> <!--开发者所需要发布的渠道名称,例如（"K-radio"）-->
	 
<!-- 选填配置 -->	 
<meta-data
	 android:name="com.kaolafm.open.sdk.CarType"
	 android:value="******" /> <!-- 接入SDK的车型 --> 
```

<h2 id="2.4">2.4 混淆</h2>
```
-dontwarn com.kaolafm.**
-keep class com.kaolafm.** { *; }
-keepclasseswithmembers class tv.danmaku.ijk.media.player.IjkMediaPlayer {
    <fields>;
    <methods>;
}
```

<h1 id="3">3 接入详情说明</h1>
所有的API都已经进行了处理，可以直接在主线程调用。

接口都可以通过绑定生命周期或设置tag取消网络请求。

示例

```java
//第一种方式，绑定生命周期
LoginRequest request = new LoginRequest().bindLifecycle(lifecycleTransformer);
//第二种方式，设置tag
LoginRequest request = new LoginRequest().setTag("LoginRequest");
request.cancel("LoginRequest");
```

<h2 id="3.1">3.1 初始化</h2>
 初始化主要包括sdk的初始化和设备激活。所在类`OpenSDK`。

#### 初始化流程图

```flow
st=>start: 获取AppId和AppKey
en=>operation: 环境搭建
init=>operation: 初始化(每次启动都要初始化)
activite=>operation: 激活(只需初始化一次)
isActivite=>condition: 是否激活
e=>end: 使用接口

st->en->init->isActivite
isActivite(yes)->e
isActivite(no)->activite->e
```
<h3 id="3.1.1">3.1.1 应用初始化</h3>
#### 接口名称

`
OpenSDK#initSDK(Application application,HttpCallback<Boolean> callback)
`

#### 接口说明

用于初始化SDK，在使用SDK接口之前必须调用一次该方法，否则SDK处于未初始化状态，无法使用其他接口。
备注：需要保证public void onSuccess(Boolean isSuccess)  函数返Boolean值为true

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|application|Application|是|应用程序上下文|
|callback|HttpCallback|否|初始化结果回调（失败则返回错误原因，可参见[5.错误代码](#5)）|

#### 代码示例

```java
OpenSDK.getInstance().initSDK(getApplication(), new HttpCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean isSuccess) {
                            showToast(isSuccess ? "初始化SDK成功" : "初始化SDK失败");
                        }

                        @Override
                        public void onError(ApiException exception) {
                            showToast("初始化SDK失败，错误码=" + exception.getCode() + ",错误信息=" + exception.getMessage());
                        }
                    });
```

<h3 id="3.1.2">3.1.2 应用激活</h3>
#### 接口名称

`OpenSDK#activate(HttpCallback<Boolean> callback)` 

#### 接口说明

设备第一次启动应用时需要调用该接口，否则相关鉴权接口无法使用。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|callback|HttpCallback|否|激活结果回调|

#### 代码示例

``` java
OpenSDK.getInstance().activate(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean isSuccess) {
                showToast(isSuccess ? "激活成功" : "激活失败");
            }

            @Override
            public void onError(ApiException exception) {
                showToast("激活失败，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
            }
        });
```

<h3 id="3.1.3">3.1.3 是否激活</h3>
#### 接口名称

`OpenSDK.getInstance().isActivate()`

#### 接口说明

该接口用于判断设置是否已经激活。

#### 返回值说明

返回true表示已经激活；false表示未激活，需要调用激活接口激活。

#### 代码示例

```java
OpenSDK openSDK = OpenSDK.getInstance();
if (!openSDK.isActivate()) {
    openSDK.activate(new HttpCallback<Boolean>() {
        @Override
        public void onSuccess(Boolean aBoolean) {
            String autoId = AccessTokenManager.getInstance().getKaolaAccessToken().getAutoId();
            showToast(aBoolean ? "激活成功=" + autoId : "激活失败");
            isActivated = true;
        }

        @Override
        public void onError(ApiException exception) {
            showToast("激活失败，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
        }
    });
}
```

<h3 id="3.1.4">3.1.4 初始化并激活</h3>
为了简化接入流程，将初始化和激活封装成一个接口。

#### 接口名称

`OpenSDK#initAndActivate(Application application, HttpCallback<Boolean> callback)`

#### 接口说明

该接口合并了初始化和激活接口，接入时，只需要在调用SDK的接口前调用一次该接口就行。应用每次启动都需要调用一次。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|application|Application|是|应用的Application|
|callback|HttpCallback|否|激活结果回调|

#### 代码示例

```java
openSDK.initAndActivate(getApplication(), new HttpCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean aBoolean) {
                            String openId = AccessTokenManager.getInstance().getKaolaAccessToken().getOpenId();
                            showToast(aBoolean ? "初始化、激活成功=" + openId : "初始化、激活失败");
                            isInitialized = aBoolean;
                            isActivated = aBoolean;
                        }

                        @Override
                        public void onError(ApiException exception) {

                        }
                    });
```


<h2 id="3.2">3.2 账号</h2> 
用于实现手机听伴App扫码绑定设备的功能。所在类`LoginRequest`。

<h3 id="001">扫描绑定流程</h3>
 ```flow
st=>start: 登录K-radio账号
end=>end: 绑定成功
getQRCode=>operation: 获取登录二维码
check=>operation: 查询二维码状态
judge=>condition: 判断二维码状态
isBind=>condition: 是否已经绑定
fetchCode=>operation: 获取code值
bind=>operation: 使用code值绑定K-radio
bindUUID=>operation: 使用UUID直接绑定K-radio

st->getQRCode->check->judge
judge(no)->check
judge(yes)->isBind
isBind(no,bottom)->fetchCode->bind->end
isBind(yes)->end
 ```

==注意：为了简化对接流程，在获取二维码后，可以直接调用 [获取二维码后自动绑定](#3.2.5) 接口==

<h3 id="3.2.1">3.2.1 获取听伴登录二维码</h3>
#### 接口名称

`LoginRequest#getQRCode(HttpCallback<QRCodeInfo> callback)`

#### 接口说明

用于获取听伴账号登录的二维码，用手机端听伴App扫描该二维码可以实现登录。
返回数据中包含二维码地址和其他接口需要用到的UUID。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|callback|HttpCallback|否|结果回调，返回[QRCodeInfo](#7.19)对象，其中包含二维码地址|

#### 示例代码

```java
mLoginRequest.getQRCode(new HttpCallback<QRCodeInfo>() {
            @Override
            public void onSuccess(QRCodeInfo qrCodeInfo) {
                mUuid = qrCodeInfo.getUuid();
                Glide.with(KaolaLoginActivity.this)
                        .load(qrCodeInfo.getQRCodePath())
                        .into(mIvKaolaQrCode);
            }

            @Override
            public void onError(ApiException exception) {
                showStatus("获取二维码错误，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
            }
        });
```

<h3 id="3.2.2">3.2.2 检查听伴登录二维码状态</h3>
#### 接口名称

`LoginRequest#checkQRCodeStatus(String uuid, HttpCallback<Integer> callback)`

#### 接口说明

用于检查登录二维码的状态，会返回三个状态：等待扫描、已授权、二维码过期。分别对应`QRCodeInfo.STATUS_NORMAL`、`QRCodeInfo.STATUS_LOSE_EFFICACY`、`QRCodeInfo.STATUS_AUTHORIZATION`

一般是用一个循环不断的请求获取二维码状态。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| uuid |String|是|用户唯一标识，获取二维码时会同时给到该标识。|
|callback|HttpCallback|否|结果回调，返回二维码状态。<br>三个状态：等待扫描、已授权、二维码过期。<br>分别对应<br>`QRCodeInfo.STATUS_NORMAL`、<br/>`QRCodeInfo.STATUS_LOSE_EFFICACY`、<br/>`QRCodeInfo.STATUS_AUTHORIZATION`|

#### 示例代码

```java
if (!TextUtils.isEmpty(mUuid)) {
            mLoginRequest.checkQRCodeStatus(mUuid, new HttpCallback<Integer>() {
                @Override
                public void onSuccess(Integer integer) {
                    switch (integer) {
                        case QRCodeInfo.STATUS_NORMAL:
                            showStatus("等待扫码");
                            break;
                        case QRCodeInfo.STATUS_LOSE_EFFICACY:
                            showStatus("二维码过期. 需要重新请求二维码");
                            dispose();
                            break;
                        case QRCodeInfo.STATUS_AUTHORIZATION:
                            showStatus("已授权");
                            dispose();
                            break;
                        default:
                    }
                }

                @Override
                public void onError(ApiException exception) {

                }
            });
        }
```

<h3 id="3.2.3">3.2.3 获取绑定设备的Code值</h3>
#### 接口名称

`LoginRequest#fetchCode(String uuid, HttpCallback<String> callback)`

#### 接口说明

根据uuid获取用于绑定设备的code。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|uuid|String|是|用户唯一标识，获取二维码时会同时给到该标识。|
|callback|HttpCallback|否|结果回调，返回Code值|

#### 示例代码

```java
if (!TextUtils.isEmpty(mUuid)) {
            mLoginRequest.fetchCode(mUuid, new HttpCallback<String>() {
                @Override
                public void onSuccess(String s) {
                    mCode = s;
                    showStatus("获取code：" + s);
                }

                @Override
                public void onError(ApiException exception) {
                    showStatus("获取二维码错误，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
                }
            });
        } else {
            showToast("uuid为空");
        }

```

<h3 id="3.2.4">3.2.4 设备与听伴账号绑定</h3>
#### 接口名称

通过code将设备与听伴账号绑定。

`LoginRequest#authorizedByCode(String code, HttpCallback<UserInfo> callback)`

直接通过uuid将设备与听伴账号绑定。

`LoginRequest#authorizedByUuid(String uuid, HttpCallback<UserInfo> callback) `

#### 接口说明

可以通过接口`fetchCode()`获取的code进行听伴绑定。

也可以通过获取二维码返回的uuid直接进行听伴绑定，该方法不需要自己手动获取code。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|callback|HttpCallback|否|结果回调，如果已经绑定会返回用户信息[UserInfo](#7.18)，包括用户昵称和头像|
|code|String|是|用于绑定设备的code值，通过fetchCode(String, HttpCallback)获得|
|uuid|String|是|用户唯一标识，获取二维码时会同时给到该标识。|

#### 示例代码

```java
//通过code将设备与听伴账号绑定。
mLoginRequest.authorizedByCode(mCode, new HttpCallback<UserInfo>() {
                @Override
                public void onSuccess(UserInfo userInfo) {
                    Log.e("KaolaLoginActivity", "onSuccess: "+userInfo);
                }

                @Override
                public void onError(ApiException exception) {

                }
            });
//直接通过uuid将设置与听伴账号绑定。
mLoginRequest.authorizedByUuid(mUuid, new HttpCallback<UserInfo>() {
                @Override
                public void onSuccess(UserInfo userInfo) {
                    Log.e("KaolaLoginActivity", "onSuccess: "+userInfo);
                }

                @Override
                public void onError(ApiException exception) {

                }
            });
```

<h3 id="3.2.5">3.2.5 获取二维码后自动绑定</h3>
#### 接口名称

`LoginRequest#loginWhenAuthorized(String uuid, long time, HttpCallback<UserInfo> callback)`

`LoginRequest#loginWhenAuthorized(String uuid, HttpCallback<UserInfo> callback)`

#### 接口说明

当授权成功时，自动进行绑定登录。如果要在二维码过期时会回调onError()。

重载方法 默认1000毫秒轮询一下查询二维码状态。

当二维码过期的时候会在onError()中回调。

注意：使用该接口要在适当的时候(如页面销毁)进行取消操作{@link #cancel(Object)}

#### 参数说明

| 参数名   | 类型         | 是否必须 | 描述                                                         |
| -------- | ------------ | -------- | ------------------------------------------------------------ |
| uuid     | String       | 是       | 用户唯一标识，获取二维码时会同时给到该标识。                 |
| callback | HttpCallback | 否       | 结果回调，绑定成功返回用户信息[UserInfo](#7.18)，包括用户昵称和头像 |

#### 示例代码

```java
mLoginRequest.loginWhenAuthorized(mUuid, new HttpCallback<UserInfo>() {
                @Override
                public void onSuccess(UserInfo userInfo) {
                    Log.e("KaolaLoginActivity", "onSuccess: "+userInfo);
                    dispose();
                }

                @Override
                public void onError(ApiException exception) {
                    Log.e("KaolaLoginActivity", "onError: "+exception);
                }
            });
```

<h3 id="3.2.6">3.2.6 退出听伴</h3>
#### 接口名称

`LoginRequest#logoutTingban(HttpCallback<Boolean> callback)`

#### 接口说明

退出听伴登录，就是将听伴账号与设备进行解绑。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|callback|HttpCallback|否|结果回调，返回true表示解绑成功。|

#### 示例代码

```java
mLoginRequest.logoutTingban(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                Log.e("KaolaLoginActivity", "onSuccess: "+aBoolean);
            }

            @Override
            public void onError(ApiException exception) {

            }
        });
```

<h3 id="3.2.7">3.2.7 账号管理</h3>
该接口用于检查账号登录或退出登录，所在类AccessTokenManager。

检查账号登录  

```java
//true表示听伴账号已经登录，false表示未登录
boolean login = AccessTokenManager.getInstance().getKaolaAccessToken().isLogin();
```
退出登录见[3.2.6](#3.2.6)


<h2 id="3.3">3.3 运营</h2>
该功能分两大类，一个是==分类==，一个是==栏目==。所在类`OperationRequest`。

==注意：==所有运营接口返回的数据中的code只能用于请求运营相关接口，不是节目的Id，不能用于获取节目详细信息或播单，并且==code值不是固定的==。获取节目详情或播单需要使用对应的节目Id，如[AlbumDetailColumnMember](#7.5.1)中的albumId，可以使用工具类[OperationAssister](#8.1)获取Id。

* <h4 id="3.3.0">整体框架图</h4>

![](http://ww1.sinaimg.cn/large/006tNc79ly1g4s6szyx5cj30t20hnn15.jpg)

<h3 id="3.3.1">3.3.1 获取指定类型的整颗分类树</h3>
#### 接口名称

全参数接口   

`OperationRequest#getCategoryTree(int contentType, String zone, Map<String, String> extInfo,
            HttpCallback<List<Category>> callback)`   
​            
重载接口，只有必填参数

`OperationRequest#getCategoryTree(int contentType, HttpCallback<List<Category>> callback)`

重载接口

`OperationRequest#getCategoryTree(int contentType,  String zone, HttpCallback<List<Category>> callback)`

#### 接口说明

获取某一内容类型的整颗分类树。该接口获取的分类树包括根分类及其子分类，不包括分类成员。分类([Category](#7.6))下才会有子分类；叶子分类([LeafCategory](#7.6.1))下才会有分类成员([CategoryMember](#7.7))。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| contentType |int|是|要获取分类树的内容类型[ResType](#7.22)，<br>专辑-`ResType.TYPE_ALBUM`<br>广播-`ResType.TYPE_BROADCAST`<br>直播-`ResType.TYPE_LIVE`<br>PGC-`ResType.TYPE_RADIO`<br>==目前仅支持上述4种内容类型==|
| zone |String|否|额外信息，暂时无用|
| extraInfo |Map&lt;String,String&gt;|否|额外信息，暂时无用|
|callback|HttpCallback|否|结果回调，成功返回[Category](#7.6)对象的List集合|

#### 代码示例

```java
new OperationRequest().getCategoryTree(contentType, zone, hashMap, new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                if (!ListUtil.isEmpty(categories)) {
                    if (!TextUtils.equals("0", mParentCode)) {
                        categories = categories.get(0).getChildCategories();
                    }
                }else {
                    showToast("数据为空");
                }
                mCategoryAdapter.setDataList(categories);
                mTrfCategoryRefresh.finishLoadmore();
                mTrfCategoryRefresh.finishRefreshing();
            }

            @Override
            public void onError(ApiException exception) {
                showToast("网络请求错误，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
                if (mTrfCategoryRefresh != null) {
                    mTrfCategoryRefresh.finishLoadmore();
                    mTrfCategoryRefresh.finishRefreshing();
                }
            }
        });
```

#### 接口使用说明

根据 [整体框架图](#3.3.0)，使用```getCategoryTree```接口，返回的数据包含： A1、B1、B2、C1、C2、C3、C4、C5、C6。

<h3 id="3.3.2">3.3.2 获取指定类型的根分类列表</h3>
#### 接口名称

全参数接口   
`OperationRequest#getCategoryRoot(int contentType, String zone, Map<String, String> extInfo, HttpCallback<List<Category>> callback)`

重载接口
`OperationRequest#getCategoryRoot(int contentType, String zone, HttpCallback<List<Category>> callback)`

重载接口
`OperationRequest#getCategoryRoot(int contentType, HttpCallback<List<Category>> callback)`



#### 接口说明

获取某一内容类型的根分类列表，只获取最上层的的分类。分类([Category](#7.6))下才会有子分类；叶子分类([LeafCategory](#7.6.1))下才会有分类成员([CategoryMember](#7.7))

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|contentType|int|是|要获取分类树的内容类型[ResType](#7.22)，包括<br/>专辑-`ResType.TYPE_ALBUM`<br/>广播-`ResType.TYPE_BROADCAST`<br/>直播-`ResType.TYPE_LIVE`<br/>PGC-`ResType.TYPE_RADIO`<br>==目前仅支持上述4种内容类型==|
|zone|String|否|额外信息，暂时无用|
|extraInfo|Map&lt;String, String&gt;|否|额外信息，暂时无用|
|callback|HttpCallback|否|结果回调，<br>成功返回[Category](#7.6)对象的List集合|

#### 代码示例

```java
new OperationRequest().getCategoryRoot(contentType, zone, hashMap, new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                mCategoryAdapter.setDataList(categories);
                if (categories == null || categories.isEmpty()) {
                    showToast("数据为空");
                }
            }

            @Override
            public void onError(ApiException exception) {
                showToast("网络请求错误，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
            }
        });
```

#### 接口使用说明

根据 [整体框架图](#3.3.0)，使用```getCategoryRoot```接口，返回的数据包含：A1、或A1同级别的数据。

<h3 id="3.3.3">3.3.3 获取指定类型的子分类列表</h3>
#### 接口名称

`OperationRequest#getSubcategoryList(String parentCode, Map<String, String> extInfo, HttpCallback<List<LeafCategory>> callback)`

重载接口

`OperationRequest#getSubcategoryList(String parentCode, HttpCallback<List<LeafCategory>> callback)`

#### 接口说明

根据分类code获取分类信息

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| parentCode |String|是|需要获取子分类的code|
|callback|HttpCallback|否|结果回调，成功返回[LeafCategory](#7.6.1)对象List集合|

#### 代码示例

```java
new OperationRequest().getSubcategoryList(mParentCode, null, new HttpCallback<List<LeafCategory>>() {
            @Override
            public void onSuccess(List<LeafCategory> leafCategories) {
                if (!ListUtil.isEmpty(leafCategories)) {
                    mCategoryAdapter.setDataList(leafCategories);
                    showToast("显示子分类");
                    setTitle("子分类列表");
                }else {
                    showToast(mContentName + "的子分类为空");
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取子分类错误", exception);
            }
        });
```
#### 接口使用说明

根据 [整体框架图](#3.3.0)，使用```getSubcategoryList```接口，返回的数据包含：

* 如果参数parentCode为（A1的Code），那么得到的数据是B1、B2。
* 如果参数parentCode为（B1的Code），那么得到的数据为C1、C2、C3。
* 如果参数parentCode为（C1的Code），那么得到的数据为空。

<h3 id="3.3.4">3.3.4 获取分类成员列表</h3>
#### 接口名称

分页获取   
`OperationRequest#getCategoryMemberList(String code, int pageNum, int pageSize, HttpCallback<BasePageResult<List<CategoryMember>>> callback)`

#### 接口说明

根据分类的code获取分类成员列表，需要分页获取。只有当[Category](#7.6) instanceOf [LeafCategory](#7.6.1)的才有分类成员数据。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| code |String|是|分类的code|
| pageNum |int|否|请求页码 1，2，3，...|
| pageSize |int|是|每页个数|
|callback|HttpCallback|否|结果回调，成功返回[CategoryMember](#7.7)对象的List集合|

#### 代码示例

```java
new OperationRequest().getCategoryMemberList(mCode, 20，1，new HttpCallback<List<CategoryMember>>() {
            @Override
            public void onSuccess(List<CategoryMember> categoryMembers) {
                if (categoryMembers != null && !categoryMembers.isEmpty()) {
					mCategoryMemberAdapter.setDataList(categoryMembers);
                } else {
                    showToast("数据为空");
                }
            }

            @Override
            public void onError(ApiException exception) {
                showToast("网络请求错误， 错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
            }
        });
```

#### 接口使用说明

根据 [整体框架图](#3.3.0). 使用```getCategoryMemberList```接口, 返回的数据包含: 

* 如果参数code 为（C1的code），那么获取的数据为 D1、D2。
* 如果参数code 为（C2的code），那么获取的数据为 D3、D4。

<h3 id="3.3.5">3.3.5 获取分类成员数量</h3>
#### 接口名称

根据分类编码获取分类成员数量  

`OperationRequest#getCategoryMemberNum(String code, Map<String, String> extInfo, HttpCallback<Integer> callback)`

重载接口

`OperationRequest#getCategoryMemberNum(String code, HttpCallback<Integer> callback)`

#### 接口说明

根据分类的code获取分类成员数量。

#### 参数说明

| 参数名   | 类型         | 是否必须 | 描述                           |
| :------- | :----------- | :------- | :----------------------------- |
| code     | String       | 是       | 分类的code                     |
| callback | HttpCallback | 否       | 结果回调，成功返回分类成员个数 |

#### 代码示例

```java
operationRequest.getCategoryMemberNum(mCode, new HttpCallback<Integer>() {
            @Override
            public void onSuccess(Integer integer) {
                mTvCategoryMemberNum.setText(integer + "个分类成员");
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取分类成员个数错误", exception);
            }
        });
```

#### 接口使用说明

根据 [整体框架图](#3.3.0)，使用```getCategoryMemberNum```接口，返回的数据包含：

* 如果参数code为（C1的code），返回值为2。

<h3 id="3.3.6">3.3.6 获取整颗栏目树</h3>
#### 接口名称

获取整个栏目树，包括根栏目和子栏目
`OperationRequest#getColumnTree(boolean isWithMembers, String zone, Map<String, String> extInfo,
HttpCallback<List<ColumnGrp>> callback)`

重载接口

`OperationRequest#getColumnTree(boolean isWithMembers, String zone, HttpCallback<List<ColumnGrp>> callback)`

重载接口

`OperationRequest#getColumnTree(boolean isWithMembers, HttpCallback<List<ColumnGrp>> callback)`

重载接口，不包含栏目成员

`OperationRequest#getColumnTree(HttpCallback<List<ColumnGrp>> callback)`

#### 接口说明

获取整个栏目树，包括根栏目和子栏目。可以获取包含栏目成员的栏目树，也可以获取不包含栏目树栏目成员。

#### 参数说明

| 参数名        | 类型                      | 是否必须 | 描述                                                  |
| :------------ | :------------------------ | :------- | :---------------------------------------------------- |
| isWithMembers | Boolean                   | 是       | 是否要成员(false表示不要成员)，不填表示不获取成员     |
| zone          | String                    | 否       | 额外信息，暂时无用|
| extraInfo     | Map&lt;String, String&gt; | 否       | 额外信息，暂时无用                                    |
| callback      | HttpCallback              | 否       | 结果回调，成功返回栏目组[ColunmGrp](#7.3)的List集合。 |

#### 代码示例

```java
//获取栏目组的树结构，包括所有根栏目和子栏目
boolean isWithMember = mSwitchWithMember.isChecked();
mOperationRequest.getColumnTree(isWithMember, zone, new HttpCallback<List<ColumnGrp>>(){
            @Override
            public void onSuccess(List<ColumnGrp> columnGrps) {
                setTitle("根栏目组列表");
                if (columnGrps != null && columnGrps.size() > 0) {
                    mColumnGrpAdapter.setDataList(columnGrps);
                } else {
                    showToast("栏目数为空");
                }
            }

            @Override
            public void onError(ApiException exception) {
                showToast("网络请求错误，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());

            }
        });
```

#### 接口使用说明

根据 [整体框架图](#3.3.0)，使用```getColumnTree```接口，返回的数据包含：

* 如果传入的isWithMembers为false，那么获取到的数据为：A1、B1、B2、C1、C2、C3、C4、C5、C6。
* 如果传入的isWithMembers为true, 那么获取到的数据为: A1、B1、B2、C1、C2、C3、C4、C5、C6、D1、D2、D3、D4、D5、D6、D7、D8、D9、D10、D11、D12。

<h3 id="3.3.7">3.3.7 获取子栏目列表</h3>
#### 接口名称

根据父栏目编码获取下级栏目列表
`OperationRequest#getSubcolumnList(String parentCode, String zone, Map<String, String> extInfo,
HttpCallback<List<ColumnGrp>> callback)`

重载接口

`OperationRequest#getSubcolumnList(String parentCode, String zone,HttpCallback<List<ColumnGrp>> callback)`

重载接口

`OperationRequest#getSubcolumnList(String parentCode, HttpCallback<List<ColumnGrp>> callback)`

#### 接口说明

根据父栏目编码获取下级栏目列表，该接口只能获取下一级的栏目列表，不包含其下的栏目成员。

#### 参数说明

| 参数名     | 类型                      | 是否必须 | 描述                                              |
| :--------- | :------------------------ | :------- | :------------------------------------------------ |
| parentCode | String                    | 是       | 父栏目编码                                        |
| zone       | String                    | 否       | 额外信息，暂时无用                                                                        |
| extraInfo  | Map&lt;String, String&gt; | 否       | 额外信息，暂时无用                                |
| callback   | HttpCallback              | 否       | 结果回调，成功栏目组[ColunmGrp](#7.3)的List集合。 |

#### 代码示例

```java
mOperationRequest.getSubcolumnList(mCode, new HttpCallback<List<Column>>() {
                    @Override
                    public void onSuccess(List<Column> columns) {
                        setTitle("子栏目列表");
                        if (!ListUtil.isEmpty(columns)) {
                            mColumnGrpAdapter.setDataList(columns);
                        } else {
                            showToast("子栏目为空");
                        }
                    }

                    @Override
                    public void onError(ApiException exception) {
                        showError("获取子栏目错误", exception);
                    }
                });
```

#### 接口使用说明

根据 [整体框架图](#3.3.0)，使用```getSubcolumnList```接口，返回的数据包含：

*  如果参数parentCode为（A1的Code），那么得到的数据是B1、B2。
*  如果参数parentCode为（B1的Code），那么得到的数据为C1、C2、C3。
*  如果参数parentCode为（C1的Code），那么得到的数据为空。

<h3 id="3.3.8">3.3.8 获取栏目成员列表</h3>
#### 接口名称

根据栏目编码获取栏目成员列表

`OperationRequest#getColumnMemberList(String code, Map<String, String> extInfo,
HttpCallback<List<ColumnMember>> callback)`

重载接口

`OperationRequest#getColumnMemberList(String code, HttpCallback<List<ColumnMember>> callback)`

#### 接口说明

根据栏目编码获取栏目成员列表。

#### 参数说明

| 参数名    | 类型                      | 是否必须 | 描述                                                 |
| :-------- | :------------------------ | :------- | :--------------------------------------------------- |
| code      | String                    | 是       | 栏目编码                                             |
| zone      | String                    | 否       | 额外信息，暂时无用                                                                             |
| extraInfo | Map&lt;String, String&gt; | 否       | 额外信息，暂时无用                                   |
| callback  | HttpCallback              | 否       | 结果回调，成功栏目组[ColunmMember](#7.5)的List集合。 |

#### 代码示例

```java
mOperationRequest.getColumnMemberList(mCode, new HttpCallback<List<ColumnMember>>() {
                    @Override
                    public void onSuccess(List<ColumnMember> columnMembers) {
                        setTitle("栏目成员列表");
                        if (!ListUtil.isEmpty(columnMembers)) {
                            mColumnGrpAdapter.setDataList(columnMembers);
                        } else {
                            showToast("栏目成员为空");
                        }
                    }

                    @Override
                    public void onError(ApiException exception) {
                        showError("获取栏目成员错误", exception);
                    }
                });
```

#### 接口使用说明

根据 [整体框架图](#3.3.0)，使用```getColumnMemberList```接口，返回的数据包含：
* 如果参数code 为（C1的code），那么获取的数据为 D1、D2。
* 如果参数code 为（C2的code），那么获取的数据为 D3、D4。

<h3 id="3.3.9">3.3.9 获取多层级的子分类列表</h3>
#### 接口名称

`OperationRequest#getSubcategoryListForMoreLevels(String parentCode, Map<String, String> extInfo, HttpCallback<List<Category>> callback)`

#### 接口说明

获取某一内容类型的子分类列表。如果有更多层级，子分类可能包含Category或Category与LeafCategory同时存在情况。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|parentCode|String|是|分类父编码|
|extInfo|Map&lt;String, String&gt; | 否 | 额外信息，暂时无用|
|callback|HttpCallback|否|回调 返回子分类[Category](#7.6)列表|

#### 代码示例

```java
mOperationRequest.getSubcategoryListForMoreLevels(mParentCode, null, new HttpCallback<List<Category>>() {
            @Override
            public void onSuccess(List<Category> categories) {
                if (!ListUtil.isEmpty(categories)) {
                    mCategoryAdapter.setDataList(categories);
                    showToast("显示子分类");
                    setTitle("子分类列表");
                }else {
                    showToast(mContentName + "的子分类为空");
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取子分类错误", exception);
            }
        });
```

<h2 id="3.4">3.4 搜索</h2>
所在类`SearchRequest`。

<h3 id="3.4.1">3.4.1 语义搜索</h3>
根据语音的关键词进行搜索。

#### 接口名称

`SearchRequest#searchBySemantics(String voiceSource,
​            int qualityType,
​            String origJson,
​            int field,
​            int tag,
​            String artist,
​            String audioName,
​            String albumName,
​            String category,
​            String keyword,
​            String text,
​            String language,
​            String freq,
​            String area,
​            HttpCallback<VoiceSearchResult> callback)
`

重载接口，去除暂不支持的参数

`
SearchRequest#searchBySemantics(String voiceSource,
​            int qualityType,
​            String origJson,
​            int field,
​            int tag,
​            String artist,
​            String audioName,
​            String albumName,
​            String category,
​            String keyword,
​            String text,
​            HttpCallback<VoiceSearchResult> callback)
`

#### 接口说明

根据语音商和语音上解析后的json串可以获取不同类型的音频资源。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|voiceSource|String|是|语音来源 公司标识。<br>同行者:txzing;思必驰:sibichi;问问:wenwen;<br>蓦然:moran;科大 讯飞:kedaxunfei;|
| qualityType |int|否|音频质量要求,0:低;1:高;|
| origJson |String|否|语音商解析后返回的原始json串|
| field |int|是|场景类别 1：音乐，2：综合; 6: 传统广播|
| tag |int|否|参数可信标识 0：不可信，1：可信；<br>为1时，表示场景分类和其他字段等信息可信度高。|
| artist |String|否|艺术家|
|audioName|String|否|音频名称|
|albumName|String|否|专辑名称|
| category |String|否|分类|
|keyword|String|是|关键词 多个关键词以英文逗号“,”分隔|
|text|String|是|用户声控的原始串|
|language|String|否|语言，暂不支持|
|freq|String|否|电台频率，传统广播场景下使用，暂不支持|
|area|String|否|搜索text中的地点，传统广播场景下使用，暂不支持|
|callback|HttpCallback|否|结果回调，成功返回[VoiceSearchResult](#7.9)对象|

#### 代码示例

```java
String mVoiceSource = "kedaxunfei";
int mVoiceQuality = 1;
String mOriginJson = "
{
    "save_history":true,
    "rc":3,
    "semantic":[
        {
            "intent":"PLAY",
            "slots":[
                {
                    "name":"lang",
                    "value":"粤语"
                }
            ]
        }
    ],
    "service":"musicX",
    "uuid":"atn0210245b@dx00070ecedaf7a11001",
    "text":"我要听粤语",
    "state":{
        "fg::musicX::default::default":{
            "lang":"1",
            "state":"default"
        }
    },
    "used_state":{
        "lang":"1",
        "state_key":"fg::musicX::default::default",
        "state":"default"
    },
    "answer":{
        "text":"可能刚刚开小差了，我去瞧一瞧"
    },
    "dialog_stat":"dataInvalid",
    "sid":"atn0210245b@dx00070ecedaf7a11001"
}";
int mField  = 1;
int mCredibility = 1;
String mArtist = null;
String mAudioName = null;
String mAlbumName = null;
String mCategory = "音乐";
String mKeyword = "粤语";
String mVoiceText = "我要听粤语";

new SearchRequest().searchBySemantics(mVoiceSource, mVoiceQuality, mOriginJson, mField,
                                mCredibility, mArtist, mAudioName, mAlbumName, mCategory, mKeyword, mVoiceText, null,
                                null, null,
                                new HttpCallback<VoiceSearchResult>() {
                                    @Override
                                    public void onSuccess(VoiceSearchResult voiceSearchResult) {
                                        List<SearchProgramBean> programList = voiceSearchResult.getProgramList();
                                        if (programList != null && programList.size() > 0) {
                                            mSearchResultNewAdapter.setDataList(programList);
                                        } else {
                                            showToast("数据为空");
                                        }
                                    }

                                    @Override
                                    public void onError(ApiException exception) {
                                        showToast("网络请求错误，错误码=" + exception.getCode() + ", 错误信息=" + exception
                                                .getMessage());
                                    }
                                });
```

<h3 id="3.4.2">3.4.2 关键词搜索</h3>
#### 接口名称

`SearchRequest#searchAll(String keyword, HttpCallback<List<SearchProgramBean>> callback)`

#### 接口说明

根据关键词搜索所有资源。

#### 参数说明

| 参数名   | 类型| 是否必须 | 描述                                                     |
| :----| :---- | :------- | :---- |
| keyword  | String       | 是       | 关键词                                                   |
| callback | HttpCallback | 否       | 结果回调，成功返回[SearchProgramBean](#7.10)对象List列表 |

#### 代码示例

```java
new SearchRequest().searchAll(keyword, new HttpCallback<List<SearchProgramBean>>() {
            @Override
            public void onSuccess(List<SearchProgramBean> searchProgramBeans) {
                mRvSearchSuggestedWord.setAdapter(mSearchAdapter);
                mSearchAdapter.setDataList(searchProgramBeans);
            }

            @Override
            public void onError(ApiException exception) {
                showError("搜索所有资源错误", exception);
            }
        });
```

<h3 id="3.4.3">3.4.3 获取联想词</h3>
#### 接口名称

`SearchRequest#getSuggestedWords(String word, HttpCallback<List<String>> callback)`

#### 接口说明

根据给定的词获取联想词。

#### 参数说明

| 参数名   | 类型         | 是否必须 | 描述                               |
| :------- | :----------- | :------- | :--------|
| word     | String       | 是       | 关键词                             |
| callback | HttpCallback | 否       | 结果回调，成功返回联想词的List列表 |

#### 代码示例

```java
new SearchRequest().getSuggestedWords(keyword, new HttpCallback<List<String>>() {
    @Override
    public void onSuccess(List<String> strings) {
    	mRvSearchSuggestedWord.setAdapter(mKeywordAdapter);
      if (mKeywordAdapter != null) {
      	mKeywordAdapter.setDataList(strings);
      }
    }
		@Override
		public void onError(ApiException exception) {
			showError("搜索联想词错误", exception);
		}
});
```

<h3 id="3.4.4">获取热词</h3>
#### 接口名称

`SearchRequest#getHotWords(HttpCallback<List<String>> callback)`

#### 接口说明

获取热门搜索词列表

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|callback|HttpCallback|否|结果回调，成功返回String列表|

#### 代码示例

```java
new SearchRequest().getHotWords(new HttpCallback<List<String>>() {
            @Override
            public void onSuccess(List<String> strings) {
                mRvSearchSuggestedWord.setAdapter(mKeywordAdapter);
                if (mKeywordAdapter != null) {
                    mKeywordAdapter.setDataList(strings);
                }
            }

            @Override
            public void onError(ApiException exception) {

            }
        });
```

<h2 id="3.5">3.5 专辑</h2>
包括获取获取专辑详情和专辑的播单列表功能。专辑相关请求所在类`AlbumRequest`。

<h3 id="3.5.1">3.5.1 获取专辑详情</h3>
#### 接口名称

获取单个专辑详情

`AlbumRequest#getAlbumDetails(long albumId, HttpCallback<AlbumDetails> callback)`

一次获取多个专辑详情

`AlbumRequest#getAlbumDetails(long[] albumIds, HttpCallback<AlbumDetails> callback)`

#### 接口说明

根据专辑Id获取专辑的信息详情。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|albumId|long|是|专辑Id|
| albumIds|Long[]|是|专辑id数组|
|callback|HttpCallback|否|结果回调，成功返回[AlbumDetails](#7.11)对象或列表|

#### 代码示例

```java
//获取单个专辑详情
new AlbumRequest().getAlbumDetails(albumId, new HttpCallback<AlbumDetails>() {
            @Override
            public void onSuccess(AlbumDetails result) {
                tvDetails.setText(mGson.toJson(result));
                Glide.with(DetailActivity.this).load(result.getImg()).into(ivCover);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
//一次获取多个专辑详情
new AlbumRequest().getAlbumDetails(new Long[]{1100000000078L, 1100000000416L}, new HttpCallback<List<AlbumDetails>>() {
            @Override
            public void onSuccess(List<AlbumDetails> result) {
                tvDetails.setText(mGson.toJson(result));
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

<h3 id="3.5.2">3.5.2 获取专辑的播单列表</h3>
#### 接口名称

`AlbumRequest#getPlaylist(long albumId, long audioId, @Sort int sort, int pageSize, int pageNum,
​            HttpCallback<BasePageResult<List<AudioDetails>>> callback)`
重载方法。单曲id默认为0
`AlbumRequest#getPlaylist(long albumId, @Sort int sort, int pageSize, int pageNum,
​            HttpCallback<BasePageResult<List<AudioDetails>>> callback) `


#### 接口说明

根据专辑id获取专辑的播单列表，分页请求；可以根据期数倒序或者正序，也可以根据单曲的id直接返回改单曲所在页的单曲列表。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| albumId |long|是|专辑id|
| audioId |long|否|单曲id，根据单曲id定位分页|
| sort |int|否|1正序 -1 倒序|
| pageSize |int|否|每页专辑个数，默认是10|
| pageNum |int|否|请求页码 1, 2, 3...，默认是1|
|callback|HttpCallback|否|结果回调，成功返回[AudioDetails](#7.13)列表|

#### 代码示例

```java
new AlbumRequest().getPlaylist(id, AlbumRequest.SORT_ACS, 20, 1, new HttpCallback<BasePageResult<List<AudioDetails>>>() {
            @Override
            public void onSuccess(BasePageResult<List<AudioDetails>> result) {
                mAdapter.replaceData(result);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

<h2 id="3.6">3.6 智能电台</h2>
智能电台又称PGC。接口所在类`RadioRequest`

<h3 id="3.6.1">3.6.1 获取PGC详情</h3>
#### 接口名称

`RadioRequest#getRadioDetails(long radioId, HttpCallback<RadioDetails> callback)`

#### 接口说明

根据智能电台的id获取信息详情。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| radioId |long|是|智能电台Id|
|callback|HttpCallback|否|结果回调，成功返回[RadioDetails](#7.12)对象|

#### 代码示例

```java
new RadioRequest().getRadioDetails(id, new HttpCallback<RadioDetails>() {
            @Override
            public void onSuccess(RadioDetails result) {
                tvDetails.setText(mGson.toJson(result));
                Glide.with(DetailActivity.this).load(result.getImg()).into(ivCover);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

<h3 id="3.6.2">3.6.2 获取PGC播单列表</h3>
#### 接口名称

`RadioRequest#getPlaylist(long radioId, String clockId, HttpCallback<List<AudioDetails>> callback)`

#### 接口说明

根据智能电台id获取播单列表

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| radioId |long|是|智能电台id|
| clockId |String|是|上一次请求返回的clockId，第一次请求为空，第二次请求必填|
|callback|HttpCallback|否|结果回调，成功返回[AudioDetails](#7.13)集合|

#### 代码示例

```java
String clockId = "";
new RadioRequest().getPlaylist(radioId, clockId, new HttpCallback<List<AudioDetails>>() {
            @Override
            public void onSuccess(List<AudioDetails> result) {
                mAdapter.replaceData(result);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

<h3 id="3.6.3">3.6.3 根据城市名称或编码获取PGC播单列表</h3>
#### 接口名称

`RadioRequest#getPlaylist(long radioId, String clockId, String areaCode, String cityName,
​            HttpCallback<List<AudioDetails>> callback)`

#### 接口说明

根据城市的名称或城市地区编码(见附件)，获取智能电台的播单列表。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:----|:----|:----|
|radioId|long|是|智能电台Id|
|clockId|String|是|上一次请求返回的clockId，第二次请求必填|
|areaCode|String|否|城市编码|
|cityName|String|否|城市名称|
|callback|HttpCallback&lt;List&lt;AudioDetails&gt;&gt;|否|接口回调，成功返回单曲[AudioDetails](#7.13)集合|

#### 代码示例

```java
new RadioRequest().getPlaylist(radioId, clockId, null, "北京市",new HttpCallback<List<AudioDetails>>() {
            @Override
            public void onSuccess(List<AudioDetails> result) {
                List<StringAdapter.Item> datas = new ArrayList<>();

                if (result != null) {
                    List<AudioDetails> dataList = result;
                    if (dataList != null) {
                        for (int i = 0; i < dataList.size(); i++) {
                            AudioDetails item = dataList.get(i);

                            StringAdapter.Item sai = new StringAdapter.Item();
                            sai.id = item.getAudioId();
                            sai.type = TYPE_AUDIO;
                            sai.title = item.getAudioName();
                            sai.details = item.getAlbumName();//mGson.toJson(item);

                            datas.add(sai);
                        }
                    }
                }

                if (datas.isEmpty()) {
                    Toast.makeText(DetailActivity.this, "列表为空", Toast.LENGTH_SHORT).show();
                }

                mAdapter.replaceData(datas);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

<h2 id="3.7">3.7 单曲</h2>
单曲相关接口所在类`AudioRequest`。

<h3 id="3.7.1">3.7.1 获取单曲详情</h3>
#### 接口名称

获取单个单曲的详情

`AudioRequest#getAudioDetails(long audioId, HttpCallback<AudioDetails> callback)`

一次获取多个单曲的详情

`AudioRequest#getAudioDetails(Long[] audioIds, HttpCallback<List<AudioDetails>> callback)`

#### 接口说明

获取一个或多个单曲的信息详情。

注：==接口不支持19位的单曲Id查询。目前台宣和歌曲的单曲是19位(单曲所在集合类型可以通过contentType区分)。如果要查询台宣或歌曲的详情需要去除Id的后6位，得到的13位Id进行查询，可能会出现单曲数量减少，因为去除后6后的Id可能出现重复的id，如台宣，一个电台的台宣一般只有一个音频，为了一个音频在同个电台中多次使用进行区分，所以在id后面添加了6位。==

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|audioId|long|是|单曲id|
|audioIds|Long[]|是|13位的单曲id的Long数组|
|callback|HttpCallback|否|结果回调，成功返回[AudioDetails](#7.13)对象或列表|

#### 代码示例

```java
//获取单个单曲详情
new AudioRequest().getAudioDetails(audioId, new HttpCallback<AudioDetails>() {
            @Override
            public void onSuccess(AudioDetails result) {
                tvDetails.setText(mGson.toJson(result));
                Glide.with(DetailActivity.this).load(result.getAudioPic()).into(ivCover);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
//获取多个单曲详情
Long[] audioIds = new Long[]{1000000394424L, 1000000394424L};
new AudioRequest().getAudioDetails(audioIds, new HttpCallback<List<AudioDetails>>() {
            @Override
            public void onSuccess(List<AudioDetails> audioDetails) {
                tvDetails.setText(mGson.toJson(audioDetails));
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

<h3 id="3.7.2">3.7.2 获取报时单曲</h3>
#### 接口名称

`AudioRequest#getCurrentClockAudio(HttpCallback<AudioDetails> callback)`

### 接口说明

获取当前时间点的报时声音单曲。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|callback|HttpCallback|否|结果回调，成功返回[AudioDetails](#7.13)对象|

#### 代码示例

```java
new AudioRequest().getCurrentClockAudio(new HttpCallback<AudioDetails>() {
            @Override
            public void onSuccess(AudioDetails audioDetails) {
                Log.e("DetailActivity", "onSuccess: "+audioDetails);
            }

            @Override
            public void onError(ApiException exception) {

            }
        });
```

<h2 id="3.8">3.8 广播</h2>
广播相关的接口，所在类`BroadcastRequest`

<h3 id="3.8.1">3.8.1 获取广播电台详情</h3>
#### 接口名称

`BroadcastRequest#getBroadcastDetails(long broadcastId, HttpCallback<BroadcastDetails> callback)`

#### 接口说明

根据广播id获取广播的信息详情

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|broadcastId|long|是|广播电台的id|
|callback|HttpCallback|否|结果回调，成功返回[BroadcastDetails](#7.15)对象或列表|

#### 代码示例

```java
new BroadcastRequest().getBroadcastDetails(id, new HttpCallback<BroadcastDetails>() {
            @Override
            public void onSuccess(BroadcastDetails result) {
                tvDetails.setText(mGson.toJson(result));
                Glide.with(DetailActivity.this).load(result.getImg()).into(ivCover);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

<h3 id="3.8.2">3.8.2 获取广播电台节目单列表</h3>
#### 接口名称

`BroadcastRequest#getBroadcastProgramList(long broadcastId, @Nullable String date, HttpCallback<List<ProgramDetails>> callback)`

#### 接口说明

获取指定广播电台的指定日期的节目单列表

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| broadcastId |long|是|广播电台id|
| date |String|否|节目单的日期，默认是当天。格式“2018-07-26”|
|callback|HttpCallback|否|结果回调，成功返回[ProgramDetails](#7.16)列表|

#### 代码示例

```java
String date = "2018-07-26";
new BroadcastRequest().getBroadcastProgramList(id, date, new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> result) {
                mAdapter.replaceData(result);
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

<h3 id="3.8.3">3.8.3 获取广播单个节目详情</h3>
#### 接口名称

`BroadcastRequest#getBroadcastProgramDetails(long programId, HttpCallback<ProgramDetails> callback)`

#### 接口说明

获取广播电台的某个节目的信息详情。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
|programId|long|是|广播节目Id|
|callback|HttpCallback|否|结果回调，成功返回[ProgramDetails](#7.16)对象或列表|

#### 代码示例

```java
long programId = 19196008;
new BroadcastRequest().getBroadcastProgramDetails(programId, new HttpCallback<ProgramDetails>() {

            @Override
            public void onSuccess(ProgramDetails programDetails) {
                tvDetails.setText(mGson.toJson(programDetails));
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

<h3 id="3.8.4">3.8.4 获取广播当前正在播放的节目</h3>
#### 接口名称

`BroadcastRequest#getBroadcastCurrentProgramDetails(long broadcastId, HttpCallback<ProgramDetails> callback)`

#### 接口说明

获取某个广播电台正在播放的节目信息。

#### 参数说明

|参数名|类型|是否必须|描述|
|:---|:---|:---|:---|
| broadcastId |long|是|广播电台Id|
|callback|HttpCallback|否|结果回调，成功返回[ProgramDetails](#7.16)对象或列表|

#### 代码示例

```java
long id = 1600000000198L;
new BroadcastRequest().getBroadcastCurrentProgramDetails(id, new HttpCallback<ProgramDetails>() {
            @Override
            public void onSuccess(ProgramDetails programDetails) {
                tvDetails.setText(mGson.toJson(programDetails));
            }

            @Override
            public void onError(ApiException exception) {
                tvDetails.setText(mGson.toJson(exception));
            }
        });
```

<h2 id="3.9">3.9 直播</h2>
<h3 id="3.9.1">3.9.1 获取直播信息</h3>
#### 接口名称

`
LiveRequest#getLiveInfo(String id, HttpCallback<LiveInfo> callback)
`
#### 接口说明

根据节目ID获取直播具体信息

#### 参数说明

| 参数名| 类型 | 是否必须 | 描述 |
| :------| :------| :------| :-----|
| id | String | 是 | 直播id |
| callback | HttpCallback | 是 | 结果回调，返回直播信息[LiveInfoDetail](#7.20) |

#### 代码示例

```java
LiveRequest request = new LiveRequest();
request.getLiveInfo(String.valueOf(programid), new HttpCallback<LiveInfoDetail>() {
            @Override
            public void onSuccess(LiveInfoDetail liveInfo) {
                if (liveInfo != null) {
                    if (mView != null) {
                        mView.showLiveInfo(liveInfo);
                    }
                } else {
                    if (mView != null) {
                        mView.showErrorInfo(ErrorStatus.ERROR_REQUEST);
                    }
                }
            }

            @Override
            public void onError(ApiException exception) {
                if (DEBUG_LIVE) {
                    Log.d(TAG, "getShowingInfo onError throwable: " + exception.getMessage());
                }
                if (mView != null) {
                    mView.showErrorInfo(ErrorStatus.ERROR_REQUEST);
                }
            }
        });
```

<h3 id="3.9.2">3.9.2 使用手机号获取进入聊天室Token</h3>
#### 接口名称

`
LiveRequest#getChatRoomToken(String uid, String phone, HttpCallback<ChatRoomTokenInfo> callback)
`

#### 接口说明

获取进入聊天室的Token。该接口需要用户手机号已经注册过听伴的账号。直播间显示的是听伴账号的用户名和头像。

#### 参数说明

| 参数名| 类型 | 是否必须 | 描述|
| :------| :-----| :-----| :----|
| uid | String | 是 | 用户id |
| phone | String | 是 | 手机号码 |
| callback | HttpCallback | 是 | 结果回调，返回聊天室信息[ChatRoomTokenDetail](#7.21) |

#### 代码示例

```java
HttpCallback callback = new HttpCallback<ChatRoomTokenDetail>() {
            @Override
            public void onSuccess(ChatRoomTokenDetail result) {
                if (result != null && result.getToken() != null && result.getAccid() != 0) {
                    String accountId = String.valueOf(result.getAccid());
                    String token = result.getToken();
                    String nickName = result.getNickName();
                    if (DEBUG_LIVE) {
                        Log.d(TAG, "registerToNim onSuccess accountId: " + accountId
                                + ", token: " + token + ", nickName: " + nickName);
                    }
                } else {
                    if (listener != null) {
                        listener.loginFailed(-1);
                    }
                }
            }

            @Override
            public void onError(ApiException t) {
                if (DEBUG_LIVE) {
                    Log.d(TAG, "registerToNim onError", t);
                }
                if (listener != null) {
                    listener.getAccountFailed(0);
                }
            }
        };
String uid = UserInfoManager.getInstance().getUserId();
String phone = UserInfoManager.getInstance().getPhoneNumber();
LiveRequest request = new LiveRequest();
request.getChatRoomToken(uid, phone, callback);
```

<h3 id="3.9.3">3.9.3 使用车厂账号获取进入聊天室token</h3>
#### 接口名称

`
LiveRequest#getChatRoomToken(String id, String avatar, String nickName, HttpCallback<ChatRoomTokenDetail> callback)
`

#### 接口说明

根据开发者(车厂)提供的唯一标识、头像、昵称获取进入直播的token。

#### 参数说明

| 参数名| 类型 | 是否必须 | 描述|
| :------| :-----| :-----| :----|
|id| String | 是 |第三方用户唯一标识|
|avatar| String | 是 |用户头像地址|
|nickName|String|是|用户昵称|
|callback| HttpCallback | 是 | 结果回调，返回聊天室信息[ChatRoomTokenDetail](#7.21) |

#### 代码示例

```java
HttpCallback callback = new HttpCallback<ChatRoomTokenDetail>() {
            @Override
            public void onSuccess(ChatRoomTokenDetail result) {
                if (result != null && result.getToken() != null && result.getAccid() != 0) {
                    String accountId = String.valueOf(result.getAccid());
                    String token = result.getToken();
                    String nickName = result.getNickName();
                    if (DEBUG_LIVE) {
                        Log.d(TAG, "registerToNim onSuccess accountId: " + accountId
                                + ", token: " + token + ", nickName: " + nickName);
                    }
                } else {
                    if (listener != null) {
                        listener.loginFailed(-1);
                    }
                }
            }

            @Override
            public void onError(ApiException t) {
                if (DEBUG_LIVE) {
                    Log.d(TAG, "registerToNim onError", t);
                }
                if (listener != null) {
                    listener.getAccountFailed(0);
                }
            }
        };
LiveRequest request = new LiveRequest();
request.getChatRoomToken(uid, avatar, nickName,callback);
```

<h2 id="3.10">3.10 订阅</h2>
订阅相关接口所在类SubscribeRequest。订阅支持专辑、PGC、广播的订阅，不支持单曲的订阅。

订阅功能无论听伴账号是否登录都可以使用。  

- 未登录——订阅是==该设备==的订阅，获取到的订阅列表也是在==该设备==订阅的节目，

- 登录——订阅是登录的==听伴账号==的订阅，获取到的订阅列表时==该设备与听伴账号==所有的订阅节目

<h3 id="3.10.1">3.10.1 获取用户订阅列表</h3>
#### 接口名称

`SubscribeRequest#getSubscribeList(int pageNum, int pageSize, HttpCallback<BasePageResult<List<SubscribeInfo>>> callback)`

#### 接口说明

获取当前听伴用户的订阅列表，可分页获取。

#### 参数说明

| 参数名| 类型 | 是否必须 | 描述|
|:------|:-----|:-----|:----|
|pageNum|int|是|第几页|
|pageSize|int|是|每页个数|
|callback|HttpCallback|否|回调，成功返回[BasePageResult](#7.1)&lt;List&lt;[SubscribeInfo](#7.23)&gt;&gt;|

#### 代码示例

```java
int pageNum = 1;
int pageSize = 20;
new SubscribeRequest().getSubscribeList(pageNum, pageSize, new HttpCallback<BasePageResult<List<SubscribeInfo>>>() {
           @Override
            public void onSuccess(BasePageResult<List<SubscribeInfo>> result) {
                mTvInfo.setText(mGson.toJson(result));
            }

            @Override
            public void onError(ApiException exception) {
                mTvInfo.setText(mGson.toJson(exception));
            }
        });
```

<h3 id="3.10.2">3.10.2 订阅</h3>
#### 接口名称

`SubscribeRequest#subscribe(long id, HttpCallback<SubscribeStatus> callback)`

#### 接口说明

根据专辑、PGC、广播的id订阅。传入id不需要区分专辑、PGC、广播类型。

#### 参数说明

| 参数名| 类型 | 是否必须 | 描述|
|:------|:-----|:-----|:----|
|id|long|是|专辑、PGC、广播的id|
|callback|HttpCallback|否|回调，成功时返回订阅状态[SubscribeStatus](#7.24)<br>订阅成功`SubscribeStatus.STATE_SUCCESS`<br>订阅失败`SubscribeStatus.STATE_FAILURE`<br>已订阅`SubscribeStatus.STATE_HAD_SUBSCRIBE`|

#### 代码示例

```java
long id = 0L;
        new SubscribeRequest().subscribe(id, new HttpCallback<SubscribeStatus>() {
            @Override
            public void onSuccess(SubscribeStatus result) {
                mTvInfo.setText(mGson.toJson(result));
            }

            @Override
            public void onError(ApiException exception) {
                mTvInfo.setText(mGson.toJson(exception));
            }
        });
```

<h3 id="3.10.3">3.10.3 取消订阅</h3>
#### 接口名称

`SubscribeRequest#unsubscribe(long id, HttpCallback<SubscribeStatus> callback)`

#### 接口说明

根据专辑、PGC、广播的id取消订阅。传入id不需要区分专辑、PGC、广播。

#### 参数说明

| 参数名| 类型 | 是否必须 | 描述|
|:------|:-----|:-----|:----|
|id|long|是|专辑、PGC、广播的id|
|callback|HttpCallback|否|回调，成功时返回取消订阅是否成功，<br>true表示取消订阅成功，false表示失败|

#### 代码示例

```java
new SubscribeRequest().unsubscribe(id, new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean result) {
                mTvInfo.setText(mGson.toJson(result));
            }

            @Override
            public void onError(ApiException exception) {
                mTvInfo.setText(mGson.toJson(exception));
            }
        });
```

<h3 id="3.10.4">3.10.4 检查是否订阅</h3>
#### 接口名称

`SubscribeRequest#isSubscribed(long id, HttpCallback<SubscribeStatus> callback)`

#### 接口说明

根据专辑、PGC、广播的id检查该专辑、PGC、广播是否已经订阅。传入id不需要区分专辑、PGC、广播。

#### 参数说明

| 参数名| 类型 | 是否必须 | 描述|
|:------|:-----|:-----|:----|
|id|long|是|专辑、PGC、广播的id|
|callback|HttpCallback|否|回调，成功时返回是否订阅，<br>true表示已订阅，false表示未订阅|

#### 代码示例

```java
new SubscribeRequest().isSubscribed(id, new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean result) {
                mTvInfo.setText(mGson.toJson(result));
            }

            @Override
            public void onError(ApiException exception) {
                mTvInfo.setText(mGson.toJson(exception));
            }
        });
```



<h2 id="3.11">3.11 收听历史</h2>
收听历史所有相关接口所在类`HistoryRequest`。收听历史只可保存专辑和PGC，不支持广播

收听历史功能无论听伴账号是否登录都可以使用。  

- 未登录——获取到的收听历史列表也是在==该设备==的收听历史。

- 登录——获取到的收听历史列表时==该设备与听伴账号==所有的收听历史。

<h3 id="3.11.1">3.11.1 获取收听历史列表</h3>
#### 接口名称

`HistoryRequest#getHistoryList(HttpCallback<List<ListeningHistory>> callback)`

#### 接口说明

获取收听历史列表，一次获取所有的，最多99条。

#### 参数说明

| 参数名   | 类型 | 是否必须 | 描述|
| -------- | ------ | -------- | --------------- |
| callback | HttpCallback&lt;List&lt;ListeningHistory&gt;&gt; | 否       | 成功，<br>返回收听历史[ListeningHistory](#7.25)集合 |

#### 代码示例

```java
new HistoryRequest().getHistoryList(new HttpCallback<List<ListeningHistory>>() {
            @Override
            public void onSuccess(List<ListeningHistory> listeningHistories) {
                if (mHistoryAdapter != null) {
                    mHistoryAdapter.setDataList(listeningHistories);
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取收听历史列表失败", exception);
            }
        });
```



<h3 id="3.11.2">3.11.2 清除收听历史</h3>
#### 接口名称

`HistoryRequest#clearListeningHistory(HttpCallback<Boolean> callback)`

#### 接口说明

清除收听历史。未登录听伴时，会清除该设备的收听历史；登录时，会清除设备和听伴账号的收听历史。

#### 参数说明

| 参数名   | 类型 | 是否必须 | 描述 |
| -------- | ----------| -------- | ---------- |
| callback | HttpCallback&lt;Boolean&gt; | 否| 返回true表示清除成功，false失败。 |

#### 代码示例

```java
new HistoryRequest().clearListeningHistory(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                if (aBoolean) {
                    showToast("清空历史成功");
                    mHistoryAdapter.clear();
                }else {
                    showToast("清空历史失败");
                }
            }
            @Override
            public void onError(ApiException exception) {
                showError("清空历史错误", exception);
            }
        });
```

<h3 id="3.11.3">3.11.3 保存收听历史</h3>
#### 接口名称

`HistoryRequest#saveListeningHistory(String type, long id, long audioId, long playedTime, long duration, long timestamp, HttpCallback<Boolean> callback)`

#### 接口说明

保存收听历史到服务器。播放器已经自动在播放的时候保存收听历史到服务器，开发者不需要再次手动调用该方法。  
播放器每收听一个单曲都会上传服务器一次，用于记录收听的位置。
如果是PGC中的非专辑单曲，如台宣、音乐等服务器不会记录，会导致如果只是收听了台宣或音乐等的不会有收听历史。

#### 参数说明

| 参数名     | 类型 | 是否必须 | 描述                                                         |
| ---- | ---------- | -------- | -------- |
| type       | String                      | 是       | 单曲所在集合类型，<br>专辑ResType.TYPE_ALBUM、<br>PGC ResType.TYPE_RADIO<br>不确定ResType.TYPE_INVALID |
| id         | long                        | 是       | 单曲所在集合id                                               |
| audioId    | long                        | 是       | 单曲id                                                       |
| playedTime | long                        | 是       | 已播放时间                                                   |
| duration   | long                        | 是       | 单曲时长                                                     |
| timestamp  | long                        | 是       | 时间戳                                                       |
| callback   | HttpCallback&lt;Boolean&gt; | 否       | 回调，返回true表示保存成功，false失败                        |

#### 代码示例

```java
new HistoryRequest().saveListeningHistory(type, radioId, audioId, playedTime, duration, timestamp, new HttpCallback<Boolean>() {
  public void onSuccess(Boolean aBoolean) {
    Log.d("PlayerManager", "saveHistory保存历史: " + aBoolean);
  }

  public void onError(ApiException e) {
    Log.e("PlayerManager", "saveHistory保存历史异常=" + e);
  }
});
```



<h2 id="3.12">3.12 场景推荐</h2>
场景推荐是指当车主处于某个场景时，根据当前的场景进行对应的节目推荐。

场景推荐接口所在类`SceneRequest`，目前只支持一种点火场景和三种速度场景。

场景推送目前只返回智能电台类型。

<h3 id="3.12.1">3.12.1 获取场景信息</h3>
#### 接口名称

`SceneRequest#getSceneInfo(HttpCallback<SceneInfo> callback, Scene... scenes)`

`SceneRequest#getSceneInfo(HttpCallback<SceneInfo> callback, Map<String, String> extInfo, Scene... scenes)`

#### 接口说明

获取推送信息，可以带附件信息，也可以不带。点火场景只需要传入[AccScene](#7.24.1)对象，速度场景传入带有对应速度参数的[SpeedScene](#7.24.2)对象，可以同时传入多个

#### 参数说明

| 参数名   | 类型  | 是否必须 | 描述 |
| :------- | :------- | :------- | :------------|
| scenes   | Scene...  | 是    | 场景类型参数,[AccScene](#7.24.1)-点火场景;[SpeedScene](#7.24.2)-速度场景 |
| extInfo  | Map&lt;String, String&gt; | 否       | 额外信息，暂时没用。                                         |
| callback | HttpCallback              | 否       | 回调，成功时返回场景信息[SceneInfo](#7.24)。                 |

#### 代码示例

```java
Scene sceneACC = new AccScene();
new SceneRequest().getSceneInfo(new HttpCallback<SceneInfo>() {
  @Override
  public void onSuccess(SceneInfo sceneInfo) {
    if (tvRst != null && sceneInfo != null) {
      tvRst.setText(tvRst.getText() + "\n\r" + sceneInfo.toString() + "\n\r");
    }
  }

  @Override
  public void onError(ApiException exception) {
    if (tvRst != null && exception != null) {
      tvRst.setText(tvRst.getText() + "\n\r" + exception.toString() + "\n\r");
    }

  }
}, sceneACC);
```



<h2 id="3.13">3.13 品牌信息</h2>
品牌信息相关接口所在类`BrandRequest`

<h3 id="3.13.1">3.13.1 获取品牌信息</h3>
#### 接口名称

`BrandRequest#getBrandInfo(HttpCallback<BrandDetails> callback)`

#### 接口说明

获取品牌信息，包括名称，logo，用户须知

#### 参数说明

| 参数名   | 类型  | 是否必须 | 描述 |
| -------- | ----------- | -------- |--------------- |
| callback | HttpCallback&lt;BrandDetails&gt; | 否       | 成功，返回品牌信息[BrandDetails](#7.26)。 |

#### 代码示例

```java
new BrandRequest().getBrandInfo(new HttpCallback<BrandDetails>() {
            @Override
            public void onSuccess(BrandDetails brandDetails) {
                if (brandDetails != null) {
                    mTvBrandName.setText(brandDetails.getBrand());
                    Glide.with(BrandInfoActivity.this)
                      .load(brandDetails.getLogo())
                      .into(mIvBrandLogo);
                    mTvBrandAgreement.setOnClickListener(v -> {
                        Intent intent=new  Intent();
                        intent.setAction(Intent.ACTION_VIEW);
                        intent.setData(Uri.parse(brandDetails.getUserAgreement()));
                        startActivity(intent);
                    });
                }
            }

            @Override
            public void onError(ApiException exception) {

            }
        });
```

<h1 id="4">4 播放器说明</h1>
--

本部分介绍考拉FM智能电台OpenSDK中附带播放器的使用说明

<h2 id="4.1">4.1 播放器概述</h2>
由于Android系统自带的媒体播放器不同版本差异较大，且后期我们会对考拉FM平台的音频内容加密，所以如果希望正常收听考拉FM平台的音频内容，需要定制版播放器才能正常播放。目前播放器支持点播、传统广播的直播和回放。

点播使用PlayerManager。   
传统广播直播和回放使用BroadcastRadioPlayerManager。  
直播播放使用LivePlayerManager。

<h2 id="4.2">4.2 播放器状态介绍</h2>
为了方便开发者接入SDK播放器，SDK播放器的各状态尽量保持和Android系统播放器状态一致。

<h2 id="4.3">4.3 播放器初始化和释放</h2>
初始化方法：由SDK自动完成  
播放器释放的方法： 说明：当程序不在使用播放器时，应及时释放播放器，让系统回收资源。   
具体方法是：`PlayerManager.getInstance(getApplicationContext()).destroy();`

<h2 id="4.4">4.4 通过播放器播放</h2>
|                    描述                    |                           方法                            |                             说明                             |
| :----------------------------------------: | :-------------------------------------------------------: | :----------------------------------------------------------: |
|              使当前播放器可用              |                      enablePlayer()                       |                       会禁用其他播放器                       |
|               禁用当前播放器               |                      disablePlayer()                      |                                                              |
|             当前播放器是否可用             |                     isPlayerEnable()                      |                    true可用，false不可用                     |
|                 播放器销毁                 |                         destroy()                         |  会解绑播放服务，<br>在使用完PlayerManager后需要执行此方法   |
|         当前播放器是否处于错误状态         |                     isPlayerFailed()                      |      一般情况是404导致，<br>true表示失败，false表示成功      |
|         当前播放器是否可以重启播放         |                    canReStartPlayer()                     |                     true为是，false为否                      |
|                播放一个单曲                |                  play(PlayItem playItem)                  |    该单曲是在播单中存在的，或者是直播(LivePlayerManager)     |
|            针对断点续播功能设计            |                        playStart()                        |    启动客户端，<br>根据历史初始化播单但没有自动播放时调用    |
|         当前播放器是否可以重启播放         |                    canReStartPlayer()                     |                     true为是，false为否                      |
|                  专辑点播                  |              playRadio(RadioBean radioBean)               |                 需要自己转换数据为RadioBean                  |
|                播放一个专辑                |                    playAlbum(long id)                     |                            专辑id                            |
|              播放一个PGC电台               |                     playPgc(long id)                      |                          PGC电台id                           |
| 根据单曲ID从当前播单中选取一个单曲进行播放 |            playAudioFromPlayList(long audioId)            |                            单曲id                            |
|                播放一个单曲                |                  playAudio(long audioId)                  |                     会播放单曲所在的专辑                     |
|                一键播放订阅                |                     playSubscribes()                      |                 以流的形式播放所有订阅的专辑                 |
|           播放指定传统广播的方法           | playBroadcast(long bId, final GeneralCallback  callback); | BroadcastRadioPlayerManager中。广播电台id。callback 播放广播回调，可传null，<br>如需处理相关逻辑可关注 callback中的<br/>onResult回调函数中的boolean结果，<br>true代表请求数据成功且即将进行播放，<br>false为播放广播失败。 |


<h2 id="4.5">4.5 播放器控制</h2>
PlayerManager和BroadcastRadioPlayerManager提供系列方法用于控制播放器的状态，主要有：开始播放，暂停播放，上一首，下一首，拖动等。   
PlayerManager和BroadcastRadioPlayerManager的方法如下：

|描述 | 方法 | 说明 |
|:--:|:---:|:---:|
|获取播放对象单例|getInstance(context)|  |
|播放指定的某个单曲 | play(PlayItem playItem) |  |
|暂停 | pause() | 广播的直播暂停是reset |
|播放 | play() |  |
|结束播放 | reset() |  |
|播放当前列表中的上一首 | playPre() |  |
|播放当前列表中的下一首 | playNext() |  |
|获取当前播放位置 | getCurPosition()  |  |
|判断播放器是否正在播放 |isPlaying() |  |
|是否有上一首 |hasPre()|  |
|是否有下一首 |hasNext()|  |
|拖动 |seek(int position)| 广播直播无法拖动 |
|设置播放器音量 |setVolume(float leftVolume, float rightVolume)|  |
|判断播放状态是否处于paused状态 |boolean isPaused()| true 为暂停状态 false 为其他状态。<br/>如果是广播直播的话，该方法会一直返回false |
|判断播放状态，true 正在播放；false 未播放。 |isPlaying()|  |
|切换播放器暂停或开始状态 |switchPlayerStatus()|  |
|清除播单数据 |clearPlayerList()|  |
|设置播放音质 |setSoundQuality(int quality)| 传入音质类型[SoundQuality]() |
|获取当前播放音质 |getSoundQuality()| 返回当前音质类型 |

<h2 id="4.6">4.6 播放器回调</h2>
此部分介绍第三方应用如何监听播放器的各种状态变化具体请实现相应的回调，并通过以下操作方式添加和移除监听。

|  描述       |      方法                       |           说明           |
| :----------: | :-----------------------------: | :----------------------: |
|  添加播放状态监听 |  addPlayerStateListener(IPlayerStateListener)   |                         |
|           移除播放状态监听           | removePlayerStateListener(IPlayerStateListener) |                          |
|         添加开始播放监听事件         |    addStartPlayItemListener(GeneralCallback)    | 在开始播放某个单曲时回调 |
|         移除开始播放监听事件         |  removeStartPlayItemListener(GeneralCallback)   |                          |
| 添加播放一个音频获取内容结果监听事件 |     addGetContentListener(GeneralCallback)      |    在创建完播单时回调    |
| 移除播放一个音频获取内容结果监听事件 |    removeGetContentListener(GeneralCallback)    |                          |
|          注册预缓存进度监听          |          regDownloadProgressListener()          |  在播放时加载数据时回调  |
|          注销预缓存进度监听          |         unRegDownloadProgressListener()         |                          |

<h2 id="4.7">4.7 点播播放器相关数据获取</h2>
此部分介绍第三方应用在触发一个播放行为后如何获取后续所播专辑和当前播放器可用的播单信息

<h3 id="4.7.1">4.7.1 PlayerListManager相关函数说明</h3>
此部分介绍如何通过点播播放器单曲播单管理类操作播单逻辑

描述 | 方法 | 参数或返回值说明 
:----------: | :-----------:  | :-----------: 
获取播放器播单 | ArrayList&lt;[PlayItem](#7.17)&gt; getPlayList() | 当前播放器所播放单曲列表
注册播放器播单条目数改变监听 | void registerPlayerListChangedListener(<br>IPlayerListChangedListener listener) | `IPlayerListChangedListener`
注销播放器播单条目数改变监听 | void unRegisterPlayerListChangedListener(<br>IPlayerListChangedListener listener) | `IPlayerListChangedListener`
获取播放器正在播放单曲在播单中的位置 | int getCurPosition() | 例如0,1,2...
获取播放器播单总条目数 | int getPlayListSize() | 例如 20,30...
根据单曲对象获取当前单曲在播单中的位置 | int getCurrentPlayItemPosition([PlayItem](#7.17) playItem) | 例如0,1,2...
根据某个单曲的id获取播单中PlayItem对象 | PlayItem getPlayItemByAudioId(long audioId) | 单曲对象[PlayItem](#7.17)
获取当前播单正在播放的[PlayItem](#7.17)对象 | [PlayItem](#7.17) getCurPlayItem() | 单曲对象PlayItem
根据PlayItem对象同步当前播放器播单索引值 | void setCurPlayItemIndex([PlayItem](#7.17) playItem) |
设置播放器播单当前索引值 | void setCurPlayItemIndex(int index) | 待设置播放器播单索引值
追加播单数据至播单头部|void addPlaylistToHeader(List&lt;PlayItem&gt; playList)|随机模式下会将该播单随机后加入当前播单头部。
初始化随机播放池|initRandomPool()|初始化当前播放中所有的数据重新随机播放
初始化随机播放池|initRandomPool(List&lt;PlayItem&gt; playList)|初始化指定的播单数据随机
插入一个单曲|insertPlayItem(PlayItem playItem)|会插入到下一个要播放的顺序中
插入一个单曲|insertPlayItemAtHead(PlayItem playItem)|会插入到播单的最前面，并从开头播放。
插入一个播单到播单|insertPlaylist(List&lt;PlayItem&gt; playlist)|会插入到当前播单的后面
追加播单数据|addPlaylist(List&lt;PlayItem&gt; playList)|随机模式下会将该播单随机后加入当前播单后面。
清除点播播单|clearPlayList()|
获取当前正在播放单曲对象|getCurPlayItem()|
根据当前播放音频同步原始播单游标|setCurPlayItemIndex(PlayItem playItem)|通过遍历当前播单比对单曲id
设置是否自动播放完毕|setAutoPlayComplete(boolean )|
判断是否播单播放完毕|isAutoPlayComplete()|

<h3 id="4.7.2">4.7.2 PlayerRadioListManager相关函数说明</h3>
此部分介绍如何通过点播播放器专辑列表管理类获取相关专辑信息逻辑

描述 | 方法 | 参数或返回值说明 
------------ | -------------  | ------------- 
获取播放器播放专辑或PGC电台列表对象 | ArrayList&lt;PlayerRadioListItem&gt; getRadioList() | 当前播放器播放专辑或PGC电台列表
获取播放器播放专辑或PGC电台列表大小 | int getCount() | 例如0,1,2...
获取播放器正在播放专辑或PGC电台在列表中的位置 | int getCurPosition() | 例如0,1,2...
获取播放器正在播放的PlayerRadioListItem对象 | PlayerRadioListItem getCurRadioItem() | 专辑、PGC电台对象PlayerRadioListItem
根据索引值获取播放器PlayerRadioListItem对象 | PlayerRadioListItem getRadioItem(int index) | 专辑、PGC电台对象PlayerRadioListItem
播放器拉取更多播单数据|fetchMorePlaylist(final OnPlayItemInfoListener onPlayItemInfoListener,false, false)| 播单信息监听器, 第一个boolean指是否为自动播放完毕请求数据 true为是， false为否                                                                                                            第二个boolean指是否为播放器播单调用true为是，false为否

<h3 id="4.7.3">4.7.3 BroadcastRadioListManager相关函数说明</h3>
此部分介绍如何通过广播播放器节目列表管理类获取相关节目信息逻辑

描述 | 方法 | 参数或返回值说明 
------------ | -------------  | ------------- 
获取播放器播单 | ArrayList&lt;[PlayItem](#7.17)&gt; getPlayList() | 当前播放器所播放节目列表
注册播放器播单条目数改变监听 | void <br>registerPlayerListChangedListener(<br>IPlayerListChangedListener listener) | 播单条目数改变监听
注销播放器播单条目数改变监听 | void <br>unRegisterPlayerListChangedListener(<br>IPlayerListChangedListener listener) | 播单条目数改变监听
获取播放器当前播放正在播放节目对象 | [PlayItem](#7.17) getCurPlayItem() | 节目对象
获取播放器正在播放广播对象 | BroadcastRadioPlayItem getCurRadioItem() | 广播对象
获取播放器正在播放节目索引 | int getCurPosition() | 例如0,1,2...
添加播放器广播信息改变监听 | void addOnBroadcastRadioChangedListener(<br>OnBroadcastRadioListChangedListener listener) | 广播信息改变监听
移除播放器广播信息改变监听 | void removeOnPlayingRadioChangedListener(<br>OnBroadcastRadioListChangedListener listener) | 广播信息改变监听

#### 关于OnBroadcastRadioListChangedListener说明：

```java
com.kaolafm.sdk.core.mediaplayer.OnBroadcastRadioListChangedListener {
    /**
    * 广播节目更新
    * @param position 当前更新节目索引值
    */
    void onProgramUpdated(int position);
    /**
    * 直播节目倒计时更新回调
    * @param timeStr 当前直播节目播放位置时间类似（19：09：09）
    */
    void onLivingCountDown(String timeStr);
}
```

方法/步骤：

描述 | 方法 | 参数说明 
------------ | -------------  | ------------- 
开始播放 | void onPlayerPlaying([PlayItem](#7.17) playItem) | 当前播放的单曲对象
暂停播放 | void onPlayerPaused([PlayItem](#7.17) playItem) | 当前播放的单曲对象
停止播放 | void onIdle([PlayItem](#7.17) playItem) | 当前播放的单曲对象
播放完成 | void onPlayerEnd([PlayItem](#7.17) playItem) | 当前播放的单曲对象
播放器准备完成 | void onPlayerPreparing([PlayItem](#7.17) playItem) | 当前播放的单曲对象
开始缓冲 | void onBufferingStart([PlayItem](#7.17) playItem) | 当前播放的单曲对象
结束缓冲 | void onBufferingEnd([PlayItem](#7.17) playItem) | 当前播放的单曲对象
播放进度回调 | void onProgress(String url, <br>int position,<br> int duration, <br>boolean isPreDownloadComplete) | url是当前播放url，<br>position是当前单曲播放进度单位ms，<br>duraion为当前单曲总时长单位ms，<br>isPreDownloadComplete<br>为当前单曲是否已经预缓存完成<br>true为是，false为否
播放器错误 | void onPlayerFailed([PlayItem](#7.17) playItem, <br>int what, <br>int extra) | 当前播放的单曲对象，<br>what和extra开发者可不用处理

<h2 id="4.8">4.8 音频焦点</h2>
音频焦点控制相关方法在PlayerManger类中

|描述|方法|参数说明|
|-----|---|---|
|重新获取音频焦点是否可以继续播放|setCanContinuePlayInBackground<br>(boolean canContinuePlayInBackground)|设置当app在失去音频焦点的状态下退至后台是否还可以继续播放，true为是，false为否，默认true|
|初始化时获取音频焦点|setCanRequestAudioFocusOnPlayerInit<br>(boolean canRequestAudioFocusOnPlayerInit)|true为是，false为否，默认true|
|设置是否使用sdk自带音频焦点逻辑|setCanUseDefaultAudioFocusLogic<br>(boolean canUseDefaultAudioFocusLogic)|true是，false是。默认true|
|设置焦点变化回调|setOnAudioFocusChangeListener<br>(OnAudioFocusChangeInter listener)|OnAudioFocusChangeInter回调，焦点状态和AudioManager的状态值一致|
|设置是否可以继续播放控制实现|setOnContinuePlayFunction<br>(OnContinuePlayFunction)|true是可以继续播放，false是不继续播放。|
|设置对音频焦点的处理逻辑|setOnKLAudioFocusChangeListener<br>(OnKLAudioFocusChangeListener)|返回false表示不播放，true表示播放|

<h1 id="5">5.ErrorCode</h1>
--

errcode | errmsg
------- | --------
307   | 请求被重定向
404	 | 请求地址不存在
403	 | 请求被服务器拒绝
408	 | 请求超时
500	 | 服务器错误
503   | 服务不可用
600   | 未知错误
601	 | json解析异常
602   | 无法解析该域名
603   | 网络连接异常
604   | 网络连接超时
605   | 数据错误
606   | 数据为空
40000 | 参数有误
40100 | 身份验证未通过
40102 | Token失效
40300 | 权限校验错误
40301 | 设备未激活
40400 | 资源不存在
40431 | refreshToken不存在或已过期
40432 | token不存在或已过期
40500 | 请求方式不允许
40600 | 请求url不存在
42900 | 请求频率超配
42901 | 设备超限额
50000 | 服务器内部错误
50001 | 设备已激活
50200 | 服务关闭或正在升级
50500 | 重复激活
50600 | appid未激活

<h1 id="6">6 Android SDK相关问题反馈</h1>
--

<h2 id="6.1">6.1 关于接口返回图片URL使用说明</h2>
目前接口返回的url均为default.jpg或default.png，例如http://image.kaolafm.net/mz/images/201603/8d3187ae-7602-489d-afd6-0f27fd9d98f4/default.jpg，开发者可根据实际情况将图片url替换成http://image.kaolafm.net/mz/images/201603/8d3187ae-7602-489d-afd6-0f27fd9d98f4/250_250.jpg 等不同尺寸资源。  
因服务器给出的default名称的图片尺寸会比较大，建议开发者根据实际使用场景酌情进行图片尺寸的替换。  
目前服务器支持100\_100，250\_250，340\_340，550\_550四种定制尺寸资源。


<h2 id="6.2">6.2 关于专辑、单曲、PGC电台、广播的说明</h2>
- **专辑：**是包含1个或若干个单曲的一个单曲的集合，对应数据类型为"0"； 

- **单曲：**是从属于某一个特定专辑的可进行播放的实体，对应数据类型为"1";  

- **PGC电台：**就是**AI电台**，是包含1个或若干个单曲且所包含单曲可能从属于不同专辑的集合，对象数据类型为"3"；   

- **在线广播：**就是传统收音机所包含的地方性或全国性的广播电台例如"北京交通广播"，"央广中国之声"，对象数据类型为"11"；

<h2 id="6.3">6.3 关于运营数据的说明</h2>
运营数据分人工运营数据和自动运营数据，都是需要根据厂商各自的需要在后台进行个性化配置，该后台目前占未对外开放，所以需要联系产品或项目等相关人员就行配置。配置时最好根据UI/UE进行定制化配置，可以减少接入成本。

<h2 id="6.4">6.4 关于使用< 获取整颗栏目树> 接口说明</h2>
当使用 [获取整颗栏目树](#3.3.6) 接口时，如果传入的isWithMembers为true， 会返回整颗栏目树(包含具体的栏目)，接口返回```List<ColumnGrp>```的数据对象。如果想得到具体的栏目，需要判断List里面```ChildColumns```成员是否为[Column](#7.4)对象，需要强转，请使用如下代码判断：
 ```ColumnGrp.getChildColumns().get(index) instanceof Column```

<h1 id="7">7 Model</h1>
--

<h2 id="7.1">7.1 BasePageResult&lt;T&gt;</h2>
带有分页的返回结果基类

|变量名|类型|描述|
|:-----|:-----|:-----|
|haveNext|int|是否有下一页 1表示有下一页，0表示没有|
|nextPage|int|下一页页数，分页请求下一页需要传入|
|havePre|int|是否有上一页 1表示有，0表示没有|
|prePage|int|上一页页码|
|currentPage|int|当前页码|
|count|int|总个数|
|sumPage|int|总页数|
|pageSize|int|每页个数|
|dataList|T|结果数据|

<h2 id="7.2">7.2 Success</h2>
检查手机号、注册、登录返回信息

|变量名|类型|描述|
|:-----|:-----|:-----|
|code|String|结果的code。等于`Success.CODE_SUCCESS`表示成功；等于`Success.PHONE_NUMBER_IS_EXIST`表示手机号已经注册；等于`Success.PHONE_NUMBER_IS_NOT_EXIST`表示手机号未注册|
|msg|String|结果信息|

<h2 id="7.3">7.3 ColumnGrp</h2>
栏目组

|变量名|类型|描述|
|:-----|:-----|:-----|
|code|String|栏目组的code，请求运营相关接口会用到。该值不是节目的id，不能用于获取节目信息或播单|
|title|String|标题|
|subtitle|String|副标题|
|description|String|描述|
|imageFiles|Map\<String, ImageFile\>|图片集合|
|extInfo|Map\<String, String\>|额外信息集合，暂时没用|
|type|String|类型，是对应类型栏目组的类名或自定义名称。<br>开发者可以不用关注|
|childColumns|List\<? extends ColumnGrp\>|子栏目|

<h2 id="7.4">7.4 Column</h2>
栏目，继承自栏目组[ColumnGrp](#7.2)。

|变量名|类型|描述|
|:-----|:-----|:-----|
|forwardToMore|int|是否支持跳转至更多。<br>1，支持；0，不支持|
|moreColumnMember|[ColumnMember](#7.5)|跳转至更多的目标栏目成员，<br>一般是分类|
|columnMembers|List\<? extends ColumnMember\>|栏目成员|

<h2 id="7.5">7.5 ColumnMember</h2>
栏目成员父类，所有类名成员都继承该类

|变量名|类型|描述|
|:-----|:-----|:-----|
|code|String|栏目成员的code，请求运营相关接口会用到。该值不是节目的id，不能用于获取节目信息或播单|
|title|String|标题|
|subtitle|String|副标题|
|description|String|描述|
|cornerMark|int|是否显示角标，1是，0否|
|imageFiles|Map\<String, ImageFile\>|图片集合|
|extInfo|Map\<String, String\>|额外信息，暂时没用|
|type|String|类型，是对应栏目成员子类的类名或自定义名称。<br>开发者可以不用关注|

<h3 id="7.5.1">7.5.1 AlbumDetailColumnMember</h3>
栏目成员-专辑，继承自[ColumnMember](#7.5)

|变量名|类型|描述|
|:-----|:-----|:-----|
|albumId|long|专辑Id|
|playTimes|int|收听数|

<h3 id="7.5.2">7.5.2 AudioDetailColumnMember</h3>
栏目成员-单曲，继承自[ColumnMember](#7.5)

|变量名|类型|描述|
|:-----|:-----|:-----|
|audioId|long|单曲Id|
|playTimes|int|收听数|

<h3 id="7.5.3">7.5.3 BroadcastDetailColumnMember</h3>
栏目成员-广播，继承自[ColumnMember](#7.5)

|变量名|类型|描述|
|:-----|:-----|:-----|
|broadcastId|long|广播Id|
|playTimes|int|收听数|
|freq|String|广播频率|

<h3 id="7.5.4">7.5.4 CategoryColumnMember</h3>
栏目成员-分类，继承自[ColumnMember](#7.5)

|变量名|类型|描述|
|:-----|:-----|:-----|
|categoryCode|String|分类Code|
|contentType|String|分类内容类型。1:专辑；2:广播；3:直播；4:智能电台。<br>该类型与[ResType](#7.22)不对应，可以使用`getContenResType()`获取与[ResType](#7.22)对应的类型|

<h3 id="7.5.5">7.5.5 LiveProgramDetailColumnMember</h3>
栏目成员-直播，继承自[ColumnMember](#7.5)

|变量名|类型|描述|
|:-----|:-----|:-----|
|liveProgramId|long|直播Id|

<h3 id="7.5.6">7.5.6 RadioDetailColumnMember</h3>
栏目成员-AI电台，继承自[ColumnMember](#7.5)

|变量名|类型|描述|
|:-----|:-----|:-----|
|radioId|long|电台Id|
|playTimes|int|收听数|

<h3 id="7.5.7">7.5.7 SearchResultColumnMember</h3>
栏目成员-搜索结果，继承自[ColumnMember](#7.5)

|变量名|类型|描述|
|:-----|:-----|:-----|
|keyword|String|关键词|

<h3 id="7.5.8">7.5.8 WebViewColumnMember</h3>
栏目成员-web页面，继承自[ColumnMember](#7.5)

|变量名|类型|描述|
|:-----|:-----|:-----|
|url|String|页面链接|

<h2 id="7.6">7.6 Category</h2>
分类，该类型下面可以有子分类。

|变量名|类型|描述|
|:-----|:-----|:-----|
|code|String|分类的code，请求运营相关接口会用到。该值不是节目的id，不能用于获取节目信息或播单|
|type|String|类型。开发者无需关心，会一直为null|
|name|String|分类名|
|description|String|描述|
|contentType|Integer|分类内容类型。<br>1:专辑；2:广播；3:直播；<br>4:智能电台。<br>该类型与[ResType](#7.22)不对应，可以使用`getContenResType()`获取与[ResType](#7.22)对应的类型|
|childCategories|List\<Category\>|子分类|
|imageFiles|Map\<String, ImageFile\>|图片集合|
|extInfo|Map\<String, String\>|额外信息，暂时没用|

<h3 id="7.6.1">7.6.1 LeafCategory</h3>
叶子分类，继承自分类，该类型下面可以有分类成员。

|变量名|类型|描述|
|:-----|:-----|:-----|
|categoryMembers|List&lt;CategoryMember&gt;|分类成员列表|

<h2 id="7.7">7.7 CategoryMember</h2>
人工运营分类成员（父类）

|变量名|类型|描述|
|:-----|:-----|:-----|
|code|String|分类成员的code，请求运营相关接口会用到。该值不是节目的id，不能用于获取节目信息或播单|
|title|String|标题|
|subtitle|String|副标题|
|description|String|描述|
|imageFiles|Map\<String, ImageFile\>|图片集合|
|extInfo|Map\<String, String\>|额外信息，暂时没用|
|type|String|类型，是对应分类成员子类的类名或自定义名称。<br>开发者可以不用关注|

<h3 id="7.7.1">7.7.1 AlbumCategoryMember</h3>
人工运营分类成员-专辑，继承自[CategoryMember](#7.7)

|变量名|类型|描述|
|:-----|:-----|:-----|
|albumId|long|专辑id|
|playTimes|int|收听数|

<h3 id="7.7.2">7.7.2 BroadcastCategoryMember</h3>
人工运营分类成员-广播，继承自[CategoryMember](#7.7)

|变量名|类型|描述|
|:-----|:-----|:-----|
|broadcastId|long|广播id|
|playTimes|int|收听数|

<h3 id="7.7.3">7.7.3 LiveProgramCategoryMember</h3>
人工运营分类成员-直播，继承自[CategoryMember](#7.7)

|变量名|类型|描述|
|:-----|:-----|:-----|
|liveProgramId|long|直播id|

<h3 id="7.7.4">7.7.4 RadioCategoryMember</h3>
人工运营分类成员-AI电台，继承自[CategoryMember](#7.7)

|变量名|类型|描述|
|:-----|:-----|:-----|
|radioId|long|电台id|
|playTimes|int|收听数|

<h2 id="7.8">7.8 ImageFile</h2>
图片文件

|变量名|类型|描述|
|:-----|:-----|:-----|
|url|String|图片地址|
|width|Integer|图片的宽|
|height| Integer |图片的高|
|type|String|图片的类型，<br>等于`ImageFile.KEY_ICON`表示图标，<br>等于`ImageFile.KEY_COVER`表示封面|

<h2 id="7.9">7.9 VoiceSearchResult</h2>
语义搜索返回结果。playType为1, playIndex为0, 直接播放第一个结果.

|变量名|类型|描述|
|:-----|:-----|:-----|
|playType|int|播放类型，<br>0: 选择播放；1: 直接播放；2: 延时播放|
|playIndex|int|播放音频下标，下标以0开始；<br>大于0，表示列表播放，取到此下标位置|
|delayTime|int|播放延迟时间单位为ms，playTime为2时有效|
|programList|List&lt;[SearchProgramBean](#7.10)&gt;|结果数据|

<h2 id="7.10">7.10 SearchProgramBean</h2>
语音搜索返回的节目数据

|变量名|类型|描述|
|:-----|:-----|:-----|
|id|long|节目id|
|name|String|节目名称|
|img|String|节目图片链接|
|type|int|节目类型，0-专辑，1-单曲，3-PGC，11-广播。见[ResType](#7.22)|
|albumName|String|专辑名称|
|source|int|来源|
|sourceName|String|来源中文名称|
|duration|long|时长|
|playUrl|String|播放地址|
|comperes|List&lt;Compere&gt;|主持人信息列表|
|freq|String|广播的频率信息，广播节目时存在|

<h3 id="7.10.1">7.10.1 Compere</h3>
SearchProgramBean的内部类，主持人信息

|变量名|类型|描述|
|:-----|:-----|:-----|
|name|String|主持人姓名|
|des|String|主持人描述|
|img|String|主持人图片|

<h2 id="7.11">7.11 AlbumDetails</h2>
专辑详细信息

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|id | long  | 专辑id|
|name | String  | 专辑名称|
|img | String  | 专辑封面URL|
|desc | String  | 专辑简介|
|listenNum | long  | 收听数|
|followedNum | long  | 订阅数|
|countNum | int  | 总期数|
|isOnline | int  | 是否上线，0-否，1-是|
|sortType | int | 默认热度排序，1-正序，0-倒序|
|hasCopyright | int  | 是否有版权，0-否 ,1-有|
|host |List&lt;[Host](#7.14)&gt;| 主持人信息组|
|status | String  | 专辑状态|
|updateDay | String  | 更新周期|
|keyWords | List&lt;String&gt;  | 关键词|
|commentNum | int  | 评论数|
|lastCheckDate | long  | 最新单曲更新时间|
|type | String  | 类型，0-专辑，1-单曲，3-智能电台。见[ResType](#7.22) |
|isSubscribe | int  | 是否订阅,1=是，0=否|

<h2 id="7.12">7.12 RadioDetails</h2>
智能电台(PGC)详细信息

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
||id | long  |
|name | String  | 智能电台名称|
|img | String  | 智能电台封面URL|
|type | String  | 类型，0-专辑，1-单曲，3-智能电台。见[ResType](#7.22) |
|followedNum | long  | 订阅数|
|isOnline | int  | 是否在线，0-否，1-是|
|listenNum | long  | 收听数|
|desc | String  | 智能电台简介|
|isSubscribe | int  | 是否订阅,1=是，0=否|
|host | List&lt;[Host](#7.14)&gt; |主持人信息组

<h2 id="7.13">7.13 AudioDetails</h2>
单曲详细信息

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|audioId | long  | 单曲id|
|audioName | String  | 单曲名称|
|audioPic | String  | 单曲图片URL|
|audioDes | String  | 单曲简介|
|listenNum | long  | 收听数|
|likedNum | long  | 点赞数|
|commentNum | int  | 评论数|
|orderNum | int  | 期数|
|mp3PlayUrl32 | String  | 32码率mp3格式地址|
|mp3PlayUrl64 | String  | 64码率mp3格式地址|
|aacPlayUrl | String  | aac格式地址|
|aacPlayUrl32 | String  | 32码率aac格式地址|
|aacPlayUrl64 | String  | 64码率aac格式地址|
|aacPlayUrl128 | String  | 128码率aac格式地址|
|aacPlayUrl320 | String  | 320码率aac格式地址|
|mp3FileSize32 | long  | 32码率mp3格式文件大小|
|mp3FileSize64 | long  | 64码率mp3格式文件大小|
|aacFileSize | long  | aac格式文件大小|
|host | List&lt;[Host](#7.14)&gt; | 主持人信息组|
|updateTime | String  | 更新时间|
|hasCopyright | int  | 是否有版权，0-否，1-有|
|clockId | String  | 时间标记|
|originalDuration | long  | 音频文件原始时长，不带片花|
|duration | long  | 音频文件默认时长|
|trailerStart | long  | 片花开始位置|
|trailerEnd | long  | 片花结束位置|
|albumId | long  | 专辑id|
|albumName | String  | 专辑名称|
|albumPic | String  | 专辑封面URL|
|contentType | int | 编排位内容类型0:分类,1:专辑,2:台宣,3:广播,4:歌曲,5:个推,6:地域,7:直播 |
|contentTypeName | String | 内容类型名称 |
|mainTitleName | String | 内容主标题名称 |
|subheadName | String | 内容副标题名称 |
|hasNextPage | int | 是否有下一页，0表示没有，1表示有。再播放PGC时，当为0时不再拉取数据。|

<h2 id="7.14">7.14 Host</h2>
主持人信息

|变量名|类型|描述|
|:-----|:-----|:-----|
|name|String|主持人姓名|
|des|String|主持人描述|
|img|String|主持人图片|

<h2 id="7.15">7.15 BroadcastDetails</h2>
传统广播详细信息

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|broadcastId | long  | 广播id|
|name | String  | 广播名称|
|img | String  | 广播封面URL|
|classifId | int  | 广播类型id|
|classifyName | String  | 广播类型名称|
|isSubscribe | int  | 是否订阅,1=是，0=否|
|playUrl | String  | 直播流地址|
|onLineNum | int  | 在线收听数|
|likeNum | long | 赞数|
|status | int  | 上下线状态,1=上线0=下线|
|roomId | int  | 直播间id|
|freq | String | 广播频段|
|icon | String | 广播图片|

<h2 id="7.16">7.16 ProgramDetails</h2>
传统广播节目详细信息

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|programId | long | 节目id|
|broadcastId | long | 广播id|
|nextProgramId | long | 下一期节目id，默认为-1|
|preProgramId | long | 上一期节目id，默认为-1|
|title | String | 节目名称|
|backLiveUrl | String | 回听地址|
|playUrl | String | 直播流地址|
|comperes | String | 主播名称|
|begIntime | String| 节目展示开始时间，如“11:00”|
|endTime | String | 节目展示结束时间，如“12:00”|
|startTime | long | 节目开始时间，单位毫秒|
|finishTime | long | 节目结束时间，单位毫秒|
|status | int | 播放状态，1-直播中，2-回放，3-未开播|
|isSubscribe| int | 是否预定节目，0-未预定，1-预定|
|desc | String | 节目简介|
|broadcastDesc | String | 广播简介|
|broadcastName | String | 广播名称|
|broadcastImg | String | 广播图片|
|icon|String|节目图标url|

<h2 id="7.17">7.17 PlayItem</h2>
播放对象，所有需要播放的东西都要转换成改对象。

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|audioId|long|单曲Id|
|title|String|单曲名称|
|playUrl|String|播放地址|
|offlineUrl|String|离线url|
|isOffline|boolean|是否离线|
|offlinePlayUrl|String|离线播放地址|
|position|int|当前播放位置|
|duration|int|单曲时长|
|totalDuration|int|单曲无片花总时长|
|audioDes|String|单曲描述|
|albumId|long|单曲所属专辑Id|
|albumPic|String|单曲所属专辑封面图|
|albumOfflinePic|String|单曲所属专辑离线封面图|
|albumName|String|单曲所属专辑名称|
|orderNum|int|单曲期数(1,2,3,4)|
|mp3PlayUrl|String|MP3播放地址|
|m3u8PlayUrl|String|m3u8播放地址|
|shareUrl|String|分享链接|
|categoryId|long|单曲类型|
|hosts|String|当前单曲主播|
|fileSize|long|单曲大小（单位Byte）|
|isLiked|int|是否喜欢（0否，1是）|
|updateTime|String|单曲更新时间|
|createTime|long|单曲创建时间|
|dataSrc|int|当前播单对象来源|
|isLivingUrl|boolean|是否是直播中url|
|aacPlayUrl32|String|32码率的播放地址|
|aacPlayUrl64|String|64码率的播放地址|
|aacPlayUrl128|String|128码率的播放地址|
|aacPlayUrl320|String|320码率的播放地址|

<h2 id="7.18">7.18 UserInfo</h2>
听伴账号用户信息

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|status|int|绑定状态，该状态可以判断<br>是否绑定/绑定是否成功/解绑是否成功|
|nickName|String|用户昵称|
|avatar|String|用户头像|
|STATUS\_IS\_BIND|int常量|表示已经绑定/绑定成功/解绑成功|
|STATUS\_IS\_UNBIND|int常量|表示没有绑定/绑定失败/解绑失败|

<h2 id="7.19">7.19 QRCodeInfo</h2>
听伴账号二维码等相关信息

|变量名 | 类型 | 描述|
|:------------ | :------------- | :------------|
|code|String|用于绑定K-radio的Code值|
|qrCodePath|String|登录二维码链接地址|
|status|String|二维码状态|
|uuid|String|用户唯一标识|
|STATUS\_NORMAL|int常量|正常状态. 等待扫描|
|STATUS\_LOSE\_EFFICACY|int常量|二维码过期，需要重新请求二维码|
|STATUS\_AUTHORIZATION|int常量|二维码已授权|

<h2 id="7.20">7.20 LiveInfoDetail</h2>
直播节目详信息

| 变量名| 类型| 描述|
| --------| :-----| :-----|
| liveName| String| 直播名称|
| liveId| long| 直播id|
| liveDesc | String| 直播描述|
| programId | long | 节目id|
| programName | String | 节目名称 |
| programDesc | String | 节目描述 |
| comperes | String | 主播 |
| liveUrl | String | 直播流地址 |
| programPic | String | 节目图片 |
| livePic | String | 直播图片 |
| timeLength | String | 字符串格式的时长 |
| startTime | long | 时间戳格式的开始时间 |
| finishTime | long | 时间戳格式的结束时间 |
| serverTime | long | 服务器时间 |
| duration | int | 毫秒时长 |
| roomId | String | 聊天室id |
| comperes Id|long | 主播Id|

<h2 id="7.21">7.21 ChatRoomTokenDetail</h2>
聊天室Token详细信息，包含用户信息

| 变量名| 类型 | 描述|
| -------- | :-----| :-----  |
| accid| long | 进入聊天室的id|
| token | String | 进入聊天室的token|
| nickName | String | 昵称 |

<h2 id="7.22">7.22 ResType</h2>
资源类型

|常量名|类型|描述|
|:----|:----|:----|
|TYPE&#95;INVALID|int|无类型|
|TYPE&#95;ALBUM|int|专辑|
|TYPE&#95;RADIO|int|电台|
|TYPE&#95;BROADCAST|int|广播|
|TYPE&#95;LIVE|int|直播|
|TYPE&#95;AUDIO|int|单曲|
|TYPE&#95;SEARCH|int|搜索结果|
|TYPE&#95;URL|int|URL|
|TYPE&#95;TOPIC|int|专题包|
|TYPE&#95;CATEGORY|int|分类|
|TYPE&#95;ALL|int|综合，包括所有类型|

<h2 id="7.23">7.23 SubscribeInfo</h2>
订阅信息类

| 变量名| 类型| 描述|
| :-------| :-----| :-----|
|id|long|订阅的专辑、电台、广播等id|
|name|String|订阅的专辑、电台、广播等名称|
|type|int|订阅的资源类型。0:专辑，3:智能电台；见[ResType](#7.22)|
|img|String|封面图片url|
|updateTime|long|最新更新时间，时间戳，毫秒|
|newNum|int|最新期数|
|newTitle|String|最新节目的标题|
|updateNum|int|一直为0|
|isOnline|int|是否在线，1表示在线|
|hasCopyright|int|是否有版权，1表示有|
|time|String|专辑更新时间|
|desc|String|描述|
|countNum|int|专辑总期数|
|comperes|String|主持人名称|

<h2 id="7.24">7.24 SceneInfo</h2>
场景信息类

| 变量名      | 类型   | 描述                                                         |
| ----------- | ------ | ------------------------------------------------------------ |
| code        | int    | 场景类型编号.10000代表可推送，10001代表不推送                |
| icon        | String | 场景icon                                                     |
| message     | String | 场景信息                                                     |
| contentId   | long   | 该场景的节目id                                               |
| contentName | String | 该场景的节目标题                                             |
| contentType | int    | 该场景的节目类型内容类型。1:专辑，2:广播，3:直播，4:智能电台。与[ResType](#7.22)不一致，为了统一类型，请使用{@link #getContentResType()} 获取类型。 |

<h3 id="7.24.1">7.24.1 AccScene</h3>
点火场景对象参数，调用场景推送接口时需要传入，该对象不需要参数。

<h3 id="7.24.2">7.24.2 SpeedScene</h3>
速度场景对象参数，调用场景推送接口时需要传入，该对象需要传入对应的速度参数。

| 变量名     | 类型   | 描述                                     |
| ---------- | ------ | ---------------------------------------- |
|type|String|速度类型。<br>`SpeedScene.TYPE_LOW_SPEED`-堵车;<br>`SpeedScene.TYPE_MEDIUM_SPEED`-中速行驶；<br>`SpeedScene.TYPE_HIGH_SPEED`-高速行驶|


<h2 id="7.25">7.25 ListeningHistory</h2>
收听历史类

| 变量名     | 类型   | 描述                                                         |
| ---------- | ------ | ------------------------------------------------------------ |
| audioId    | String | 单曲id                                                       |
| audioTitle | String | 单曲标题                                                     |
| createTime | long   | 创建时间                                                     |
| duration   | int    | 单曲时长                                                     |
| orderNum   | int    | 总期数                                                       |
| picUrl     | String | 图片地址                                                     |
| playUrl    | String | 播放地址                                                     |
| playedTime | long   | 已播时长                                                     |
| radioId    | String | 电台/专辑 id                                                 |
| radioTitle | String | 电台标题                                                     |
| shareUrl   | String | 分享链接                                                     |
| status     | int    | 节目状态                                                     |
| type       | int    | 节目类型 0专辑，1单曲，3电台，11传统广播，见[ResType](#7.22) |
| updateTime | long   | 更新时间                                                     |

<h2 id="7.26">7.26 BrandDetails</h2>
品牌信息类

| 变量名        | 类型   | 描述         |
| ------------- | ------ | ------------ |
| brand         | String | 品牌名       |
| logo          | String | 品牌logo地址 |
| userAgreement | String | 服务协议地址 |

<h2 id="7.27">7.27 SoundQuality</h2>
音质常量

| 常量名           | 类型 | 描述     |
| ---------------- | ---- | -------- |
| LOW_QUALITY      | int  | 低品质   |
| STANDARD_QUALITY | int  | 标准品质 |
| HIGH_QUALITY     | int  | 高品质   |
| HIGHER_QUALITY   | int  | 较高品质 |




<h1 id="8">8 工具类</h1>
--

<h2 id="8.1">8.1 OperationAssister</h2>
该工具类主要用来处理运营接口返回的成员Bean对象，根据传入的成员对象获取对应的属性。

<h3 id="8.1.1">8.1.1 获取成员的Id</h3>
获取栏目成员的id  

`OperationAssister.getId(ColumnMember member)`

获取分类成员的id   

`OperationAssister.getId(CategoryMember member)`

<h3 id="8.1.2">8.1.2 获取收听数</h3>
只有分类成员的部分类型有收听数，不支持收听数的返回0。

`OperationAssister.getListenNum(CategoryMember member)`

<h3 id="8.1.3">8.1.3 获取成员类型</h3>
对应类型见资源类型[ResType](#7.22)

获取分类成员的类型  

`OperationAssister.getType(CategoryMember member)`

获取栏目成员类型  

`OperationAssister.getType(ColumnMember member)`

<h3 id="8.1.4">8.1.4 获取图标和封面</h3>
返回图标或封面的url地址或“”。

获取分类成员的图标  

`OperationAssister.getIcon(CategoryMember member)`

获取栏目成员的图标  

`OperationAssister.getIcon(ColumnMember member)`

获取分类成员的封面  

`OperationAssister.getCover(CategoryMember member)`

获取栏目成员的封面  

`OperationAssister.getCover(ColumnMember member)`

获取分类成员的图片，如果没有Icon就返回Cover

`OperationAssister.getImage(CategoryMember member)`

获取栏目成员的图片，如果没有Icon就返回cover

`OperationAssister.getImage(ColumnMember member)`

<h2 id="8.2">8.2 BeanUtil</h2>
该工具类主要用来Bean转换

<h3 id="8.2.1">8.2.1 单曲转PlayItem</h3>
将单曲转换为PlayItem用于播放。

专辑/PGC单曲转换为播放item

`BeanUtil.translateToPlayItem(AudioDetails audioDetails)`

广播节目单曲转换成播放item

`BeanUtil.translateToPlayItem(ProgramDetails details)`

<h2 id="8.3">8.3 Logging</h2>
用于控制log的打印，需要在初始化之前进行调用。

开启全局log

```java
Logging.setDebug(true);//true表示开启log，false表示关闭

```

网络请求信息log。需要在全局log都开启的情况下才有效  
开启所有请求信息RequestLevel.ALL；  
关闭RequestLevel.NONE；  
只打印请求信息RequestLevel.REQUEST；  
只打印响应信息RequestLevel.RESPONSE；

```java
Logging.setRequestLevel(RequestLevel.ALL);

```

可以实现`Printer`接口来实现自己的打印方式   

```java
Logging.printer(new CustomPrinter());

```

<h1 id="附录">附录</h1>
<h2 id="1000">更新说明</h2>

<h3>版本：v1.5.5 2020-07-10</h3>

--

<h4>Bug 修复</h4>

* 修复激活时可能导致死锁的问题。
* 修复内存泄露问题。
* 其他已知Bug修复。
* 修改仓库地址，原仓库有被墙的风险。


<h3>版本：v1.5.4 2019-12-03</h3>

--

<h4>优化</h4>
* 优化IJK播放器底层逻辑。

### 版本：v1.5.3 2019-10-28

--

#### 新功能

- 新增直播主持人Id字段
- 新增获取热词列表的接口
- 新增同时获取多级子分类接口
- 新增设置车型（CarType）方式。
- 新增广播可订阅、取消订阅。

#### Bug修复

- 修正一键播放拉取clockId获取的值
- 修正搜索结果字段类型。
- 修复打印log设置tag的Bug。
- 调整deviceId逻辑。
- 修复其他已知Bug。

### 版本：v1.5.2 2019-07-30

--

#### Bug修复

- 修正播放智能电台的功能逻辑。

### 版本：v1.5.1 2019-07-15

--

#### Bug修复

* 修复播放器特定情况下由于状态不一致导致播放失败问题。

### 版本：v1.5.0 2019-06-21

--

#### 新功能

- 新增运营相关的八个接口，原有接口已废弃，但仍可使用。
- 新增关键词搜索。
- 新增获取联想词。
- 网络请求超时时间修改为10秒。
- 新增获取二维码时可以传入回调地址。
- 新增播放器设置音质功能。
- 单曲新增编排内容类型相关字段。

#### Bug修复

- 修复数据上报相关崩溃。
- 修复特定情况下播放器无法播放的问题。
- 修复初始化后直接激活可能会崩溃


### 版本：v1.4.0 2019-03-31

--

#### 新功能

- 新增收听历史相关接口。
- 新增获取品牌相关信息。
- 新增网络请求可以绑定rxlifecycle或tag取消请求。
- 新增一键播放订阅功能。
- 新增场景推送功能。
- 账号体系修改。
- 添加单曲转PlayItem工具类。
- 添加可以显示log。

#### Bug修复

- 修正网络请求错误码和错误信息。
- 修复跑Monkey可能引起OOM的问题。


### 版本：v1.3.1 2019-01-17

--

#### 新功能

- 添加判断是否已经激活接口。
- 广播栏目成员添加广播频率。

#### Bug修复

- 修复跑monkey时可能出现死锁。
- fastjson替换成Gson，以解决内存泄露问题。
- 修复播放专辑上一页一直是第一页的问题。

### 版本：v1.3.0 2018-11-07

--

#### 新功能

- 新增获取当前报时单曲接口。
- 新增可以根据地区或地区编码获取PGC播单。
- 新增语音搜索接口，去掉暂不支持的参数。
- 新增播放器音频焦点变化回调和开关。
- 新增第三方账号打通接口。
- 新增运营接口的Bean操作辅助类OperationAssister。

#### Bug修复

- 修复SDK初始化可能会崩溃。
- 修复QQ音乐Token失效不自动刷新。
- 修改SDK激活逻辑，有异常回调异常，返回数据有问题返回false。

### 版本：v1.2.1 2018-10-29

--

#### Bug修复

- 修复播放器在Android O以上版本兼容问题。

### 版本：v1.2.0 2018-10-19

--

#### 新功能

- 新增直播功能，包括接口和播放器相关功能。

### 版本：v1.1.0 2018-9-26

--

#### 新功能

- 新增听伴账号登录绑定等相关功能。
- 新增QQ音乐登录等相关接口。
- 修改运营接口相关Bean类的Integer、Long为int、long。
- 更新考拉SDK版本到2.2.4。

#### Bug修复

- 修复token未到期失效问题。
- 优化sign参数生成时机。

