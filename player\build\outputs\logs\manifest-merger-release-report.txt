-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml:1:1-2:44
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml:1:1-2:44
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml:1:1-2:44
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml:1:1-2:44
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml:1:1-2:44
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml:1:1-2:44
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml:1:1-2:44
	package
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml:2:5-41
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml:1:1-2:44
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml:1:1-2:44
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml:1:11-69
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\player\src\main\AndroidManifest.xml
