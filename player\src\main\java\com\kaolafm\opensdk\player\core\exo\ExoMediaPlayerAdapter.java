//package com.kaolafm.opensdk.player.core.exo;
//
//import android.content.Context;
//
//import com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener;
//import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
//import com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener;
//import com.kaolafm.opensdk.player.core.model.AMediaPlayer;
//
///**
// * <AUTHOR> on 2019-05-24.
// */
//
//public class ExoMediaPlayerAdapter extends AMediaPlayer {
//    private ExoMediaPlayer mediaPlayer;
//
//    public ExoMediaPlayerAdapter(Context context) {
//        mediaPlayer = new ExoMediaPlayer(context);
//    }
//
//    @Override
//    public long getDuration() {
//        return 0;
//    }
//
//    @Override
//    public long getCurrentPosition() {
//        return 0;
//    }
//
//    @Override
//    public boolean isPlaying() {
//        return false;
//    }
//
//    @Override
//    public void pause() {
//
//    }
//
//    @Override
//    public void play() {
//
//    }
//
//    @Override
//    public void preload(String url) {
//
//    }
//
//    @Override
//    public void reset() {
//
//    }
//
//    @Override
//    public void release() {
//
//    }
//
//    @Override
//    public void prepare() {
//
//    }
//
//    @Override
//    public void prepare(int needSeek) {
//
//    }
//
//    @Override
//    public void seek(long msec) {
//
//    }
//
//    @Override
//    public void setDataSource(String source) {
//
//    }
//
//    @Override
//    public void start(String source) {
//        mediaPlayer.start(source);
//    }
//
//    @Override
//    public void start(String url, long position) {
//
//    }
//
//    @Override
//    public void stop() {
//
//    }
//
//    @Override
//    public void setPlayerStateListener(IPlayerStateCoreListener listener) {
//
//    }
//
//    @Override
//    public void setBufferProgressListener(IPlayerBufferProgressListener progressListener) {
//
//    }
//
//    @Override
//    public void setInitPlayerInitCompleteListener(IPlayerInitCompleteListener initPlayerInitCompleteListener) {
//
//    }
//
//    @Override
//    public void setPlayRatio(float ratio) {
//
//    }
//}
