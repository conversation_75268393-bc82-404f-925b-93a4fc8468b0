<?xml version="1.0" encoding="UTF-8"?>
<metadata>
  <groupId>com.kaolafm</groupId>
  <artifactId>open-sdk</artifactId>
  <versioning>
    <latest>3.0.6.xiaopeng8.test-SNAPSHOT</latest>
    <versions>
      <version>exo_alpha9.5.test_SGM557-SNAPSHOT</version>
      <version>exo_alpha9.6.test_SGM557-SNAPSHOT</version>
      <version>1.6.0.68.test01.ford-SNAPSHOT</version>
      <version>1.6.0.69.ford-SNAPSHOT</version>
      <version>1.6.0.70.ford-SNAPSHOT</version>
      <version>1.6.0.71.ford-SNAPSHOT</version>
      <version>1.6.0.72.ford-SNAPSHOT</version>
      <version>1.8.0.23-SNAPSHOT</version>
      <version>********.huayang_t1n_t1h.2-SNAPSHOT</version>
      <version>3.0.3-test-SNAPSHOT</version>
      <version>3.0.5.nvccs.01-SNAPSHOT</version>
      <version>3.0.5.nvccs.02-SNAPSHOT</version>
      <version>3.0.6-SNAPSHOT</version>
      <version>3.0.6.ford.6-SNAPSHOT</version>
      <version>3.0.6.xiaopeng2-SNAPSHOT</version>
      <version>3.0.6.xiaopeng3-SNAPSHOT</version>
      <version>3.0.7-SNAPSHOT</version>
      <version>3.0.7.1-SNAPSHOT</version>
      <version>3.0.8.benchi.test.1-SNAPSHOT</version>
      <version>3.0.8.exo_alpha5-SNAPSHOT</version>
      <version>3.0.8.exo_alpha9_text_pre__SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_alpha9_text_pre2_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_alpha9_text_pre_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_alpha9.1.test_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_alpha9.2.test_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_alpha9.3.test_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_alpha9.4.test_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_alpha9.7-test_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_alpha13_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_alpha14_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_alpha14.1_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_alpha14.2_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_alpha17.1_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_alpha18_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_benchi3-SNAPSHOT</version>
      <version>3.0.8.pre3-SNAPSHOT</version>
      <version>3.0.9.haha.1-SNAPSHOT</version>
      <version>3.0.9.pre1-SNAPSHOT</version>
      <version>3.0.9.pre2-SNAPSHOT</version>
      <version>3.0.9.pre7.test-SNAPSHOT</version>
      <version>3.0.9.pre8-SNAPSHOT</version>
      <version>3.0.9.pre8.qirui-SNAPSHOT</version>
      <version>3.0.10.pre1.pipi.2-SNAPSHOT</version>
      <version>3.0.10.pre3-SNAPSHOT</version>
      <version>********.huayang_t1n_t1h.3-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha24_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha25_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha26_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha27_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha28_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha29_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha30_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha31_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha32_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha33_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha34_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha36_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha37_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha38_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha39_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha40_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha41_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha42_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha43_SGM557-SNAPSHOT</version>
      <version>3.0.10.pre8.lhx1-SNAPSHOT</version>
      <version>3.0.6.ford.8.temp1-SNAPSHOT</version>
      <version>3.0.6.ford.8.temp2-SNAPSHOT</version>
      <version>3.0.6.ford.8.temp3-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha44_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha45_SGM557-SNAPSHOT</version>
      <version>3.0.9.exo.1-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha46_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha47_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha48_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha49_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha50_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha51_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha52_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha53_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha54_SGM557-SNAPSHOT</version>
      <version>3.0.8.exo_test_alpha55_SGM557-SNAPSHOT</version>
      <version>3.0.6.ford.10_test-SNAPSHOT</version>
      <version>3.0.11.pre11.temp1-SNAPSHOT</version>
      <version>3.0.11.pre11.litest2-SNAPSHOT</version>
      <version>3.0.11.fengtian.pre8_test-SNAPSHOT</version>
      <version>3.0.11.fengtian.pre8_test2-SNAPSHOT</version>
      <version>3.0.11.fengtian.pre8_test3-SNAPSHOT</version>
      <version>1.6.0.73.pre1.ford-SNAPSHOT</version>
      <version>3.0.11.pre20-SNAPSHOT</version>
      <version>3.0.6.xiaopeng6-SNAPSHOT</version>
      <version>3.0.6.xiaopeng7.test-SNAPSHOT</version>
      <version>3.0.6.xiaopeng8.test-SNAPSHOT</version>
      <version>1.6.0.73.pre2.ford-SNAPSHOT</version>
    </versions>
    <lastUpdated>20250822062516</lastUpdated>
  </versioning>
</metadata>
