//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.kaolafm.opensdk.api.subscribe;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;
import com.kaolafm.opensdk.ResType;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;

/***
 * 订阅信息类。
 */
@Entity
public class SubscribeInfo implements Parcelable {

    /** 订阅的专辑、电台、在线广播等id*/
    @SerializedName("id")
    private long id;

    /** 订阅的专辑、电台、在线广播等名称*/
    @SerializedName("name")
    private String name;

    /** 订阅的资源类型。0：专辑，3：AI电台，5：单曲，11：在线广播 */
    @SerializedName("type")
    private int type;

    /** 封面图片url*/
    @SerializedName("img")
    private String img;

    /** 最新更新时间，时间戳，毫秒*/
    @SerializedName("updateTime")
    private long updateTime;

    /** 更新期数*/
    @SerializedName("newNum")
    private int newNum;

    /** 最新节目的标题*/
    @SerializedName("newTitle")
    private String newTitle;

    @Deprecated
    /** 一直为0，不需要关心*/
    @SerializedName("updateNum")
    private int updateNum;

    /** 是否在线，1表示在线*/
    @SerializedName("isOnline")
    private int isOnline;

    @Deprecated
    /** 是否有版权，1表示有*/
    @SerializedName("hasCopyright")
    private int hasCopyright;

    @Deprecated
    /** 专辑更新时间*/
    @SerializedName("time")
    private String time;

    /** 描述*/
    @SerializedName("desc")
    private String desc;

    /** 专辑总期数*/
    @SerializedName("countNum")
    private int countNum;

    /** 主持人名称*/
    @SerializedName("comperes")
    private String comperes;

    @SerializedName("freq")
    private String freq;

    @SerializedName("playCount")
    private int playCount;

    /** 是否vip 1:是,0:否 */
    @SerializedName("vip")
    private int vip;
    
    /** 是否精品 1:是,0:否 */
    @SerializedName("fine")
    private int fine;

    @SerializedName("createdTime")
    private Long createdTime;

    @Deprecated
    @SerializedName("albumName")
    private String albumName;

    public SubscribeInfo() {
    }

    public Long getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }

    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public String getAlbumName() {
        return albumName;
    }

    public void setAlbumName(String albumName) {
        this.albumName = albumName;
    }

    public int getPlayCount() {
        return playCount;
    }

    public void setPlayCount(int playCount) {
        this.playCount = playCount;
    }

    public long getId() {
        return this.id;
    }

    public void setId(long var1) {
        this.id = var1;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String var1) {
        this.name = var1;
    }

    public int getType() {
        return this.type;
    }

    public void setType(int var1) {
        this.type = var1;
    }

    /**
     * 获取对应的{@link ResType}类型，与其进行类型统一。
     * @return
     */
    public int getResType() {//0：专辑，3：AI电台，5：单曲，11：在线广播
        switch (type) {
            case 5:
                return ResType.TYPE_AUDIO;
            case 0:
                return ResType.TYPE_ALBUM;
            case 3:
                return ResType.TYPE_RADIO;
            case 11:
                return ResType.TYPE_BROADCAST;
            default:
        }
        return type;
    }

    public String getImg() {
        return this.img;
    }

    public void setImg(String var1) {
        this.img = var1;
    }

    public long getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(long var1) {
        this.updateTime = var1;
    }

    public int getNewNum() {
        return this.newNum;
    }

    public void setNewNum(int var1) {
        this.newNum = var1;
    }

    public String getNewTitle() {
        return this.newTitle;
    }

    public void setNewTitle(String var1) {
        this.newTitle = var1;
    }

    public int getUpdateNum() {
        return this.updateNum;
    }

    public void setUpdateNum(int var1) {
        this.updateNum = var1;
    }

    public int getIsOnline() {
        return this.isOnline;
    }

    public void setIsOnline(int var1) {
        this.isOnline = var1;
    }

    public int getHasCopyright() {
        return this.hasCopyright;
    }

    public void setHasCopyright(int var1) {
        this.hasCopyright = var1;
    }

    public String getTime() {
        return this.time;
    }

    public void setTime(String var1) {
        this.time = var1;
    }

    public String getDesc() {
        return this.desc;
    }

    public void setDesc(String var1) {
        this.desc = var1;
    }

    public int getCountNum() {
        return this.countNum;
    }

    public void setCountNum(int var1) {
        this.countNum = var1;
    }

    public String getComperes() {
        return this.comperes;
    }

    public void setComperes(String var1) {
        this.comperes = var1;
    }

    public int getFine() {
        return fine;
    }

    public void setFine(int fine) {
        this.fine = fine;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(this.id);
        dest.writeString(this.name);
        dest.writeInt(this.type);
        dest.writeString(this.img);
        dest.writeLong(this.updateTime);
        dest.writeInt(this.newNum);
        dest.writeString(this.newTitle);
        dest.writeInt(this.updateNum);
        dest.writeInt(this.isOnline);
        dest.writeInt(this.hasCopyright);
        dest.writeString(this.time);
        dest.writeString(this.desc);
        dest.writeInt(this.countNum);
        dest.writeString(this.comperes);
        dest.writeInt(fine);
        dest.writeInt(vip);
        dest.writeInt(playCount);
        dest.writeString(albumName);
        dest.writeString(freq);
    }

    protected SubscribeInfo(Parcel in) {
        this.id = in.readLong();
        this.name = in.readString();
        this.type = in.readInt();
        this.img = in.readString();
        this.updateTime = in.readLong();
        this.newNum = in.readInt();
        this.newTitle = in.readString();
        this.updateNum = in.readInt();
        this.isOnline = in.readInt();
        this.hasCopyright = in.readInt();
        this.time = in.readString();
        this.desc = in.readString();
        this.countNum = in.readInt();
        this.comperes = in.readString();
        fine = in.readInt();
        vip = in.readInt();
        playCount = in.readInt();
        albumName = in.readString();
        freq = in.readString();
    }

    @Generated(hash = 156414168)
    public SubscribeInfo(long id, String name, int type, String img, long updateTime, int newNum, String newTitle,
            int updateNum, int isOnline, int hasCopyright, String time, String desc, int countNum,
            String comperes, String freq, int playCount, int vip, int fine, Long createdTime, String albumName) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.img = img;
        this.updateTime = updateTime;
        this.newNum = newNum;
        this.newTitle = newTitle;
        this.updateNum = updateNum;
        this.isOnline = isOnline;
        this.hasCopyright = hasCopyright;
        this.time = time;
        this.desc = desc;
        this.countNum = countNum;
        this.comperes = comperes;
        this.freq = freq;
        this.playCount = playCount;
        this.vip = vip;
        this.fine = fine;
        this.createdTime = createdTime;
        this.albumName = albumName;
    }

    public static final Parcelable.Creator<SubscribeInfo> CREATOR = new Parcelable.Creator<SubscribeInfo>() {
        @Override
        public SubscribeInfo createFromParcel(Parcel source) {
            return new SubscribeInfo(source);
        }

        @Override
        public SubscribeInfo[] newArray(int size) {
            return new SubscribeInfo[size];
        }
    };
}
