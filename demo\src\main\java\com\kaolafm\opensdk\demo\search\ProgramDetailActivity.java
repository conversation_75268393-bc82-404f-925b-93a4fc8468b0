package com.kaolafm.opensdk.demo.search;

import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.util.Log;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.google.gson.GsonBuilder;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.search.model.Compere;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.api.search.model.VoiceSearchProgramBean;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.demo.player.AlbumPlayerActivity;
import com.kaolafm.opensdk.demo.player.AudioPlayerActivity;
import com.kaolafm.opensdk.demo.player.BasePlayerActivity;
import com.kaolafm.opensdk.demo.player.BroadcastPlayerActivity;
import com.kaolafm.opensdk.demo.player.RadioPlayerActivity;

import java.util.List;

import butterknife.BindView;

/**
 * <AUTHOR> Yan
 * @date 2018/8/7
 */

public class ProgramDetailActivity extends BaseActivity {
    public static final String KEY_PROGRAM = "Program";
    public static final String KEY_VOICE_PROGRAM = "voiceProgram";

    @BindView(R.id.ll_program_detail_root)
    LinearLayout mLlProgramDetailRoot;

    @BindView(R.id.tv_program_detail_info)
    TextView mTvProgramDetailInfo;

    private SearchProgramBean mProgram;
    private VoiceSearchProgramBean mVoiceProgram;

    @Override
    public int getLayoutId() {
        return R.layout.activity_program_detail;
    }

    @Override
    public void initArgs() {
        Intent intent = getIntent();
        if (intent != null) {
            mProgram = intent.getParcelableExtra(KEY_PROGRAM);
            mVoiceProgram = intent.getParcelableExtra(KEY_VOICE_PROGRAM);
        }
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("搜索结果节目详情页");
        mTvProgramDetailInfo.setOnClickListener(v -> {
            int type = -1;
            String id = "";
            if(mProgram != null){
                type = mProgram.getType();
                id = String.valueOf(mProgram.getId());
            }
            if(mVoiceProgram != null){
                type = mVoiceProgram.getType();
                id = String.valueOf(mVoiceProgram.getId());
            }
            Intent intent = new Intent();
            intent.putExtra(BasePlayerActivity.KEY_ID, id);
            Class clazz = null;
            switch (type) {
                case ResType.TYPE_ALBUM:
                    clazz = AlbumPlayerActivity.class;
                    break;
                case ResType.TYPE_AUDIO:
                    clazz = AudioPlayerActivity.class;
                    break;
                case ResType.TYPE_RADIO:
                    clazz = RadioPlayerActivity.class;
                    break;
                case ResType.TYPE_BROADCAST:
                    clazz = BroadcastPlayerActivity.class;
                    break;
                default:
            }
            if (clazz != null) {
                intent.setClass(ProgramDetailActivity.this, clazz);
                startActivity(intent);
            }
        });
    }

    @Override
    public void initData() {
        if(mProgram != null){
            Log.i("SEARCH, mProgram :", mProgram.toString());
            showInfo(mProgram);
        }
        if(mVoiceProgram != null){
            Log.i("SEARCH, mVoiceProgram :", mVoiceProgram.toString());
            showInfo(mVoiceProgram);
        }
    }

    private void showInfo(SearchProgramBean searchProgramBean) {
        Glide.with(this).load(searchProgramBean.getImg()).into(new SimpleTarget<Drawable>() {
            @Override
            public void onResourceReady(@NonNull Drawable resource,
                    @Nullable Transition<? super Drawable> transition) {
                mTvProgramDetailInfo.setCompoundDrawablesWithIntrinsicBounds(null, resource, null, null);
            }
        });
        String infoStr = new GsonBuilder()
                .setPrettyPrinting()
                .serializeNulls()
                .create().toJson(searchProgramBean);
        mTvProgramDetailInfo.setText(infoStr);
//        StringBuilder sb = new StringBuilder();
//        //来源 编码+名称
//        sb.append("ID:").append(searchProgramBean.getId()).append("\r\n")
//                .append("名称:").append(searchProgramBean.getName()).append("\r\n")
//                //0为专辑;1为单曲;11为在线广播;3为电台;1003,qq音乐场景电台;1004,qq音乐标签电台
//                .append("类型:").append(getType(searchProgramBean.getType())).append("\r\n")
//                .append("专辑名称:").append(searchProgramBean.getAlbumName()).append("\r\n")
//                .append("时长:").append(searchProgramBean.getDuration()).append("毫秒").append("\r\n")
//                .append("播放地址:").append(searchProgramBean.getPlayUrl()).append("\r\n")
//                .append("收听人数:").append(searchProgramBean.getListenNum()).append("\r\n")
//                .append("是否付费:").append(searchProgramBean.getFine()==1?"是":"否").append("\r\n")
//                .append("是否会员:").append(searchProgramBean.getVip()==1?"是":"否").append("\r\n")
//                .append("是否试听:").append(searchProgramBean.getAudition()==1?"是":"否");
//        mTvProgramDetailInfo.setText(sb);
//        List<Compere> comperes = searchProgramBean.getComperes();
//        if (!ListUtil.isEmpty(comperes)) {
//            sb.setLength(0);
////            for (int i = 0, size = comperes.size(); i < size; i++) {
////                SearchProgramBean.Compere compere = comperes.get(i);
////                sb.append("主持人").append(i).append(":").append("\r\n")
////                        /*.append("姓名:").append(compere.getName()).append("\r\n")
////                        .append("描述：").append(compere.getDes()).append("\r\n")*/;
////                TextView imageText = new TextView(this);
////                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
////                        LinearLayout.LayoutParams.WRAP_CONTENT);
////                imageText.setLayoutParams(layoutParams);
////                imageText.setText(sb);
////                mLlProgramDetailRoot.addView(imageText);
////            }
//        }
    }

    private void showInfo(VoiceSearchProgramBean voiceSearchProgramBean) {
        Glide.with(this).load(voiceSearchProgramBean.getImg()).into(new SimpleTarget<Drawable>() {
            @Override
            public void onResourceReady(@NonNull Drawable resource,
                                        @Nullable Transition<? super Drawable> transition) {
                mTvProgramDetailInfo.setCompoundDrawablesWithIntrinsicBounds(null, resource, null, null);
            }
        });
        String infoStr = new GsonBuilder()
                .setPrettyPrinting()
                .serializeNulls()
                .create().toJson(voiceSearchProgramBean);
        mTvProgramDetailInfo.setText(infoStr);
//        StringBuilder sb = new StringBuilder();
//        //来源 编码+名称
//        sb.append("ID:").append(searchProgramBean.getId()).append("\r\n")
//                .append("名称:").append(searchProgramBean.getName()).append("\r\n")
//                //0为专辑;1为单曲;11为在线广播;3为电台;1003,qq音乐场景电台;1004,qq音乐标签电台
//                .append("类型:").append(getType(searchProgramBean.getType())).append("\r\n")
//                .append("专辑名称:").append(searchProgramBean.getAlbumName()).append("\r\n")
//                .append("时长:").append(searchProgramBean.getDuration()).append("毫秒").append("\r\n")
//                .append("播放地址:").append(searchProgramBean.getPlayUrl()).append("\r\n")
//                .append("收听人数:").append(searchProgramBean.getListenNum()).append("\r\n")
//                .append("是否付费:").append(searchProgramBean.getFine()==1?"是":"否").append("\r\n")
//                .append("是否会员:").append(searchProgramBean.getVip()==1?"是":"否").append("\r\n")
//                .append("是否试听:").append(searchProgramBean.getAudition()==1?"是":"否");
//        mTvProgramDetailInfo.setText(sb);
//        List<Compere> comperes = searchProgramBean.getComperes();
//        if (!ListUtil.isEmpty(comperes)) {
//            sb.setLength(0);
////            for (int i = 0, size = comperes.size(); i < size; i++) {
////                SearchProgramBean.Compere compere = comperes.get(i);
////                sb.append("主持人").append(i).append(":").append("\r\n")
////                        /*.append("姓名:").append(compere.getName()).append("\r\n")
////                        .append("描述：").append(compere.getDes()).append("\r\n")*/;
////                TextView imageText = new TextView(this);
////                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
////                        LinearLayout.LayoutParams.WRAP_CONTENT);
////                imageText.setLayoutParams(layoutParams);
////                imageText.setText(sb);
////                mLlProgramDetailRoot.addView(imageText);
////            }
//        }
    }

    private String getType(int type) {
        switch (type) {
            case 0:
                return "专辑";
            case 1:
                return "单曲";
            case 3:
                return "电台";
            case 11:
                return "在线广播";
            case 1003:
                return "QQ音乐场景电台";
            case 1004:
                return "QQ音乐标签电台";
            default:

        }
        return "";
    }

}
