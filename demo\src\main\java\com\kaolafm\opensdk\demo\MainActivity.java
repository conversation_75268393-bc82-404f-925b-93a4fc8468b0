package com.kaolafm.opensdk.demo;

import android.Manifest;
import android.Manifest.permission;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.content.res.TypedArray;
import android.location.Criteria;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Bundle;
import android.support.v4.app.ActivityCompat;
import android.support.v7.app.AlertDialog;
import android.support.v7.app.AlertDialog.Builder;
import android.support.v7.widget.DividerItemDecoration;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.text.Editable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.EditText;
import android.widget.LinearLayout;

import com.kaolafm.ad.AdvertOptions;
import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.expose.AdvertisingLifecycleCallback;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ad.timer.TimedAdvertManager;
import com.kaolafm.opensdk.OpenSDK;
import com.kaolafm.opensdk.Options;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.api.media.AudioRequest;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.demo.account.LinkAccountActivity;
import com.kaolafm.opensdk.demo.activity.ActivitiesActivity;
import com.kaolafm.opensdk.demo.brandinfo.BrandInfoActivity;
import com.kaolafm.opensdk.demo.player.BasePlayerActivity;
import com.kaolafm.opensdk.demo.player.BroadcastPlayerGetLocalByIdActivity;
import com.kaolafm.opensdk.demo.player.RadioPlayerGetListByAreaActivity;
import com.kaolafm.opensdk.demo.purchase.AlbumQRCodeActivity;
import com.kaolafm.opensdk.demo.purchase.AudiosQRCodeActivity;
import com.kaolafm.opensdk.demo.purchase.OrderActivity;
import com.kaolafm.opensdk.demo.purchase.PurchasedActivity;
import com.kaolafm.opensdk.demo.purchase.QRCodeStatusActivity;
import com.kaolafm.opensdk.demo.purchase.VipQRCodeActivity;
import com.kaolafm.opensdk.demo.detail.DetailActivity;
import com.kaolafm.opensdk.demo.history.HistoryActivity;
import com.kaolafm.opensdk.demo.login.KaolaLoginActivity;
import com.kaolafm.opensdk.demo.operation.category.CategoryActivity;
import com.kaolafm.opensdk.demo.operation.column.ColumnActivity;
import com.kaolafm.opensdk.demo.personalise.InterestActivity;
import com.kaolafm.opensdk.demo.personalise.UserActivity;
import com.kaolafm.opensdk.demo.player.AlbumPlayerActivity;
import com.kaolafm.opensdk.demo.player.AudioPlayerActivity;
import com.kaolafm.opensdk.demo.player.BroadcastPlayerActivity;
import com.kaolafm.opensdk.demo.player.RadioPlayerActivity;
import com.kaolafm.opensdk.demo.purchase.VipMealsActivity;
import com.kaolafm.opensdk.demo.qqmusic.CollectionActivity;
import com.kaolafm.opensdk.demo.scene.SceneActivity;
import com.kaolafm.opensdk.demo.search.KeywordSearchActivity;
import com.kaolafm.opensdk.demo.search.VoiceSearchActivity;
import com.kaolafm.opensdk.demo.subcribe.SubscribeActivity;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.LogLevel;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.utils.BaseHttpsStrategy;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.listener.IReportInitListener;
import com.tbruyelle.rxpermissions2.RxPermissions;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import io.reactivex.android.schedulers.AndroidSchedulers;
import okhttp3.Interceptor;
import okhttp3.Response;

public class MainActivity extends BaseActivity {
    /**
     * 权限管理
     */
    private RxPermissions mRxPermissions;
    @BindView(R.id.rv_main)
    RecyclerView mRvMain;

    private CategoryAdapter mAdapter;

    private double mLatitude;

    private double mLongitude;

    private boolean isInitialized = false;

//    private boolean isActivated = false;
    private AdvertisingLifecycleCallback mLifecycleCallback;

    public class defaultHttpsStrategy extends BaseHttpsStrategy {
        @Override
        public void updateChannelHttpsStrategy() {
        }
    }
    defaultHttpsStrategy mDemoStrategy = new defaultHttpsStrategy();

    @Override
    public int getLayoutId() {
        return R.layout.activity_main;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        mBackView.setVisibility(View.GONE);
        mRxPermissions = new RxPermissions(MainActivity.this);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        mRvMain.setLayoutManager(linearLayoutManager);
        mAdapter = new CategoryAdapter();
        mAdapter.setOnItemClickListener((view, viewType, s, position) -> {
            if (viewType == FunctionItem.TYPE_CHILD) {
                startPage(s.getName());
            }
        });
        mRvMain.setAdapter(mAdapter);
        mRvMain.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
        setTitle("所有功能列表");
        //设置log显示
        Logging.setDebug(true);
        Logging.setRequestLevel(LogLevel.RequestLevel.ALL);
//        Logging.printer(new EfficientPrinter());
    }

    private List<FunctionItem> getAllFunctionData() {
        String[] titles = getResources().getStringArray(R.array.main_list);
        TypedArray typedArray = getResources().obtainTypedArray(R.array.child_index);
        int[] reses = getResources().getIntArray(R.array.res_type);
        ArrayList<FunctionItem> functionList = new ArrayList<>();
        for (int i = 0, size = titles.length; i < size; i++) {
            FunctionItem functionItem = new FunctionItem(titles[i], FunctionItem.TYPE_TITLE, ResType.TYPE_INVALID);
            functionList.add(functionItem);
            int resourceId = typedArray.getResourceId(i, 0);
            String[] childs = getResources().getStringArray(resourceId);
            for (int j = 0; j < childs.length; j++) {
                FunctionItem childItem = new FunctionItem(childs[j], FunctionItem.TYPE_CHILD, reses[j]);
                functionList.add(childItem);
            }
        }
        typedArray.recycle();
        return functionList;
    }

    @Override
    public void initData() {
        requestPermissions();
        getLngAndLat(MainActivity.this);
        mAdapter.setDataList(getAllFunctionData());
        ReportHelper.getInstance().setIReportInitListener(new IReportInitListener() {
            @Override
            public void initComplete() {
                ReportHelper.getInstance().addAppStart("1");
            }
        });
    }

    private void requestPermissions() {
        mRxPermissions = new RxPermissions(this);
        mRxPermissions.request(Manifest.permission.CALL_PHONE, Manifest.permission.READ_PHONE_STATE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(isGranted -> {
                        }
                );
    }


    /**
     * 获取经纬度
     */
    private void getLngAndLat(Context context) {
        mLatitude = 0.0;
        mLongitude = 0.0;
        LocationManager locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
        if (locationManager == null) {
            return;
        }
        //从gps获取经纬度
        if (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
            Logging.d("MainActivity", "getLngAndLat: GPS获取经纬度");
            if (ActivityCompat.checkSelfPermission(this, permission.ACCESS_FINE_LOCATION)
                    != PackageManager.PERMISSION_GRANTED
                    && ActivityCompat.checkSelfPermission(this, permission.ACCESS_COARSE_LOCATION)
                    != PackageManager.PERMISSION_GRANTED) {
                Logging.Log.e("MainActivity", "getLngAndLat: ");
                return;
            }
            Location location = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER);
            if (location != null) {
                mLatitude = location.getLatitude();
                mLongitude = location.getLongitude();
            } else {//当GPS信号弱没获取到位置的时候又从网络获取
                getLngAndLatWithNetwork(locationManager);
            }
        } else {    //从网络获取经纬度
            getLngAndLatWithNetwork(locationManager);
        }
    }

    //从网络获取经纬度
    public void getLngAndLatWithNetwork(LocationManager locationManager) {
        //上海
        mLatitude = 30.0d;// 39.90960456;
        mLongitude = 120.0d;//116.39722824;
        Criteria criteria = new Criteria();
        criteria.setAccuracy(Criteria.ACCURACY_FINE);
        criteria.setAltitudeRequired(false);
        criteria.setBearingRequired(false);
        criteria.setCostAllowed(true);
        criteria.setPowerRequirement(Criteria.POWER_LOW);
        String provider = locationManager.getBestProvider(criteria, true);
        if (ActivityCompat.checkSelfPermission(this, permission.ACCESS_FINE_LOCATION)
                != PackageManager.PERMISSION_GRANTED
                && ActivityCompat.checkSelfPermission(this, permission.ACCESS_COARSE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            // TODO: Consider calling
            //    ActivityCompat#requestPermissions
            // here to request the missing permissions, and then overriding
            //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
            //                                          int[] grantResults)
            // to handle the case where the user grants the permission. See the documentation
            // for ActivityCompat#requestPermissions for more details.
            return;
        }
        locationManager.requestLocationUpdates(provider, 1000, 0, locationListener);
        Location location = locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER);
        Logging.e("MainActivity", "getLngAndLatWithNetwork: 网络获取经纬度，location=" + location + ", provider=" + provider);
        if (location != null) {
            mLatitude = location.getLatitude();
            mLongitude = location.getLongitude();
        }
    }

    LocationListener locationListener = new LocationListener() {

        // Provider的状态在可用、暂时不可用和无服务三个状态直接切换时触发此函数
        @Override
        public void onStatusChanged(String provider, int status, Bundle extras) {
            Logging.e("MainActivity", "onStatusChanged: " + provider);
        }

        // Provider被enable时触发此函数，比如GPS被打开
        @Override
        public void onProviderEnabled(String provider) {
            Logging.e("MainActivity", "onProviderEnabled: ");
        }

        // Provider被disable时触发此函数，比如GPS被关闭
        @Override
        public void onProviderDisabled(String provider) {
            Logging.e("MainActivity", "onProviderDisabled: ");
        }

        //当坐标改变时触发此函数，如果Provider传进相同的坐标，它就不会被触发
        @Override
        public void onLocationChanged(Location location) {
            if (location != null) {
                mLatitude = location.getLatitude();
                mLongitude = location.getLongitude();
            }
        }
    };

    private void startPage(String name) {
        if (check(name)) {
            return;
        }
        switch (name) {
            //初始化sdk
            case "1.1 初始化SDK":
                initSDK();
                break;
            //设备激活
            case "1.2 设备激活":
                activate();
                break;
            case "1.3 初始化并激活":
                initAndActivate();
                break;
            case "1.4 获取激活状态":
                getActivatStatus();
                break;
            case "1.5 获取设备ID":
                getDeviceId();
                break;
            case "1.6 设置设备ID":
                setDeviceId();
                break;
            case "1.7 品牌信息":
                getBrandInfo();
                break;
            //栏目
            case "2.1 栏目":
                //分类
            case "2.2 分类":
                showList(name);
//                inputLocation(name);
                break;
            //专辑
            case "3.1 专辑":
                showDetail(ResType.TYPE_ALBUM, "1100002157060", AlbumPlayerActivity.class);
                break;
            //单曲
            case "3.2 单曲":
                showDetail(ResType.TYPE_AUDIO, "1000026368360", AudioPlayerActivity.class);
                break;
            //AI电台
            case "3.3 AI电台":
                showDetail(ResType.TYPE_RADIO, "1200000000099", RadioPlayerActivity.class);
                break;
            case "3.4 AI电台(地区)":
                showAIRadio(ResType.TYPE_RADIO, "1200000000099", RadioPlayerGetListByAreaActivity.class);
                break;
            //在线广播
            case "3.5 在线广播":
                showBroadcast(ResType.TYPE_BROADCAST, "1600000000323", "", BroadcastPlayerActivity.class);
                break;
            case "3.6 在线广播(本地或国家台)":
                showDetail(ResType.TYPE_BROADCAST, "1600000000323", BroadcastPlayerGetLocalByIdActivity.class);
                break;
            case "3.7 报时":
                playClock();
                break;
            //语义搜索
            case "4.1 语义搜索":
                startActivity(new Intent(this, VoiceSearchActivity.class));
                break;
            case "4.2 关键词搜索":
                startActivity(new Intent(this, KeywordSearchActivity.class));
                break;
            //考拉账号登录
            case "5.1 云听账号登录":
                startActivity(new Intent(this, KaolaLoginActivity.class));
                break;
            //收藏
            case "6.1 订阅":
                if(checkLogin()) {
                    startActivity(new Intent(MainActivity.this, SubscribeActivity.class));
                }
//                choiceCp();
                break;
            //收听历史
            case "6.2 收听历史":
                if(checkLogin()) {
                    startActivity(new Intent(this, HistoryActivity.class));
                }
                break;
            //直播
//            case "7.1 直播演示":
//                startActivity(new Intent(this, LiveActivity.class));
//                break;
            //场景
//            case "7.1 场景演示":
//                startActivity(new Intent(this, SceneActivity.class));
//                break;
//            case "8.1 用户信息":
//                startActivity(new Intent(this, UserActivity.class));
//                break;
//            case "8.2 兴趣标签":
//                startActivity(new Intent(this, InterestActivity.class));
//                break;
//            case "9.1 测试":
//                AdvertisingManager manager = AdvertisingManager.getInstance();
//                if (mLifecycleCallback == null) {
//                    manager.setImager(new AdvertImagerTest());
//                    manager.setPlayer(new AdvertPlayerImpl());
//                    mLifecycleCallback = new AdvertisingLifecycleCallback() {
//                        @Override
//                        public void onCreate(String adZoneId, int subtype) {
//                            Log.e("MainActivity", "onCreate: " + adZoneId);
//                        }
//
//                        @Override
//                        public void onStart(Advert advert) {
//                            Log.e("MainActivity", "onStart: " + advert);
//                            Intent intent = new Intent(MainActivity.this, DownloadActivity.class);
//                            intent.putExtra("ssss", advert);
//                            startActivity(intent);
//                        }
//
//                        @Override
//                        public void onClose(Advert advert) {
//                            Log.e("MainActivity", "onClose: " + advert);
//                        }
//
//                        @Override
//                        public void onError(String adZoneId, int subtype, Exception e) {
//                            Logging.e("MainActivity", "onError: " + e);
//                        }
//                    };
//                    manager.registerAdvertLifecycleCallback(mLifecycleCallback);
//                }
//                manager.exposePreloading("198", "", "", "3,4,5", "");
//                TimedAdvertManager.getInstance().start(186, null, null);
//                break;
            case "7.1 vip套餐":
                startActivity(new Intent(this, VipMealsActivity.class));
                break;
            case "7.2 vip二维码":
                if(checkLogin()){
                    startActivity(new Intent(this, VipQRCodeActivity.class));
                }
                break;
            case "7.3 专辑二维码":
                if(checkLogin()) {
                    startActivity(new Intent(this, AlbumQRCodeActivity.class));
                }
                break;
            case "7.4 单曲二维码":
                if(checkLogin()) {
                    startActivity(new Intent(this, AudiosQRCodeActivity.class));
                }
                break;
            case "7.5 查看二维码状态":
                startActivity(new Intent(this, QRCodeStatusActivity.class));
                break;
            case "7.6 已购列表":
                if(checkLogin()) {
                    startActivity(new Intent(this, PurchasedActivity.class));
                }
                break;
            case "7.7 订单列表":
                if(checkLogin()) {
                    startActivity(new Intent(this, OrderActivity.class));
                }
                break;
            case "8.1 活动列表":
                startActivity(new Intent(this, ActivitiesActivity.class));
                break;
            default:
                break;
        }
    }

    private void playClock() {
        new AudioRequest().getCurrentClockAudio(new HttpCallback<AudioDetails>() {
            @Override
            public void onSuccess(AudioDetails audioDetails) {
                if (audioDetails != null) {
                    PlayerManager.getInstance().startTempTask(getTempTaskPlayItem(audioDetails));
                }
            }

            @Override
            public void onError(ApiException e) {
                showError("获取报时失败", e);
            }
        });
    }

    private TempTaskPlayItem getTempTaskPlayItem(AudioDetails audioDetails) {
        TempTaskPlayItem tempTaskPlayItem = new TempTaskPlayItem();
        tempTaskPlayItem.setNeedPlayStateCallBack(true);
        tempTaskPlayItem.setTempTaskType(PlayerConstants.TEMP_TASK_TYPE_CLOCK);
//        tempTaskPlayItem.setPlayStateListener(basePlayStateListener);
        tempTaskPlayItem.setNeedNextInnerAction(false);
        tempTaskPlayItem.setPlayUrl(audioDetails.getAacPlayUrl32());
        return tempTaskPlayItem;
    }

    private void choiceCp() {
        new AlertDialog.Builder(this).setSingleChoiceItems(R.array.dialog_cp_name, -1, (dialog, which) -> {
            switch (which) {
                case 0:
                    startActivity(new Intent(MainActivity.this, SubscribeActivity.class));
                    break;
                case 1:
                    startActivity(new Intent(MainActivity.this, CollectionActivity.class));
                    break;
                default:
                    break;
            }
            dialog.dismiss();
        }).create().show();
    }

    private void inputLocation(String name) {
        View view = LayoutInflater.from(this).inflate(R.layout.dialog_input_location, null);
        EditText etDialogLng = view.findViewById(R.id.et_dialog_lng);
        EditText etDialogLat = view.findViewById(R.id.et_dialog_lat);
        new AlertDialog.Builder(this).setView(view)
                .setPositiveButton("确定", (dialog, which) -> {
                    String longitude = etDialogLng.getText().toString().trim();
                    if (!TextUtils.isEmpty(longitude)) {
                        OpenSDK.getInstance().setLongitude(longitude);
                    }
                    String latitude = etDialogLat.getText().toString().trim();
                    if (!TextUtils.isEmpty(latitude)) {
                        OpenSDK.getInstance().setLatitude(latitude);
                    }
                    dialog.dismiss();
                    showList(name);
                })
                .setNegativeButton("取消", (dialog, which) -> {
                    dialog.dismiss();
                    showList(name);
                }).create().show();
    }

    private void showList(String name) {
        switch (name) {
            case "2.1 栏目":
                startActivity(new Intent(this, ColumnActivity.class));
                break;
            case "2.2 分类":
                startActivity(new Intent(this, CategoryActivity.class));
                break;
            default:
        }
    }

    private void showAIRadio(int type, String defaultId, Class clazz) {
        LinearLayout linearLayout = new LinearLayout(this);
        linearLayout.setOrientation(LinearLayout.VERTICAL);
        linearLayout.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
        EditText editText = new EditText(this);
        editText.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
        editText.setHint("请输入id，不输入则使用默认Id");
        EditText editText1 = new EditText(this);
        editText1.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
        editText1.setHint("请输入地区编码，不输入则默认不传");
        EditText editText2 = new EditText(this);
        editText2.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
        editText2.setHint("请输入地区城市名称，不输入则默认不传");
        linearLayout.addView(editText);
        linearLayout.addView(editText1);
        linearLayout.addView(editText2);
        new Builder(this)
                .setView(linearLayout)
                .setPositiveButton("确定", (dialog, which) -> {
                    Editable text = editText.getText();
                    Editable text1 = editText1.getText();
                    String id = text.toString().trim();
                    String areaCode = text1.toString().trim();
                    String areaCityName = text1.toString().trim();
                    if(TextUtils.isEmpty(id)){
                        id = defaultId;
                    }
                    Intent intent = new Intent();
                    intent.setClass(MainActivity.this, clazz);
                    intent.putExtra(BasePlayerActivity.KEY_ID, id);
                    intent.putExtra(BasePlayerActivity.KEY_TYPE, type);
                    intent.putExtra(BasePlayerActivity.KEY_AREA_CODE, areaCode);
                    intent.putExtra(BasePlayerActivity.KEY_AREA_CITY_NAME, areaCityName);
                    startActivity(intent);
                })
                .setNegativeButton("取消", (dialog, which) -> dialog.dismiss())
                .create().show();
    }

    private void showDetail(int type, String defaultId, Class clazz) {
        EditText editText = new EditText(this);
        editText.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
        editText.setHint("请输入id，不输入则使用默认Id");
        new Builder(this)
                .setView(editText)
                .setPositiveButton("确定", (dialog, which) -> {
                    Editable text = editText.getText();
                    String id = text.toString().trim();
                    if(TextUtils.isEmpty(id)){
                        id = defaultId;
                    }
                    if(!isLetterDigit(id)){
                        showToast("输入格式有误");
                        return;
                    }
                    Intent intent = new Intent();
                    intent.setClass(MainActivity.this, clazz);
                    intent.putExtra(DetailActivity.KEY_ID, id);
                    intent.putExtra(DetailActivity.KEY_TYPE, type);
                    startActivity(intent);

                })
                .setNegativeButton("取消", (dialog, which) -> dialog.dismiss())
                .create().show();
    }

    private void setDeviceId(){
        EditText editText = new EditText(this);
        editText.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
        editText.setHint("请输入设备id");
        new Builder(this)
                .setView(editText)
                .setPositiveButton("确定", (dialog, which) -> {
                    Editable text = editText.getText();
                    String id = text.toString().trim();
                    if(TextUtils.isEmpty(id)){
                        showToast("请输入设备id");
                        return;
                    }
                    OpenSDK.getInstance().setDeviceId(id);
                    showToast("设置成功：设备id:"+OpenSDK.getInstance().getDeviceId());
                })
                .setNegativeButton("取消", (dialog, which) -> dialog.dismiss())
                .create().show();
    }

    private void showBroadcast(int type, String defaultId, String defaultData, Class clazz) {
        LinearLayout linearLayout = new LinearLayout(this);
        linearLayout.setOrientation(LinearLayout.VERTICAL);
        linearLayout.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
        EditText editText = new EditText(this);
        editText.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
        editText.setHint("请输入id，不输入则使用默认Id");
        EditText editText1 = new EditText(this);
        editText1.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
        editText1.setHint("请输入日期，不输入则使用当前日期");
        linearLayout.addView(editText);
        linearLayout.addView(editText1);
        new Builder(this)
                .setView(linearLayout)
                .setPositiveButton("确定", (dialog, which) -> {
                    Editable text = editText.getText();
                    Editable text1 = editText1.getText();
                    String id = text.toString().trim();
                    String date = text1.toString().trim();
                    if(TextUtils.isEmpty(id)){
                        id = defaultId;
                    }
                    if(TextUtils.isEmpty(date)){
                        date = defaultData;
                    }
                    Intent intent = new Intent();
                    intent.setClass(MainActivity.this, clazz);
                    intent.putExtra(BasePlayerActivity.KEY_ID, id);
                    intent.putExtra(BasePlayerActivity.KEY_TYPE, type);
                    intent.putExtra(BasePlayerActivity.KEY_DATE, date);
                    startActivity(intent);
                })
                .setNegativeButton("取消", (dialog, which) -> dialog.dismiss())
                .create().show();
    }

    private void getDeviceId(){
        showToast("设备id:"+OpenSDK.getInstance().getDeviceId());
    }

    private void getActivatStatus(){
        showToast("是否激活:"+OpenSDK.getInstance().isActivate());
    }

    private void initAndActivate() {
        OpenSDK openSDK = OpenSDK.getInstance();
        String[] mateValues = getMateValue();
        AlertDialog alertDialog = new Builder(this)
                .setMessage("AppId:" + mateValues[0] + "\n\r" + "AppKey:" + mateValues[1] + "\n\r" + "Channel:"
                        + mateValues[2])
                .setNegativeButton("取消", (dialog, which) -> dialog.dismiss())
                .setPositiveButton("初始化", (dialog, which) -> {
                    AdvertOptions options = new AdvertOptions.Builder()
                            .deviceType(1)
                            .useHttps(mDemoStrategy)
                            .brand("宝马").carType("X7").build();
                    openSDK.config(getApplication(), options, new HttpCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean aBoolean) {
                            String openId = AccessTokenManager.getInstance().getKaolaAccessToken().getOpenId();
                            showToast(aBoolean ? "初始化、激活成功,激活Id=" + openId : "初始化、激活失败");
                            isInitialized = aBoolean;
//                            isActivated = aBoolean;
                        }

                        @Override
                        public void onError(ApiException exception) {
                            showToast("初始化激活失败，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
                        }
                    });
                    dialog.dismiss();
                }).create();
        alertDialog.show();
    }

    private void activate() {
        OpenSDK openSDK = OpenSDK.getInstance();
        if (!openSDK.isActivate()) {
            openSDK.activate(new HttpCallback<Boolean>() {
                @Override
                public void onSuccess(Boolean aBoolean) {
                    openSDK.setLatitude(String.valueOf(mLatitude));
                    openSDK.setLongitude(String.valueOf(mLongitude));
                    String openId = AccessTokenManager.getInstance().getKaolaAccessToken().getOpenId();
                    showToast(aBoolean ? "激活成功,激活Id=" + openId : "激活失败");
//                    isActivated = true;
                }

                @Override
                public void onError(ApiException exception) {
                    showToast("激活失败，错误码：" + exception.getCode() + ", 错误信息：" + exception.getMessage());
                }
            });
        } else {
            String openId = AccessTokenManager.getInstance().getKaolaAccessToken().getOpenId();
            showToast("已经激活：" + openId);
//            isActivated = true;
        }
    }

    private void getBrandInfo() {
        startActivity(new Intent(this, BrandInfoActivity.class));
    }


    private void initSDK() {
//        PlayerManager.getInstance(this).setCanRequestAudioFocusOnPlayerInit(false);
//        PlayerManager.getInstance(this).setCanUseDefaultAudioFocusLogic(false);
        Logging.d("main", "初始化");
        Logging.d("初始化");
        OpenSDK openSDK = OpenSDK.getInstance();
        Options options = new AdvertOptions.Builder()
                .interceptor(new Interceptor() {
                    @Override
                    public Response intercept(Chain chain) throws IOException {
                        Log.e("MainActivity", "intercept: "+chain.request().url());
                        return chain.proceed(chain.request());
                    }
                })
                .versionName("1.0.1.11").carType("奥迪Q5").build();
        String[] mateValues = getMateValue();
        AlertDialog alertDialog = new Builder(this)
                .setMessage("AppId:" + mateValues[0] + "\n\r" + "AppKey:" + mateValues[1] + "\n\r" + "Channel:"
                        + mateValues[2])
                .setNegativeButton("取消", (dialog, which) -> dialog.dismiss())
                .setPositiveButton("初始化", (dialog, which) -> {
                    openSDK.initSDK(getApplication(), options, new HttpCallback<Boolean>() {
                        @Override
                        public void onSuccess(Boolean aBoolean) {
                            String openId = AccessTokenManager.getInstance().getKaolaAccessToken().getOpenId();
                            Logging.d("Main", "mLatitude=" + mLatitude);
                            Logging.Log.e("MainActivity", "onSuccess: mLongitude=" + mLongitude);
                            //添加经纬度用于获取本地广播、语义搜索、推荐等接口
                            openSDK.setLatitude(String.valueOf(mLatitude));
                            openSDK.setLongitude(String.valueOf(mLongitude));
                            showToast(aBoolean ? "初始化SDK成功,激活Id=" + openId : "初始化SDK失败");
                            isInitialized = true;
                        }

                        @Override
                        public void onError(ApiException exception) {
                            showToast("初始化SDK失败，错误码=" + exception.getCode() + ",错误信息=" + exception.getMessage());
                        }
                    });
                    dialog.dismiss();
                }).create();
        alertDialog.show();

    }

    private String[] getMateValue() {
        String[] values = new String[3];
        try {
            ApplicationInfo applicationInfo = getPackageManager()
                    .getApplicationInfo(getPackageName(), PackageManager.GET_META_DATA);
            Bundle metaData = applicationInfo.metaData;
            values[0] = metaData.getString("com.kaolafm.open.sdk.AppId");
            values[1] = metaData.getString("com.kaolafm.open.sdk.AppKey");
            values[2] = metaData.getString("com.kaolafm.open.sdk.Channel");
        } catch (NameNotFoundException | NullPointerException e) {
            e.printStackTrace();
        }
        return values;
    }

    private boolean check(String name) {
        if (name.startsWith("1.3")) {
            return false;
        }
        if (!isInitialized && !"1.1 初始化SDK".equals(name)) {
            showToast("请先初始化");
            return true;
        }
        if ((OpenSDK.getInstance() != null && !OpenSDK.getInstance().isActivate()) && !"1.2 设备激活".equals(name) && !"1.1 初始化SDK".equals(name)) {
            showToast("请先激活");
            return true;
        }
        return false;
    }

    public static boolean isLetterDigit(String str) {
        String regex = "^[0-9,]*$";
        return str.matches(regex);
    }

    private boolean checkLogin(){
        boolean isLogin = AccessTokenManager.getInstance().getKaolaAccessToken().isLogin();
        if(!isLogin){
            showToast("当前用户未登录,请先登录");
            startActivity(new Intent(this, KaolaLoginActivity.class));
        }
        return isLogin;
    }
}
