package com.kaolafm.opensdk.api.live;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.live.model.ChatRoomTokenDetail;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.http.core.HttpCallback;
import java.util.HashMap;
import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * <AUTHOR>
 */
public class LiveRequest extends BaseRequest {

    public static final String KEY_PROGRAM_ID = "programid";

    public static final String KEY_KRADIO_ID = "kradioId";

    public static final String KEY_PHONE_NUM = "phoneNum";

    private final LiveService mLiveService;

    public LiveRequest() {
        mLiveService = obtainRetrofitService(LiveService.class);
    }

    /**
     * 根据直播间的id获取直播信息
     *
     * @param id 直播间的Id
     */
    public void getLiveInfo(String id, HttpCallback<LiveInfoDetail> callback) {
        doHttpDeal(mLiveService.getLiveInfo(id), BaseResult::getResult, callback);
    }

    /**
     * 根据手机号获取进入直播的token。该接口需要用户手机号已经注册过考拉的账号。直播间显示的是考拉账号的用户名和头像。
     *
     * @param uid   用户uid
     * @param phone 手机号
     */
    public void getChatRoomToken(String uid, String phone, HttpCallback<ChatRoomTokenDetail> callback) {
        HashMap<String, String> tempMap = new HashMap<>();
        tempMap.put(KEY_KRADIO_ID, uid);
        tempMap.put(KEY_PHONE_NUM, phone);
        doHttpDeal(mLiveService.getChatRoomToken(tempMap), BaseResult::getResult, callback);
    }

    /**
     * 根据开发者提供的唯一标识、头像、昵称获取进入直播的token。
     *
     * @param id       唯一标识id
     * @param avatar   头像
     * @param nickName 昵称
     */
    public void getChatRoomToken(String id, String avatar, String nickName,
            HttpCallback<ChatRoomTokenDetail> callback) {
        HashMap<String, String> params = new HashMap<>();
        params.put("otherId", id);
        params.put("avatar", avatar);
        params.put("nickName", nickName);
        String body = mGsonLazy.get().toJson(params);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
        doHttpDeal(mLiveService.getChatRoomTokenByUnique(requestBody), BaseResult::getResult, callback);
    }
}
