14:53:52.506  2559  2890 V IJKMEDIA: read thread: run in read loop - 459380
14:53:56.456  2559  2591 D NetworkManager: Socket test thread is running!
14:53:56.456  2559  2591 D NetworkManager: Socket test start
14:53:56.495  2559  2591 D NetworkManager: Socket test finish
14:53:58.512  2559  2890 V IJKMEDIA: read thread: run in read loop - 465387
14:54:01.496  2559  2591 D NetworkManager: Socket test thread is running!
14:54:01.496  2559  2591 D NetworkManager: Socket test start
14:54:01.511  2559  2591 D NetworkManager: Socket test finish
14:54:03.625  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref++: 3 
14:54:03.625  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref--: 2 
14:54:03.628  2559  2559 I NetworkUtil: hasNetwork internet = true valided = true
14:54:03.628  2559  2559 I ZHUTAG  : isNetworkAvailable------->networkUsable = true
14:54:03.628  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.628  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.628  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.628  2559  2559 D report_tag: │ ReportManager.addPlayerUiControlEvent  (ReportManager.java:744)
14:54:03.628  2559  2559 D report_tag: │    PlayerUiControlReportEvent.<init>  (PlayerUiControlReportEvent.java:39)
14:54:03.628  2559  2559 D report_tag: │       BaseReportEventBean.<init>  (BaseReportEventBean.java:113)
14:54:03.628  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.628  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.628  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.628  2559  2559 D report_tag: │ 获取版本名称: 2.10.4, 版本号: 210320088
14:54:03.628  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.628  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.628  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.628  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.628  2559  2559 D report_tag: │ ReportParameterManager.getActionId  (ReportParameterManager.java:355)
14:54:03.629  2559  2559 D report_tag: │    ReportParameterManager.savePrivateParameter  (ReportParameterManager.java:454)
14:54:03.629  2559  2559 D report_tag: │       ReportHelper.savePrivateParameter  (ReportHelper.java:493)
14:54:03.629  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.629  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.629  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.629  2559  2559 D report_tag: │ savePrivateParameter
14:54:03.629  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.631  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.631  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.632  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.632  2559  2559 D report_tag: │ ReportManager.addPlayerUiControlEvent  (ReportManager.java:748)
14:54:03.632  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:255)
14:54:03.632  2559  2559 D report_tag: │       ReportHelper.addEvent  (ReportHelper.java:281)
14:54:03.632  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.632  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.632  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.632  2559  2559 D report_tag: │ 添加数据上报事件.是否立即:false事件id = 300006 json = {"controltype":"1","position":"1","type":"2","action_id":"139","app_version":"2.10.4","app_version2":"210320088","appid":"eh7169","appid_type":"1","applicationcode":"17000","car_brand":"","carrier":"1","channel":"com.edog.car.ford_kradio","developer":"5548079","dsource":"vehicleeqv2","eventcode":"300006","imsi":"460000000000","ip":"*********","lat":"31.2378881115","lib_version":"1.6.0","lon":"121.476035847","manufacturer":"vivo","market_type":"","model":"V2366GA","network":"1","oem":"","openid":"eh71692023091210042759","operator":"1","os":"android","osversion":"12","page":"110000","playid":"9c2bc0573f8b53c39452aeff395d290e","report_timely":"0","screen_direction":"0","screen_height":"1080","screen_width":"2348","sessionid":"2","sourcetype":"3","timestamp":"1755845643628","udid":"5f5607f9a1dba516ce20e67af230f7c4","wifi":"1"}
14:54:03.632  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.632  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.632  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.632  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.632  2559  2559 D report_tag: │ ReportManager.addPlayerUiControlEvent  (ReportManager.java:748)
14:54:03.632  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:255)
14:54:03.632  2559  2559 D report_tag: │       ReportHelper.addEvent  (ReportHelper.java:300)
14:54:03.632  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.632  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.632  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.632  2559  2559 D report_tag: │ 上传到神策
14:54:03.632  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.632  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.632  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.632  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.632  2559  2559 D report_tag: │ ReportHelper.addEvent  (ReportHelper.java:255)
14:54:03.632  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:311)
14:54:03.632  2559  2559 D report_tag: │       ReportTaskHelper.insertTask  (ReportTaskHelper.java:40)
14:54:03.632  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.632  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.632  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.632  2559  2559 D report_tag: │ 队列大小 = 0
14:54:03.632  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.632  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.632  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.632  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.632  2559  2559 D report_tag: │ ReportHelper.addEvent  (ReportHelper.java:255)
14:54:03.632  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:311)
14:54:03.632  2559  2559 D report_tag: │       ReportTaskHelper.insertTask  (ReportTaskHelper.java:44)
14:54:03.632  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.632  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.632  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.632  2559  2559 D report_tag: │ 没有再运行
14:54:03.632  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.632  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.632  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.632  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.632  2559  2559 D report_tag: │ ReportTaskHelper.insertTask  (ReportTaskHelper.java:46)
14:54:03.632  2559  2559 D report_tag: │    ReportTaskHelper.run  (ReportTaskHelper.java:63)
14:54:03.632  2559  2559 D report_tag: │       ReportTaskHelper.getTask  (ReportTaskHelper.java:55)
14:54:03.632  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.632  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.632  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.632  2559  2559 D report_tag: │ 获取队列大小 = 1
14:54:03.632  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.632  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.633  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.633  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.633  2559  2559 D report_tag: │ ReportHelper.addEvent  (ReportHelper.java:311)
14:54:03.633  2559  2559 D report_tag: │    ReportTaskHelper.insertTask  (ReportTaskHelper.java:46)
14:54:03.633  2559  2559 D report_tag: │       ReportTaskHelper.run  (ReportTaskHelper.java:74)
14:54:03.633  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.633  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.633  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.633  2559  2559 D report_tag: │ 插入数据
14:54:03.633  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.633  2559  2559 I player_log_tag: PlayerManagerHelper.invalidPlayAction->calling =false
14:54:03.633  2559  2559 I player_log_tag: PlayerManagerHelper->lockPlayerIntercept AudioAdPlayLockPlayer = false
14:54:03.633  2559  2559 I player_log_tag: PlayerManagerHelper->lockPlayerIntercept no ad play
14:54:03.633  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref++: 3 
14:54:03.633  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref--: 2 
14:54:03.633  2559  2559 I player_log_tag: PlayerManagerHelper.invalidPlayAction->calling =false
14:54:03.633  2559  2559 I player_log_tag: PlayerManagerHelper->lockPlayerIntercept AudioAdPlayLockPlayer = false
14:54:03.633  2559  2559 I player_log_tag: PlayerManagerHelper->lockPlayerIntercept no ad play
14:54:03.633  2559  2559 I player_log_tag: PlayerManager.play->fromUser = true
14:54:03.635  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.636  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.636  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.636  2559  2559 D report_tag: │ ConsumerSingleObserver.onSuccess  (ConsumerSingleObserver.java:63)
14:54:03.636  2559  3011 I RequestInterceptManager: intercept url= https://iovopen.radio.cn/v2/audio/playInfo?playUrlId=9223371036827977189&capabilities=NEW_DOMAIN_SUPPORTTED%2CPAY_CONTENT_SUPPORTTED%2CMEDIA_URL_MUST_BE_HTTPS&os=android&openid=eh71692023091210042759&packagename=com.edog.car.ford_kradio&sign=a3d018f64aa8125f3874fe44e756c6ff&channel=ford_kradio&deviceid=5f5607f9a1dba516ce20e67af230f7c4&version=2.10.4.0088&sdkversion=1.6.0&carType=PD2366&appid=eh7169&udid=5f5607f9a1dba516ce20e67af230f7c4&access_token=***&lat=***&lng=***
14:54:03.636  2559  2559 D report_tag: │    -$$Lambda$ReportTaskHelper$0_306wrLG-ZqLBvoT6kuPoy_UZk.accept  (null:4)
14:54:03.636  2559  2559 D report_tag: │       ReportTaskHelper.lambda$run$0  (ReportTaskHelper.java:76)
14:54:03.637  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.637  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.637  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.637  2559  2559 D report_tag: │ 数据库大小 = 1
14:54:03.637  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.637  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.637  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.637  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.637  2559  2559 D report_tag: │ ReportTaskHelper.taskDone  (ReportTaskHelper.java:51)
14:54:03.637  2559  2559 D report_tag: │    ReportTaskHelper.run  (ReportTaskHelper.java:63)
14:54:03.637  2559  2559 D report_tag: │       ReportTaskHelper.getTask  (ReportTaskHelper.java:55)
14:54:03.637  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.637  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.638  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.638  2559  2559 D report_tag: │ 获取队列大小 = 0
14:54:03.638  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.638  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.638  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.638  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.638  2559  2559 D report_tag: │ ReportTaskHelper.lambda$run$0  (ReportTaskHelper.java:80)
14:54:03.638  2559  2559 D report_tag: │    ReportTaskHelper.taskDone  (ReportTaskHelper.java:51)
14:54:03.638  2559  2559 D report_tag: │       ReportTaskHelper.run  (ReportTaskHelper.java:65)
14:54:03.638  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.638  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.638  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.639  2559  2559 D report_tag: │ 没有任何任务了. 停止运行
14:54:03.639  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.639  2559  3011 I RequestInterceptManager: 替换完成 url= https://iovopen.radio.cn/v2/audio/playInfo?playUrlId=9223371036827977189&capabilities=NEW_DOMAIN_SUPPORTTED%2CPAY_CONTENT_SUPPORTTED%2CMEDIA_URL_MUST_BE_HTTPS&os=android&openid=eh71692023091210042759&packagename=com.edog.car.ford_kradio&sign=a3d018f64aa8125f3874fe44e756c6ff&channel=ford_kradio&deviceid=5f5607f9a1dba516ce20e67af230f7c4&version=2.10.4.0088&sdkversion=1.6.0&carType=PD2366&appid=eh7169&udid=5f5607f9a1dba516ce20e67af230f7c4&access_token=***&lat=***&lng=*** timestamp:1755845643639
14:54:03.766  2559  3011 D K-radio-Request: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.767  2559  3011 D K-radio-Request: │ Thread: RxCachedThreadScheduler-7
14:54:03.767  2559  3011 D K-radio-Request: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.767  2559  3011 D K-radio-Request: │ ConnectInterceptor.intercept  (ConnectInterceptor.java:45)
14:54:03.767  2559  3011 D K-radio-Request: │    RealInterceptorChain.proceed  (RealInterceptorChain.java:147)
14:54:03.767  2559  3011 D K-radio-Request: │       RequestInterceptor.intercept  (RequestInterceptor.java:56)
14:54:03.767  2559  3011 D K-radio-Request: │          FormatPrinter.printFileRequest  (FormatPrinter.java:100)
14:54:03.767  2559  3011 D K-radio-Request: │             FormatPrinter.logInfo  (FormatPrinter.java:277)
14:54:03.767  2559  3011 D K-radio-Request: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.767  2559  3011 D K-radio-Request: │ ┌────── Request ────────────────────────────────────────────────────────────────────────
14:54:03.767  2559  3011 D K-radio-Request: │ │ URL: https://iovopen.radio.cn/v2/audio/playInfo?playUrlId=9223371036827977189&capabilities=NEW_DOMAIN_SUPPORTTED%2CPAY_CONTENT_SUPPORTTED%2CMEDIA_URL_MUST_BE_HTTPS&os=android&lng=121.476035847&openid=eh71692023091210042759&packagename=com.edog.car.ford_kradio&sign=a3d018f64aa8125f3874fe44e756c6ff&channel=ford_kradio&deviceid=5f5607f9a1dba516ce20e67af230f7c4&version=2.10.4.0088&sdkversion=1.6.0&carType=PD2366&appid=eh7169&udid=5f5607f9a1dba516ce20e67af230f7c4&lat=31.2378881115
14:54:03.767  2559  3011 D K-radio-Request: │ │ 
14:54:03.767  2559  3011 D K-radio-Request: │ │ Method: @GET
14:54:03.767  2559  3011 D K-radio-Request: │ │ 
14:54:03.767  2559  3011 D K-radio-Request: │ │ Headers:
14:54:03.767  2559  3011 D K-radio-Request: │ │ ┌ Host: iovopen.radio.cn
14:54:03.767  2559  3011 D K-radio-Request: │ │ ├ Connection: Keep-Alive
14:54:03.767  2559  3011 D K-radio-Request: │ │ ├ Accept-Encoding: gzip
14:54:03.767  2559  3011 D K-radio-Request: │ │ └ User-Agent: okhttp/3.12.12
14:54:03.767  2559  3011 D K-radio-Request: │ │ Omitted request body
14:54:03.767  2559  3011 D K-radio-Request: │ └───────────────────────────────────────────────────────────────────────────────────────
14:54:03.767  2559  3011 D K-radio-Request: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.854  2559  3011 D K-radio-Response: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.854  2559  3011 D K-radio-Response: │ Thread: RxCachedThreadScheduler-7
14:54:03.854  2559  3011 D K-radio-Response: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.854  2559  3011 D K-radio-Response: │ ConnectInterceptor.intercept  (ConnectInterceptor.java:45)
14:54:03.854  2559  3011 D K-radio-Response: │    RealInterceptorChain.proceed  (RealInterceptorChain.java:147)
14:54:03.854  2559  3011 D K-radio-Response: │       RequestInterceptor.intercept  (RequestInterceptor.java:85)
14:54:03.854  2559  3011 D K-radio-Response: │          FormatPrinter.printJsonResponse  (FormatPrinter.java:129)
14:54:03.854  2559  3011 D K-radio-Response: │             FormatPrinter.logInfo  (FormatPrinter.java:277)
14:54:03.854  2559  3011 D K-radio-Response: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.854  2559  3011 D K-radio-Response: │ ┌────── Response ───────────────────────────────────────────────────────────────────────
14:54:03.854  2559  3011 D K-radio-Response: │ │ URL: https://iovopen.radio.cn/v2/audio/playInfo?playUrlId=9223371036827977189&capabilities=NEW_DOMAIN_SUPPORTT
14:54:03.854  2559  3011 D K-radio-Response: │ │ ED%2CPAY_CONTENT_SUPPORTTED%2CMEDIA_URL_MUST_BE_HTTPS&os=android&lng=121.476035847&openid=eh716920230912100427
14:54:03.854  2559  3011 D K-radio-Response: │ │ 59&packagename=com.edog.car.ford_kradio&sign=a3d018f64aa8125f3874fe44e756c6ff&channel=ford_kradio&deviceid=5f5
14:54:03.854  2559  3011 D K-radio-Response: │ │ 607f9a1dba516ce20e67af230f7c4&version=2.10.4.0088&sdkversion=1.6.0&carType=PD2366&appid=eh7169&udid=5f5607f9a1
14:54:03.854  2559  3011 D K-radio-Response: │ │ dba516ce20e67af230f7c4&lat=31.2378881115
14:54:03.854  2559  3011 D K-radio-Response: │ │ /v2/audio/playInfo - is success : true - Received in: 85ms
14:54:03.854  2559  3011 D K-radio-Response: │ │ 
14:54:03.854  2559  3011 D K-radio-Response: │ │ Status Code: 200 / 
14:54:03.854  2559  3011 D K-radio-Response: │ │ 
14:54:03.854  2559  3011 D K-radio-Response: │ │ Headers:
14:54:03.854  2559  3011 D K-radio-Response: │ │ ┌ date: Fri, 22 Aug 2025 06:54:05 GMT
14:54:03.854  2559  3011 D K-radio-Response: │ │ ├ content-type: application/json;charset=UTF-8
14:54:03.854  2559  3011 D K-radio-Response: │ │ ├ alt-svc: h3=":443"; ma=3600,quic=":443"; v="46,43,39"; ma=3600
14:54:03.854  2559  3011 D K-radio-Response: │ │ ├ vary: Accept-Encoding
14:54:03.854  2559  3011 D K-radio-Response: │ │ ├ vary: Origin
14:54:03.854  2559  3011 D K-radio-Response: │ │ ├ vary: Access-Control-Request-Method
14:54:03.854  2559  3011 D K-radio-Response: │ │ ├ vary: Access-Control-Request-Headers
14:54:03.854  2559  3011 D K-radio-Response: │ │ ├ content-language: en-US
14:54:03.854  2559  3011 D K-radio-Response: │ │ └ content-encoding: gzip
14:54:03.854  2559  3011 D K-radio-Response: │ │ 
14:54:03.854  2559  3011 D K-radio-Response: │ │ Body:
14:54:03.854  2559  3011 D K-radio-Response: │ │ {
14:54:03.854  2559  3011 D K-radio-Response: │ │     "code": 10000,
14:54:03.854  2559  3011 D K-radio-Response: │ │     "message": "success",
14:54:03.854  2559  3011 D K-radio-Response: │ │     "result": {
14:54:03.854  2559  3011 D K-radio-Response: │ │         "currentTime": 1755845645,
14:54:03.854  2559  3011 D K-radio-Response: │ │         "distanceTime": 1755932045,
14:54:03.854  2559  3011 D K-radio-Response: │ │         "duration": 95000,
14:54:03.854  2559  3011 D K-radio-Response: │ │         "playInfoList": [
14:54:03.854  2559  3011 D K-radio-Response: │ │             {
14:54:03.854  2559  3011 D K-radio-Response: │ │                 "fileType": "mp3",
14:54:03.854  2559  3011 D K-radio-Response: │ │                 "playUrl": "https:\/\/iovaudio.radio.cn\/mz\/audios\/202112\/d45feb07-dc02-4728-bf09-6971b2643
14:54:03.854  2559  3011 D K-radio-Response: │ │ 718.mp3?auth_key=1755845645660-17e869af37364d3b95ea721196ad93b2-0-50d978b23e28f81910d98f9113ad69b5",
14:54:03.854  2559  3011 D K-radio-Response: │ │                 "fileSize": 571102,
14:54:03.854  2559  3011 D K-radio-Response: │ │                 "bitrate": 64,
14:54:03.854  2559  3011 D K-radio-Response: │ │                 "bitrateNew": 192
14:54:03.854  2559  3011 D K-radio-Response: │ │             },
14:54:03.854  2559  3011 D K-radio-Response: │ │             {
14:54:03.854  2559  3011 D K-radio-Response: │ │                 "fileType": "mp3",
14:54:03.854  2559  3011 D K-radio-Response: │ │                 "playUrl": "https:\/\/iovaudio.radio.cn\/mz\/audios\/202112\/d45feb07-dc02-4728-bf09-6971b2643
14:54:03.854  2559  3011 D K-radio-Response: │ │ 718.mp3?auth_key=1755845645660-17e869af37364d3b95ea721196ad93b2-0-50d978b23e28f81910d98f9113ad69b5",
14:54:03.854  2559  3011 D K-radio-Response: │ │                 "fileSize": 571102,
14:54:03.854  2559  3011 D K-radio-Response: │ │                 "bitrate": 64,
14:54:03.854  2559  3011 D K-radio-Response: │ │                 "bitrateNew": 48
14:54:03.854  2559  3011 D K-radio-Response: │ │             }
14:54:03.854  2559  3011 D K-radio-Response: │ │         ]
14:54:03.854  2559  3011 D K-radio-Response: │ │     },
14:54:03.854  2559  3011 D K-radio-Response: │ │     "serverTime": 1755845645660
14:54:03.854  2559  3011 D K-radio-Response: │ │ }
14:54:03.854  2559  3011 D K-radio-Response: │ └───────────────────────────────────────────────────────────────────────────────────────
14:54:03.854  2559  3011 D K-radio-Response: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.856  2559  2559 I player_log_tag: AlbumPlayControl->toneQuality=  1
14:54:03.856  2559  2559 I setPlayUrl: tempUrl is null
14:54:03.856  2559  2559 I BasePlayControl: getPlayurl:null
14:54:03.857  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.857  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.857  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.857  2559  2559 D report_tag: │ ReportManager.reportRequestError  (ReportManager.java:487)
14:54:03.857  2559  2559 D report_tag: │    RequetErrorReportEvent.<init>  (RequetErrorReportEvent.java:43)
14:54:03.857  2559  2559 D report_tag: │       BaseReportEventBean.<init>  (BaseReportEventBean.java:113)
14:54:03.857  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.857  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.857  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.857  2559  2559 D report_tag: │ 获取版本名称: 2.10.4, 版本号: 210320088
14:54:03.857  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.857  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.857  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.857  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.857  2559  2559 D report_tag: │ ReportParameterManager.getActionId  (ReportParameterManager.java:355)
14:54:03.857  2559  2559 D report_tag: │    ReportParameterManager.savePrivateParameter  (ReportParameterManager.java:454)
14:54:03.857  2559  2559 D report_tag: │       ReportHelper.savePrivateParameter  (ReportHelper.java:493)
14:54:03.857  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.857  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.857  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.857  2559  2559 D report_tag: │ savePrivateParameter
14:54:03.857  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.859  2559  2559 I NetworkUtil: hasNetwork internet = true valided = true
14:54:03.859  2559  2559 I ZHUTAG  : isNetworkAvailable------->networkUsable = true
14:54:03.860  2559  2559 I client.impl.p: onPlayerFailed: 
14:54:03.860  2559  2559 I kradio.playerbar:  onPlayerFailed
14:54:03.860  2559  2559 I K_Radio-PlayerBar: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.860  2559  2559 I K_Radio-PlayerBar: │ PlayerListenerHelper$MBasePlayStateListener.onPlayerFailed  (PlayerListenerHelper.java:273)
14:54:03.860  2559  2559 I K_Radio-PlayerBar: │    UIThreadUtil.runUIThread  (UIThreadUtil.java:21)
14:54:03.860  2559  2559 I K_Radio-PlayerBar: │       -$$Lambda$PlayerListenerHelper$MBasePlayStateListener$44pXhgyJ_BfG_L4lj81Q9i3acmc.onSuccess  (null:8)
14:54:03.860  2559  2559 I K_Radio-PlayerBar: │          PlayerListenerHelper$MBasePlayStateListener.lambda$onPlayerFailed$5  (PlayerListenerHelper.java:273)
14:54:03.860  2559  2559 I K_Radio-PlayerBar: │             PlayerListenerHelper.access$100  (PlayerListenerHelper.java:26)
14:54:03.860  2559  2559 I K_Radio-PlayerBar: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.860  2559  2559 I K_Radio-PlayerBar: │ showPauseState start isLiveState = false
14:54:03.860  2559  2559 I K_Radio-PlayerBar: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.869  2559  2559 I K_Radio-PlayerBar: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.869  2559  2559 I K_Radio-PlayerBar: │ PlayerListenerHelper$MBasePlayStateListener.onPlayerFailed  (PlayerListenerHelper.java:273)
14:54:03.869  2559  2559 I K_Radio-PlayerBar: │    UIThreadUtil.runUIThread  (UIThreadUtil.java:21)
14:54:03.869  2559  2559 I K_Radio-PlayerBar: │       -$$Lambda$PlayerListenerHelper$MBasePlayStateListener$44pXhgyJ_BfG_L4lj81Q9i3acmc.onSuccess  (null:8)
14:54:03.869  2559  2559 I K_Radio-PlayerBar: │          PlayerListenerHelper$MBasePlayStateListener.lambda$onPlayerFailed$5  (PlayerListenerHelper.java:273)
14:54:03.869  2559  2559 I K_Radio-PlayerBar: │             PlayerListenerHelper.access$100  (PlayerListenerHelper.java:26)
14:54:03.869  2559  2559 I K_Radio-PlayerBar: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.869  2559  2559 I K_Radio-PlayerBar: │ showPauseState drawable playerbar_play
14:54:03.869  2559  2559 I K_Radio-PlayerBar: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.869  2559  2559 I PlayerBar: setThumb() start --- isPlaying = false
14:54:03.869  2559  2559 I PlayerBar: setThumb() normal execute
14:54:03.869  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref++: 3 
14:54:03.869  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref--: 2 
14:54:03.869  2559  2559 I kradio.playerbar: onPlayerFailed: what=404 extra=404
14:54:03.870  2559  2559 I NetworkUtil: hasNetwork internet = true valided = true
14:54:03.870  2559  2559 I ZHUTAG  : isNetworkAvailable------->networkUsable = true
14:54:03.870  2559  2559 I showToast: msg = 该资源暂不支持播放，试一试其他的吧
14:54:03.870  2559  2559 D kaolafm.clientsdk: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.871  2559  2559 D kaolafm.clientsdk: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.871  2559  2559 D kaolafm.clientsdk: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.871  2559  2559 D kaolafm.clientsdk: │ KRadioToastImpl.showToast  (KRadioToastImpl.java:29)
14:54:03.871  2559  2559 D kaolafm.clientsdk: │    KRadioToastImpl.dismiss  (KRadioToastImpl.java:53)
14:54:03.871  2559  2559 D kaolafm.clientsdk: │       Logging$Log.d  (Logging.java:198)
14:54:03.871  2559  2559 D kaolafm.clientsdk: │          Logging.d  (Logging.java:106)
14:54:03.871  2559  2559 D kaolafm.clientsdk: │             Logging.log  (Logging.java:49)
14:54:03.871  2559  2559 D kaolafm.clientsdk: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.871  2559  2559 D kaolafm.clientsdk: │ dismiss mToast
14:54:03.871  2559  2559 D kaolafm.clientsdk: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.872  2559  2559 D ToastHandler: ---------isIndeterminate----->true
14:54:03.872  2559  2559 I HomePlayerSecondPlayerStateListenerWrapper: onPlayerFailed: error = 404 , 404
14:54:03.872  2559  2559 I kradio.home:  onPlayerFailed
14:54:03.872  2559  2559 I kradio.home: onPlayerFailed: what=404 extra=404
14:54:03.878  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.878  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.878  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.878  2559  2559 D report_tag: │ -$$Lambda$ReportManager$e9y5kvKRE3xu4RDEGmvmdM-oJF0.accept  (null:4)
14:54:03.878  2559  2559 D report_tag: │    ReportManager.lambda$reportRequestError$2  (ReportManager.java:518)
14:54:03.878  2559  2559 D report_tag: │       ReportHelper.addEvent  (ReportHelper.java:281)
14:54:03.878  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.878  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.878  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.878  2559  2559 D report_tag: │ 添加数据上报事件.是否立即:false事件id = 992000 json = {"audioid":"1000026798618","radioid":"1100002161226","remarks2":"","remarks3":"2","result":"404","speed":"0","action_id":"140","app_version":"2.10.4","app_version2":"210320088","appid":"eh7169","appid_type":"1","applicationcode":"17000","car_brand":"","carrier":"1","channel":"com.edog.car.ford_kradio","developer":"5548079","dsource":"vehicleeqv2","eventcode":"992000","imsi":"460000000000","ip":"*********","lat":"31.2378881115","lib_version":"1.6.0","lon":"121.476035847","manufacturer":"vivo","market_type":"","model":"V2366GA","network":"1","oem":"","openid":"eh71692023091210042759","operator":"1","os":"android","osversion":"12","page":"110000","playid":"9c2bc0573f8b53c39452aeff395d290e","report_timely":"0","screen_direction":"0","screen_height":"1080","screen_width":"2348","sessionid":"2","sourcetype":"3","timestamp":"1755845643856","udid":"5f5607f9a1dba516ce20e67af230f7c4","wifi":"1"}
14:54:03.878  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.878  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.879  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.879  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.880  2559  2559 D report_tag: │ -$$Lambda$ReportManager$e9y5kvKRE3xu4RDEGmvmdM-oJF0.accept  (null:4)
14:54:03.880  2559  2559 D report_tag: │    ReportManager.lambda$reportRequestError$2  (ReportManager.java:518)
14:54:03.880  2559  2559 D report_tag: │       ReportHelper.addEvent  (ReportHelper.java:300)
14:54:03.880  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.880  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.880  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.880  2559  2559 D report_tag: │ 上传到神策
14:54:03.880  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.880  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.881  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.881  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.881  2559  2559 D report_tag: │ ReportManager.lambda$reportRequestError$2  (ReportManager.java:518)
14:54:03.881  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:311)
14:54:03.881  2559  2559 D report_tag: │       ReportTaskHelper.insertTask  (ReportTaskHelper.java:40)
14:54:03.881  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.881  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.881  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.881  2559  2559 D report_tag: │ 队列大小 = 0
14:54:03.881  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.881  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.881  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.881  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.881  2559  2559 D report_tag: │ ReportManager.lambda$reportRequestError$2  (ReportManager.java:518)
14:54:03.881  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:311)
14:54:03.881  2559  2559 D report_tag: │       ReportTaskHelper.insertTask  (ReportTaskHelper.java:44)
14:54:03.881  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.881  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.881  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.881  2559  2559 D report_tag: │ 没有再运行
14:54:03.881  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.881  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.881  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.881  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.882  2559  2559 D report_tag: │ ReportTaskHelper.insertTask  (ReportTaskHelper.java:46)
14:54:03.882  2559  2559 D report_tag: │    ReportTaskHelper.run  (ReportTaskHelper.java:63)
14:54:03.882  2559  2559 D report_tag: │       ReportTaskHelper.getTask  (ReportTaskHelper.java:55)
14:54:03.882  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.882  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.882  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.882  2559  2559 D report_tag: │ 获取队列大小 = 1
14:54:03.882  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.882  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.882  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.882  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.882  2559  2559 D report_tag: │ ReportHelper.addEvent  (ReportHelper.java:311)
14:54:03.882  2559  2559 D report_tag: │    ReportTaskHelper.insertTask  (ReportTaskHelper.java:46)
14:54:03.882  2559  2559 D report_tag: │       ReportTaskHelper.run  (ReportTaskHelper.java:74)
14:54:03.882  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.882  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.882  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.882  2559  2559 D report_tag: │ 插入数据
14:54:03.882  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.884  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.884  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.884  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.884  2559  2559 D report_tag: │ ConsumerSingleObserver.onSuccess  (ConsumerSingleObserver.java:63)
14:54:03.884  2559  2559 D report_tag: │    -$$Lambda$ReportTaskHelper$0_306wrLG-ZqLBvoT6kuPoy_UZk.accept  (null:4)
14:54:03.884  2559  2559 D report_tag: │       ReportTaskHelper.lambda$run$0  (ReportTaskHelper.java:76)
14:54:03.884  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.884  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.884  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.884  2559  2559 D report_tag: │ 数据库大小 = 2
14:54:03.884  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.884  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.884  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.884  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.884  2559  2559 D report_tag: │ ReportTaskHelper.taskDone  (ReportTaskHelper.java:51)
14:54:03.884  2559  2559 D report_tag: │    ReportTaskHelper.run  (ReportTaskHelper.java:63)
14:54:03.885  2559  2559 D report_tag: │       ReportTaskHelper.getTask  (ReportTaskHelper.java:55)
14:54:03.885  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.885  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.885  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.885  2559  2559 D report_tag: │ 获取队列大小 = 0
14:54:03.885  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.885  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:03.885  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.ad.AdComponent-com.edog.car:18
14:54:03.885  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.885  2559  2559 D report_tag: │ ReportTaskHelper.lambda$run$0  (ReportTaskHelper.java:80)
14:54:03.885  2559  2559 D report_tag: │    ReportTaskHelper.taskDone  (ReportTaskHelper.java:51)
14:54:03.885  2559  2559 D report_tag: │       ReportTaskHelper.run  (ReportTaskHelper.java:65)
14:54:03.885  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:03.885  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:03.885  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:03.885  2559  2559 D report_tag: │ 没有任何任务了. 停止运行
14:54:03.885  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:04.523  2559  2890 V IJKMEDIA: read thread: run in read loop - 471397
14:54:06.264  2559  3165 I SocketClient: EVENT_PACKET----->type = pong---->data = null
14:54:06.512  2559  2591 D NetworkManager: Socket test thread is running!
14:54:06.512  2559  2591 D NetworkManager: Socket test start
14:54:06.528  2559  2591 D NetworkManager: Socket test finish
14:54:06.597  2559  2559 I PlayerBar: mPlayerBar onClick
14:54:06.598  2559  2559 I PlayerBar: mPlayerBar onClick be executed
14:54:06.599  2559  2559 I PageJumper: jumpToPlayerFragment RadioPlayerFragment start
14:54:06.605  2559  2559 D ActionQueue: enqueue nullaction = ACTION_NORMAL duration = 0
14:54:06.606  2559  2559 D ActionQueue: enqueueAction mQueue.size() = 0
14:54:06.606  2559  2559 D ActionQueue: executeNextAction postDelayed mQueue.size() = 1
14:54:06.607  2559  2559 I BaseFragment: isNeedAnimation:true
14:54:06.609  2559  2559 I RadioPlayerPresenter: [RPP]初始化动态监听
14:54:06.609  2559  2559 I player.subscribe: addSubscribeChangeListener: listener=
14:54:06.610  2559  2559 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.610  2559  2559 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:19
14:54:06.610  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.610  2559  2559 D K-radio : │ RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.610  2559  2559 D K-radio : │    ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.610  2559  2559 D K-radio : │       VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.610  2559  2559 D K-radio : │          ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.610  2559  2559 D K-radio : │             CallServiceInterceptor.intercept  (CallServiceInterceptor.java:59)
14:54:06.610  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.610  2559  2559 D K-radio : │ intercept: com.kaolafm.kradio.component.CallServiceInterceptor$CallServiceRunnable@aa30f60不需要切换线程，直接运行。
14:54:06.610  2559  2559 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.611  2559  2559 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.611  2559  2559 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:19
14:54:06.611  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.611  2559  2559 D K-radio : │ ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.611  2559  2559 D K-radio : │    VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.611  2559  2559 D K-radio : │       ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.611  2559  2559 D K-radio : │          CallServiceInterceptor.intercept  (CallServiceInterceptor.java:60)
14:54:06.611  2559  2559 D K-radio : │             CallServiceInterceptor$CallServiceRunnable.run  (CallServiceInterceptor.java:101)
14:54:06.611  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.612  2559  2559 D K-radio : │ run: com.kaolafm.kradio.subscribe.SubscribeComponent@f78c128不是异步获取结果，且没有收到结果，发送ERROR_NULL_RESULT
14:54:06.612  2559  2559 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.612  2559  2559 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.612  2559  2559 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:19
14:54:06.612  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.612  2559  2559 D K-radio : │ RealCaller.call  (RealCaller.java:77)
14:54:06.612  2559  2559 D K-radio : │    RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.612  2559  2559 D K-radio : │       ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.612  2559  2559 D K-radio : │          VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.612  2559  2559 D K-radio : │             ComponentChain.proceed  (ComponentChain.java:69)
14:54:06.612  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.612  2559  2559 D K-radio : │ proceed: result=-3
14:54:06.612  2559  2559 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.612  2559  2559 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.612  2559  2559 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:19
14:54:06.612  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.612  2559  2559 D K-radio : │ ComponentUtil.addObserver  (ComponentUtil.java:231)
14:54:06.612  2559  2559 D K-radio : │    ComponentClient.call  (ComponentClient.java:114)
14:54:06.612  2559  2559 D K-radio : │       RealCaller.call  (RealCaller.java:77)
14:54:06.613  2559  2559 D K-radio : │          RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.613  2559  2559 D K-radio : │             ComponentChain.proceed  (ComponentChain.java:69)
14:54:06.613  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.613  2559  2559 D K-radio : │ proceed: result=-3
14:54:06.613  2559  2559 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.614  2559  2559 D FragmentName: onCreate FordRadioPlayerFragment =======>> LauncherActivity
14:54:06.615  2559  2559 I RadioPlayerFragment: PlayerBar uiPosition = 0,uiDuration = 1657469
14:54:06.616  2559  2559 D FragmentName: onHiddenChanged,hidden = true fragment = HorizontalHomePlayerFragment
14:54:06.616  2559  2559 D FragmentName: onHiddenChanged=true
14:54:06.616  2559  2559 D FragmentName: HorizontalHomePlayerFragment{e1d55fc #1 id=0x7f090105 com.kaolafm.kradio.k_kaolafm.home.HorizontalHomePlayerFragment}=onVisibleChanged=false
14:54:06.616  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.616  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:19
14:54:06.616  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.617  2559  2559 D report_tag: │ BaseFragment.reportPageShowEvent  (BaseFragment.java:478)
14:54:06.617  2559  2559 D report_tag: │    PageShowReportEvent.<init>  (PageShowReportEvent.java:15)
14:54:06.617  2559  2559 D report_tag: │       BaseReportEventBean.<init>  (BaseReportEventBean.java:113)
14:54:06.617  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.617  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.617  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.617  2559  2559 D report_tag: │ 获取版本名称: 2.10.4, 版本号: 210320088
14:54:06.617  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.617  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.617  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:19
14:54:06.617  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.617  2559  2559 D report_tag: │ ReportParameterManager.getActionId  (ReportParameterManager.java:355)
14:54:06.617  2559  2559 D report_tag: │    ReportParameterManager.savePrivateParameter  (ReportParameterManager.java:454)
14:54:06.617  2559  2559 D report_tag: │       ReportHelper.savePrivateParameter  (ReportHelper.java:493)
14:54:06.617  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.617  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.617  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.617  2559  2559 D report_tag: │ savePrivateParameter
14:54:06.617  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.620  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.620  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:19
14:54:06.620  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.620  2559  2559 D report_tag: │ BaseFragment.reportPageShowEvent  (BaseFragment.java:481)
14:54:06.620  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:255)
14:54:06.620  2559  2559 D report_tag: │       ReportHelper.addEvent  (ReportHelper.java:281)
14:54:06.620  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.620  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.620  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.620  2559  2559 D report_tag: │ 添加数据上报事件.是否立即:false事件id = 210004 json = {"pageid":"110000","pagetime":"375273","action_id":"141","app_version":"2.10.4","app_version2":"210320088","appid":"eh7169","appid_type":"1","applicationcode":"17000","car_brand":"","carrier":"1","channel":"com.edog.car.ford_kradio","developer":"5548079","dsource":"vehicleeqv2","eventcode":"210004","imsi":"460000000000","ip":"*********","lat":"31.2378881115","lib_version":"1.6.0","lon":"121.476035847","manufacturer":"vivo","market_type":"","model":"V2366GA","network":"1","oem":"","openid":"eh71692023091210042759","operator":"1","os":"android","osversion":"12","page":"110000","playid":"9c2bc0573f8b53c39452aeff395d290e","report_timely":"0","screen_direction":"0","screen_height":"1080","screen_width":"2348","sessionid":"2","sourcetype":"3","timestamp":"1755845646616","udid":"5f5607f9a1dba516ce20e67af230f7c4","wifi":"1"}
14:54:06.620  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.620  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.620  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:19
14:54:06.620  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.620  2559  2559 D report_tag: │ BaseFragment.reportPageShowEvent  (BaseFragment.java:481)
14:54:06.620  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:255)
14:54:06.620  2559  2559 D report_tag: │       ReportHelper.addEvent  (ReportHelper.java:300)
14:54:06.620  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.620  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.620  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.620  2559  2559 D report_tag: │ 上传到神策
14:54:06.620  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.620  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.620  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:19
14:54:06.620  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.620  2559  2559 D report_tag: │ ReportHelper.addEvent  (ReportHelper.java:255)
14:54:06.620  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:311)
14:54:06.620  2559  2559 D report_tag: │       ReportTaskHelper.insertTask  (ReportTaskHelper.java:40)
14:54:06.620  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.620  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.620  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.620  2559  2559 D report_tag: │ 队列大小 = 0
14:54:06.620  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.620  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.620  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:19
14:54:06.620  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.620  2559  2559 D report_tag: │ ReportHelper.addEvent  (ReportHelper.java:255)
14:54:06.620  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:311)
14:54:06.620  2559  2559 D report_tag: │       ReportTaskHelper.insertTask  (ReportTaskHelper.java:44)
14:54:06.620  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.620  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.620  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.620  2559  2559 D report_tag: │ 没有再运行
14:54:06.620  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.620  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.620  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:19
14:54:06.620  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.620  2559  2559 D report_tag: │ ReportTaskHelper.insertTask  (ReportTaskHelper.java:46)
14:54:06.620  2559  2559 D report_tag: │    ReportTaskHelper.run  (ReportTaskHelper.java:63)
14:54:06.620  2559  2559 D report_tag: │       ReportTaskHelper.getTask  (ReportTaskHelper.java:55)
14:54:06.620  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.620  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.620  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.620  2559  2559 D report_tag: │ 获取队列大小 = 1
14:54:06.620  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.620  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.621  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:19
14:54:06.621  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.621  2559  2559 D report_tag: │ ReportHelper.addEvent  (ReportHelper.java:311)
14:54:06.621  2559  2559 D report_tag: │    ReportTaskHelper.insertTask  (ReportTaskHelper.java:46)
14:54:06.621  2559  2559 D report_tag: │       ReportTaskHelper.run  (ReportTaskHelper.java:74)
14:54:06.621  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.621  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.621  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.621  2559  2559 D report_tag: │ 插入数据
14:54:06.621  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.621  2559  2559 D FragmentName: report=375273
14:54:06.621  2559  2559 I kradio.home: hidden: true
14:54:06.622  2559  2559 E k.coin  : stop: 
14:54:06.681  2559  2559 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.681  2559  2559 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:20
14:54:06.681  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.681  2559  2559 D K-radio : │ RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.681  2559  2559 D K-radio : │    ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.681  2559  2559 D K-radio : │       VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.681  2559  2559 D K-radio : │          ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.681  2559  2559 D K-radio : │             CallServiceInterceptor.intercept  (CallServiceInterceptor.java:59)
14:54:06.681  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.681  2559  2559 D K-radio : │ intercept: com.kaolafm.kradio.component.CallServiceInterceptor$CallServiceRunnable@6c36c4e不需要切换线程，直接运行。
14:54:06.681  2559  2559 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.682  2559  2559 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.682  2559  2559 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:20
14:54:06.682  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.682  2559  2559 D K-radio : │ ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.682  2559  2559 D K-radio : │    VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.682  2559  2559 D K-radio : │       ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.682  2559  2559 D K-radio : │          CallServiceInterceptor.intercept  (CallServiceInterceptor.java:60)
14:54:06.682  2559  2559 D K-radio : │             CallServiceInterceptor$CallServiceRunnable.run  (CallServiceInterceptor.java:101)
14:54:06.682  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.682  2559  2559 D K-radio : │ run: com.kaolafm.kradio.user.UserComponent@d96fc74不是异步获取结果，且没有收到结果，发送ERROR_NULL_RESULT
14:54:06.682  2559  2559 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.682  2559  2559 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.683  2559  2559 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:20
14:54:06.683  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.683  2559  2559 D K-radio : │ RealCaller.call  (RealCaller.java:77)
14:54:06.683  2559  2559 D K-radio : │    RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.683  2559  2559 D K-radio : │       ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.683  2559  2559 D K-radio : │          VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.683  2559  2559 D K-radio : │             ComponentChain.proceed  (ComponentChain.java:69)
14:54:06.683  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.683  2559  2559 D K-radio : │ proceed: result=-3
14:54:06.683  2559  2559 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.683  2559  2559 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.683  2559  2559 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:20
14:54:06.683  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.683  2559  2559 D K-radio : │ ComponentUtil.addObserver  (ComponentUtil.java:231)
14:54:06.683  2559  2559 D K-radio : │    ComponentClient.call  (ComponentClient.java:114)
14:54:06.683  2559  2559 D K-radio : │       RealCaller.call  (RealCaller.java:77)
14:54:06.684  2559  2559 D K-radio : │          RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.684  2559  2559 D K-radio : │             ComponentChain.proceed  (ComponentChain.java:69)
14:54:06.684  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.684  2559  2559 D K-radio : │ proceed: result=-3
14:54:06.684  2559  2559 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.685  2559  3166 D Gabe    : call	returning from cache
14:54:06.687  2559  3166 D Gabe    : call	returning from cache
14:54:06.689  2559  3166 D Gabe    : call	returning from cache
14:54:06.690  2559  3166 D Gabe    : call	returning from cache
14:54:06.691  2559  2559 I RadioPlayListContent: convertToAdapterData
14:54:06.693  2559  2559 I RadioPlayerFragment: setPlayBarThumb() start
14:54:06.693  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref++: 3 
14:54:06.693  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref--: 2 
14:54:06.693  2559  2559 I RadioPlayerFragment: setPlayBarThumb() isPlaying = false
14:54:06.694  2559  2559 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.694  2559  2559 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.694  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.694  2559  2559 D K-radio : │ RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.694  2559  2559 D K-radio : │    ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.694  2559  2559 D K-radio : │       VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.694  2559  2559 D K-radio : │          ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.694  2559  2559 D K-radio : │             CallServiceInterceptor.intercept  (CallServiceInterceptor.java:59)
14:54:06.694  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.694  2559  2559 D K-radio : │ intercept: com.kaolafm.kradio.component.CallServiceInterceptor$CallServiceRunnable@7dc48b9不需要切换线程，直接运行。
14:54:06.694  2559  2559 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.695  2559  2559 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.695  2559  2559 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.695  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.695  2559  2559 D K-radio : │ ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.695  2559  2559 D K-radio : │    VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.695  2559  2559 D K-radio : │       ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.695  2559  2559 D K-radio : │          CallServiceInterceptor.intercept  (CallServiceInterceptor.java:60)
14:54:06.695  2559  2559 D K-radio : │             CallServiceInterceptor$CallServiceRunnable.run  (CallServiceInterceptor.java:101)
14:54:06.695  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.696  2559  2559 D K-radio : │ run: com.kaolafm.kradio.user.UserComponent@d96fc74不是异步获取结果，且没有收到结果，发送ERROR_NULL_RESULT
14:54:06.696  2559  2559 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.696  2559  2559 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.696  2559  2559 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.696  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.696  2559  2559 D K-radio : │ RealCaller.call  (RealCaller.java:77)
14:54:06.696  2559  2559 D K-radio : │    RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.696  2559  2559 D K-radio : │       ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.696  2559  2559 D K-radio : │          VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.696  2559  2559 D K-radio : │             ComponentChain.proceed  (ComponentChain.java:69)
14:54:06.696  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.696  2559  2559 D K-radio : │ proceed: result=-3
14:54:06.696  2559  2559 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.696  2559  2559 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.696  2559  2559 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.696  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.696  2559  2559 D K-radio : │ ComponentUtil.addObserver  (ComponentUtil.java:231)
14:54:06.696  2559  2559 D K-radio : │    ComponentClient.call  (ComponentClient.java:114)
14:54:06.696  2559  2559 D K-radio : │       RealCaller.call  (RealCaller.java:77)
14:54:06.696  2559  2559 D K-radio : │          RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.696  2559  2559 D K-radio : │             ComponentChain.proceed  (ComponentChain.java:69)
14:54:06.696  2559  2559 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.696  2559  2559 D K-radio : │ proceed: result=-3
14:54:06.696  2559  2559 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.697  2559  2559 I RadioPlayerFragment: attachPlayer,CurrentPlayItem = PlayItem{audioId=1000026798618, playUrl='null', playUrlId='9223371036827977189', position=0, buyType=1, buyStatus=0, audition=1, vip=0, fine=1, duration=95000, mDataMap={}, payMethod=[AlbumDetailsPayMethod{payTypeName='人民币', buyNotice='null', payType=1, originPrice=20, currentPrice=null}]}
14:54:06.697  2559  2559 I RadioPlayerFragment:  onPlayerFailed
14:54:06.703  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref++: 3 
14:54:06.703  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref--: 2 
14:54:06.703  2559  2559 I RadioPlayerFragment: onPlayerFailed: what=0 extra=0
14:54:06.703  2559  2559 I ToastAop: toastShowOnActivity: isAppOnForeground= true
14:54:06.703  2559  2559 I ToastAop: toastShowOnActivity: msg  instanceof String 
14:54:06.703  2559  2559 I showToast: msg = 该资源暂不支持播放，试一试其他的吧
14:54:06.703  2559  2559 D kaolafm.clientsdk: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.703  2559  2559 D kaolafm.clientsdk: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.703  2559  2559 D kaolafm.clientsdk: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.703  2559  2559 D kaolafm.clientsdk: │ KRadioToastImpl.showToast  (KRadioToastImpl.java:29)
14:54:06.703  2559  2559 D kaolafm.clientsdk: │    KRadioToastImpl.dismiss  (KRadioToastImpl.java:53)
14:54:06.703  2559  2559 D kaolafm.clientsdk: │       Logging$Log.d  (Logging.java:198)
14:54:06.704  2559  2559 D kaolafm.clientsdk: │          Logging.d  (Logging.java:106)
14:54:06.704  2559  2559 D kaolafm.clientsdk: │             Logging.log  (Logging.java:49)
14:54:06.704  2559  2559 D kaolafm.clientsdk: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.704  2559  2559 D kaolafm.clientsdk: │ dismiss mToast
14:54:06.704  2559  2559 D kaolafm.clientsdk: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.706  2559  2559 D ToastHandler: ---------isIndeterminate----->true
14:54:06.709  2559  2559 I RadioPlayerFragment: showPlayerTitle 1100002161226--1100002161226--大江东去
14:54:06.709  2559  2559 D kradio.img: 设置图片分辨率:getCustomPicUrl: type=
14:54:06.712  2559  2559 I NetworkUtil: hasNetwork internet = true valided = true
14:54:06.712  2559  2559 I ZHUTAG  : isNetworkAvailable------->networkUsable = true
14:54:06.712  2559  2559 I RadioPlayerFragment: 是否已经订阅= false
14:54:06.713  2559  3171 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.714  2559  2559 D EventBus: No subscribers registered for event class com.kaolafm.kradio.common.event.PlayerSubscribeResultEBData
14:54:06.714  2559  2559 D EventBus: No subscribers registered for event class org.greenrobot.eventbus.NoSubscriberEvent
14:54:06.714  2559  3171 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:22
14:54:06.714  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.714  2559  3171 D K-radio : │ RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.714  2559  3171 D K-radio : │    ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.714  2559  3171 D K-radio : │       VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.714  2559  3171 D K-radio : │          ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.714  2559  3171 D K-radio : │             CallServiceInterceptor.intercept  (CallServiceInterceptor.java:59)
14:54:06.714  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.714  2559  3171 D K-radio : │ intercept: com.kaolafm.kradio.component.CallServiceInterceptor$CallServiceRunnable@3ad5057不需要切换线程，直接运行。
14:54:06.714  2559  3171 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.715  2559  3171 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.715  2559  2559 I RadioPlayerFragment: onPlayerFailed...
14:54:06.715  2559  2559 I RadioPlayerFragment: setPlayBarThumb() start
14:54:06.715  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref++: 3 
14:54:06.715  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref--: 2 
14:54:06.715  2559  3171 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:22
14:54:06.715  2559  2559 I RadioPlayerFragment: setPlayBarThumb() isPlaying = false
14:54:06.715  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.715  2559  3171 D K-radio : │ ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.715  2559  3171 D K-radio : │    VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.715  2559  3171 D K-radio : │       ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.715  2559  3171 D K-radio : │          CallServiceInterceptor.intercept  (CallServiceInterceptor.java:60)
14:54:06.715  2559  3171 D K-radio : │             CallServiceInterceptor$CallServiceRunnable.run  (CallServiceInterceptor.java:105)
14:54:06.715  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.715  2559  3171 D K-radio : │ run: com.kaolafm.kradio.subscribe.SubscribeComponent@f78c128异步获取结果，等待结果
14:54:06.715  2559  3171 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.715  2559  3171 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.715  2559  3171 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:22
14:54:06.715  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.715  2559  3171 D K-radio : │ RealCaller.access$300  (RealCaller.java:20)
14:54:06.715  2559  3171 D K-radio : │    RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.715  2559  3171 D K-radio : │       ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.715  2559  3171 D K-radio : │          VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.715  2559  3171 D K-radio : │             ComponentChain.proceed  (ComponentChain.java:69)
14:54:06.715  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.715  2559  3171 D K-radio : │ proceed: result=0
14:54:06.715  2559  3171 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.716  2559  3171 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.716  2559  3171 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:22
14:54:06.716  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.716  2559  3171 D K-radio : │ NamedRunnable.run  (NamedRunnable.java:32)
14:54:06.716  2559  3171 D K-radio : │    RealCaller$AsyncRunnable.execute  (RealCaller.java:371)
14:54:06.716  2559  3171 D K-radio : │       RealCaller.access$300  (RealCaller.java:20)
14:54:06.716  2559  3171 D K-radio : │          RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.716  2559  3171 D K-radio : │             ComponentChain.proceed  (ComponentChain.java:69)
14:54:06.716  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.716  2559  3171 D K-radio : │ proceed: result=0
14:54:06.716  2559  3171 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.717  2559  2559 I NetworkUtil: hasNetwork internet = true valided = true
14:54:06.717  2559  2559 I ZHUTAG  : isNetworkAvailable------->networkUsable = true
14:54:06.717  2559  2559 I ToastAop: toastShowOnActivity: isAppOnForeground= true
14:54:06.717  2559  2559 I ToastAop: toastShowOnActivity: msg  instanceof String 
14:54:06.717  2559  2559 I showToast: msg = 该资源暂不支持播放，试一试其他的吧
14:54:06.717  2559  2559 D kaolafm.clientsdk: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.717  2559  2559 D kaolafm.clientsdk: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.718  2559  2559 D kaolafm.clientsdk: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.718  2559  2559 D kaolafm.clientsdk: │ KRadioToastImpl.showToast  (KRadioToastImpl.java:29)
14:54:06.718  2559  2559 D kaolafm.clientsdk: │    KRadioToastImpl.dismiss  (KRadioToastImpl.java:53)
14:54:06.718  2559  2559 D kaolafm.clientsdk: │       Logging$Log.d  (Logging.java:198)
14:54:06.718  2559  2559 D kaolafm.clientsdk: │          Logging.d  (Logging.java:106)
14:54:06.718  2559  2559 D kaolafm.clientsdk: │             Logging.log  (Logging.java:49)
14:54:06.718  2559  2559 D kaolafm.clientsdk: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.718  2559  2559 D kaolafm.clientsdk: │ dismiss mToast
14:54:06.718  2559  2559 D kaolafm.clientsdk: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.720  2559  2559 D ToastHandler: ---------isIndeterminate----->true
14:54:06.722  2559  2559 I NetworkUtil: hasNetwork internet = true valided = true
14:54:06.722  2559  2559 I ZHUTAG  : isNetworkAvailable------->networkUsable = true
14:54:06.722  2559  2559 I RadioPlayerPresenter: initPayInfo albumId: 1100002161226
14:54:06.724  2559  3011 I RequestInterceptManager: intercept url= https://iovopen.radio.cn/v2/album/detail?ids=1100002161226&capabilities=NEW_DOMAIN_SUPPORTTED%2CPAY_CONTENT_SUPPORTTED%2CMEDIA_URL_MUST_BE_HTTPS&os=android&openid=eh71692023091210042759&packagename=com.edog.car.ford_kradio&sign=a3d018f64aa8125f3874fe44e756c6ff&channel=ford_kradio&deviceid=5f5607f9a1dba516ce20e67af230f7c4&version=2.10.4.0088&sdkversion=1.6.0&carType=PD2366&appid=eh7169&udid=5f5607f9a1dba516ce20e67af230f7c4&access_token=***&lat=***&lng=***
14:54:06.725  2559  3011 I RequestInterceptManager: 替换完成 url= https://iovopen.radio.cn/v2/album/detail?ids=1100002161226&capabilities=NEW_DOMAIN_SUPPORTTED%2CPAY_CONTENT_SUPPORTTED%2CMEDIA_URL_MUST_BE_HTTPS&os=android&openid=eh71692023091210042759&packagename=com.edog.car.ford_kradio&sign=a3d018f64aa8125f3874fe44e756c6ff&channel=ford_kradio&deviceid=5f5607f9a1dba516ce20e67af230f7c4&version=2.10.4.0088&sdkversion=1.6.0&carType=PD2366&appid=eh7169&udid=5f5607f9a1dba516ce20e67af230f7c4&access_token=***&lat=***&lng=*** timestamp:1755845646725
14:54:06.726  2559  3011 D K-radio-Request: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.726  2559  3011 D K-radio-Request: │ Thread: RxCachedThreadScheduler-7
14:54:06.726  2559  3011 D K-radio-Request: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.726  2559  3011 D K-radio-Request: │ ConnectInterceptor.intercept  (ConnectInterceptor.java:45)
14:54:06.726  2559  3011 D K-radio-Request: │    RealInterceptorChain.proceed  (RealInterceptorChain.java:147)
14:54:06.726  2559  3011 D K-radio-Request: │       RequestInterceptor.intercept  (RequestInterceptor.java:56)
14:54:06.726  2559  3011 D K-radio-Request: │          FormatPrinter.printFileRequest  (FormatPrinter.java:100)
14:54:06.726  2559  3011 D K-radio-Request: │             FormatPrinter.logInfo  (FormatPrinter.java:277)
14:54:06.726  2559  3011 D K-radio-Request: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.726  2559  3011 D K-radio-Request: │ ┌────── Request ────────────────────────────────────────────────────────────────────────
14:54:06.726  2559  3011 D K-radio-Request: │ │ URL: https://iovopen.radio.cn/v2/album/detail?ids=1100002161226&capabilities=NEW_DOMAIN_SUPPORTTED%2CPAY_CONTENT_SUPPORTTED%2CMEDIA_URL_MUST_BE_HTTPS&os=android&lng=121.476035847&openid=eh71692023091210042759&packagename=com.edog.car.ford_kradio&sign=a3d018f64aa8125f3874fe44e756c6ff&channel=ford_kradio&deviceid=5f5607f9a1dba516ce20e67af230f7c4&version=2.10.4.0088&sdkversion=1.6.0&carType=PD2366&appid=eh7169&udid=5f5607f9a1dba516ce20e67af230f7c4&lat=31.2378881115
14:54:06.726  2559  3011 D K-radio-Request: │ │ 
14:54:06.726  2559  3011 D K-radio-Request: │ │ Method: @GET
14:54:06.726  2559  3011 D K-radio-Request: │ │ 
14:54:06.726  2559  3011 D K-radio-Request: │ │ Headers:
14:54:06.726  2559  3011 D K-radio-Request: │ │ ┌ Host: iovopen.radio.cn
14:54:06.726  2559  3011 D K-radio-Request: │ │ ├ Connection: Keep-Alive
14:54:06.726  2559  3011 D K-radio-Request: │ │ ├ Accept-Encoding: gzip
14:54:06.726  2559  3011 D K-radio-Request: │ │ └ User-Agent: okhttp/3.12.12
14:54:06.726  2559  3011 D K-radio-Request: │ │ Omitted request body
14:54:06.726  2559  3011 D K-radio-Request: │ └───────────────────────────────────────────────────────────────────────────────────────
14:54:06.726  2559  3011 D K-radio-Request: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.727  2559  2559 D FragmentName: onSaveInstanceState start FordRadioPlayerFragment{c676a78 #2 id=0x7f090105 com.kaolafm.kradio.widget.view.FordRadioPlayerFragment}
14:54:06.727  2559  2559 D report_tag: 设置pageid= 141200
14:54:06.727  2559  2559 D FragmentName: onResume FordRadioPlayerFragment =======>> LauncherActivity
14:54:06.727  2559  2559 I RadioPlayerFragment: onResume
14:54:06.727  2559  2559 I RadioPlayerFragment: loinStateChange:false vipStateChange:false
14:54:06.727  2559  2559 D ActionQueue: executeNextAction before poll mQueue.size() = 1
14:54:06.727  2559  2559 D ActionQueue: executeNextAction after poll mQueue.size() = 0
14:54:06.727  2559  2559 D ActionQueue: handleAction mQueue is empty
14:54:06.735  2559  2559 D BaseAdapter:  PlayerListAdapter
14:54:06.737  2559  3166 D Gabe    : call	returning from cache
14:54:06.744  2559  2559 I PlayListContent: updateData playItem = 《大江东去》预告片花
14:54:06.747  2559  2559 D RadioPlayTag: not contains key=1000026798618
14:54:06.748  2559  2559 I PlayListContent: updateProgress elapsed: 0 total = 95000
14:54:06.748  2559  3171 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.748  2559  3171 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:23
14:54:06.748  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.748  2559  3171 D K-radio : │ RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.748  2559  3171 D K-radio : │    ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.748  2559  3171 D K-radio : │       VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.748  2559  3171 D K-radio : │          ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.748  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref++: 3 
14:54:06.748  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref--: 2 
14:54:06.748  2559  3171 D K-radio : │             CallServiceInterceptor.intercept  (CallServiceInterceptor.java:59)
14:54:06.748  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.748  2559  3171 D K-radio : │ intercept: com.kaolafm.kradio.component.CallServiceInterceptor$CallServiceRunnable@4d8eaf4不需要切换线程，直接运行。
14:54:06.748  2559  3171 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.748  2559  3171 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.748  2559  3171 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:23
14:54:06.748  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.748  2559  3171 D K-radio : │ ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.748  2559  3171 D K-radio : │    VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.748  2559  3171 D K-radio : │       ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.748  2559  3171 D K-radio : │          CallServiceInterceptor.intercept  (CallServiceInterceptor.java:60)
14:54:06.748  2559  3171 D K-radio : │             CallServiceInterceptor$CallServiceRunnable.run  (CallServiceInterceptor.java:105)
14:54:06.748  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.748  2559  3171 D K-radio : │ run: com.kaolafm.kradio.subscribe.SubscribeComponent@f78c128异步获取结果，等待结果
14:54:06.748  2559  3171 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.748  2559  3171 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.748  2559  3171 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:23
14:54:06.748  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.748  2559  3171 D K-radio : │ RealCaller.access$300  (RealCaller.java:20)
14:54:06.748  2559  3171 D K-radio : │    RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.749  2559  3171 D K-radio : │       ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.749  2559  3171 D K-radio : │          VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.749  2559  3171 D K-radio : │             ComponentChain.proceed  (ComponentChain.java:69)
14:54:06.749  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.749  2559  3171 D K-radio : │ proceed: result=0
14:54:06.749  2559  3171 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.749  2559  3171 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.749  2559  3171 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:23
14:54:06.749  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.749  2559  3171 D K-radio : │ NamedRunnable.run  (NamedRunnable.java:32)
14:54:06.749  2559  3171 D K-radio : │    RealCaller$AsyncRunnable.execute  (RealCaller.java:371)
14:54:06.749  2559  3171 D K-radio : │       RealCaller.access$300  (RealCaller.java:20)
14:54:06.749  2559  3171 D K-radio : │          RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.749  2559  3171 D K-radio : │             ComponentChain.proceed  (ComponentChain.java:69)
14:54:06.749  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.749  2559  3171 D K-radio : │ proceed: result=0
14:54:06.749  2559  3171 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.750  2559  2559 D BaseAdapter:  PlayerListAdapter
14:54:06.751  2559  3166 D Gabe    : call	returning from cache
14:54:06.754  2559  2559 I PlayListContent: updateData playItem = 《大江东去》第01集
14:54:06.755  2559  2559 D RadioPlayTag: not contains key=1000026798619
14:54:06.756  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref++: 3 
14:54:06.756  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref--: 2 
14:54:06.756  2559  3171 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.756  2559  3171 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:24
14:54:06.756  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.756  2559  3171 D K-radio : │ RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.756  2559  3171 D K-radio : │    ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.756  2559  3171 D K-radio : │       VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.756  2559  3171 D K-radio : │          ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.756  2559  3171 D K-radio : │             CallServiceInterceptor.intercept  (CallServiceInterceptor.java:59)
14:54:06.756  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.756  2559  3171 D K-radio : │ intercept: com.kaolafm.kradio.component.CallServiceInterceptor$CallServiceRunnable@ac5c2a7不需要切换线程，直接运行。
14:54:06.756  2559  3171 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.756  2559  3171 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.756  2559  3171 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:24
14:54:06.756  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.756  2559  3171 D K-radio : │ ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.756  2559  3171 D K-radio : │    VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.756  2559  3171 D K-radio : │       ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.756  2559  3171 D K-radio : │          CallServiceInterceptor.intercept  (CallServiceInterceptor.java:60)
14:54:06.756  2559  3171 D K-radio : │             CallServiceInterceptor$CallServiceRunnable.run  (CallServiceInterceptor.java:105)
14:54:06.756  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.756  2559  3171 D K-radio : │ run: com.kaolafm.kradio.subscribe.SubscribeComponent@f78c128异步获取结果，等待结果
14:54:06.756  2559  3171 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.756  2559  3171 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.756  2559  3171 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:24
14:54:06.757  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.757  2559  3171 D K-radio : │ RealCaller.access$300  (RealCaller.java:20)
14:54:06.757  2559  3171 D K-radio : │    RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.757  2559  3171 D K-radio : │       ComponentChain.proceed  (ComponentChain.java:61)
14:54:06.757  2559  3171 D K-radio : │          VerifyInterceptor.intercept  (VerifyInterceptor.java:46)
14:54:06.757  2559  3171 D K-radio : │             ComponentChain.proceed  (ComponentChain.java:69)
14:54:06.757  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.757  2559  3171 D K-radio : │ proceed: result=0
14:54:06.757  2559  3171 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.757  2559  3171 D K-radio : ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.757  2559  3171 D K-radio : │ Thread: CallServiceRunnable-com.kaolafm.kradio.subscribe.SubscribeComponent-com.edog.car:24
14:54:06.757  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.757  2559  3171 D K-radio : │ NamedRunnable.run  (NamedRunnable.java:32)
14:54:06.757  2559  3171 D K-radio : │    RealCaller$AsyncRunnable.execute  (RealCaller.java:371)
14:54:06.757  2559  3171 D K-radio : │       RealCaller.access$300  (RealCaller.java:20)
14:54:06.757  2559  2559 D ExposeUtil: visible：0---1
14:54:06.757  2559  3171 D K-radio : │          RealCaller.getResultWithInterceptorChain  (RealCaller.java:325)
14:54:06.757  2559  3171 D K-radio : │             ComponentChain.proceed  (ComponentChain.java:69)
14:54:06.757  2559  3171 D K-radio : ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.757  2559  3171 D K-radio : │ proceed: result=0
14:54:06.757  2559  3171 D K-radio : └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.757  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.758  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.758  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.758  2559  2559 D report_tag: │ ReportManager.addContentShowEvent  (ReportManager.java:888)
14:54:06.758  2559  2559 D report_tag: │    ContentShowReportEvent.<init>  (ContentShowReportEvent.java:42)
14:54:06.758  2559  2559 D report_tag: │       BaseReportEventBean.<init>  (BaseReportEventBean.java:113)
14:54:06.758  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.758  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.758  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.758  2559  2559 D report_tag: │ 获取版本名称: 2.10.4, 版本号: 210320088
14:54:06.758  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.758  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.758  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.758  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.758  2559  2559 D report_tag: │ ReportParameterManager.getActionId  (ReportParameterManager.java:355)
14:54:06.758  2559  2559 D report_tag: │    ReportParameterManager.savePrivateParameter  (ReportParameterManager.java:454)
14:54:06.758  2559  2559 D report_tag: │       ReportHelper.savePrivateParameter  (ReportHelper.java:493)
14:54:06.758  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.758  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.758  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.758  2559  2559 D report_tag: │ savePrivateParameter
14:54:06.758  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.761  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.762  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.762  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.762  2559  2559 D report_tag: │ ReportManager.addContentShowEvent  (ReportManager.java:897)
14:54:06.762  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:255)
14:54:06.762  2559  2559 D report_tag: │       ReportHelper.addEvent  (ReportHelper.java:281)
14:54:06.762  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.762  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.762  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.762  2559  2559 D report_tag: │ 添加数据上报事件.是否立即:false事件id = 210005 json = {"audioid":"1000026798618","audioidtype":"2","pageid":"141200","radioid":"1100002161226","radiotype":"0","remarks1":"","remarks2":"","tag":"精品","action_id":"142","app_version":"2.10.4","app_version2":"210320088","appid":"eh7169","appid_type":"1","applicationcode":"17000","car_brand":"","carrier":"1","channel":"com.edog.car.ford_kradio","developer":"5548079","dsource":"vehicleeqv2","eventcode":"210005","imsi":"460000000000","ip":"*********","lat":"31.2378881115","lib_version":"1.6.0","lon":"121.476035847","manufacturer":"vivo","market_type":"","model":"V2366GA","network":"1","oem":"","openid":"eh71692023091210042759","operator":"1","os":"android","osversion":"12","page":"141200","playid":"9c2bc0573f8b53c39452aeff395d290e","report_timely":"0","screen_direction":"0","screen_height":"1080","screen_width":"2348","sessionid":"2","sourcetype":"3","timestamp":"1755845646757","udid":"5f5607f9a1dba516ce20e67af230f7c4","wifi":"1"}
14:54:06.762  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.762  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.762  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.762  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.762  2559  2559 D report_tag: │ ReportManager.addContentShowEvent  (ReportManager.java:897)
14:54:06.762  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:255)
14:54:06.762  2559  2559 D report_tag: │       ReportHelper.addEvent  (ReportHelper.java:300)
14:54:06.762  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.762  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.762  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.762  2559  2559 D report_tag: │ 上传到神策
14:54:06.762  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.762  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.762  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.762  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.762  2559  2559 D report_tag: │ ReportHelper.addEvent  (ReportHelper.java:255)
14:54:06.762  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:311)
14:54:06.762  2559  2559 D report_tag: │       ReportTaskHelper.insertTask  (ReportTaskHelper.java:40)
14:54:06.762  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.762  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.762  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.762  2559  2559 D report_tag: │ 队列大小 = 0
14:54:06.762  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.763  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.763  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.763  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.763  2559  2559 D report_tag: │ ReportManager.addContentShowEvent  (ReportManager.java:888)
14:54:06.763  2559  2559 D report_tag: │    ContentShowReportEvent.<init>  (ContentShowReportEvent.java:42)
14:54:06.763  2559  2559 D report_tag: │       BaseReportEventBean.<init>  (BaseReportEventBean.java:113)
14:54:06.763  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.763  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.763  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.763  2559  2559 D report_tag: │ 获取版本名称: 2.10.4, 版本号: 210320088
14:54:06.763  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.763  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.763  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.763  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.763  2559  2559 D report_tag: │ ReportParameterManager.getActionId  (ReportParameterManager.java:355)
14:54:06.763  2559  2559 D report_tag: │    ReportParameterManager.savePrivateParameter  (ReportParameterManager.java:454)
14:54:06.763  2559  2559 D report_tag: │       ReportHelper.savePrivateParameter  (ReportHelper.java:493)
14:54:06.763  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.763  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.763  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.763  2559  2559 D report_tag: │ savePrivateParameter
14:54:06.763  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.766  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.766  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.766  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.766  2559  2559 D report_tag: │ ReportManager.addContentShowEvent  (ReportManager.java:897)
14:54:06.766  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:255)
14:54:06.766  2559  2559 D report_tag: │       ReportHelper.addEvent  (ReportHelper.java:281)
14:54:06.766  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.766  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.766  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.766  2559  2559 D report_tag: │ 添加数据上报事件.是否立即:false事件id = 210005 json = {"audioid":"1000026798619","audioidtype":"2","pageid":"141200","radioid":"1100002161226","radiotype":"0","remarks1":"","remarks2":"","tag":"精品","action_id":"143","app_version":"2.10.4","app_version2":"210320088","appid":"eh7169","appid_type":"1","applicationcode":"17000","car_brand":"","carrier":"1","channel":"com.edog.car.ford_kradio","developer":"5548079","dsource":"vehicleeqv2","eventcode":"210005","imsi":"460000000000","ip":"*********","lat":"31.2378881115","lib_version":"1.6.0","lon":"121.476035847","manufacturer":"vivo","market_type":"","model":"V2366GA","network":"1","oem":"","openid":"eh71692023091210042759","operator":"1","os":"android","osversion":"12","page":"141200","playid":"9c2bc0573f8b53c39452aeff395d290e","report_timely":"0","screen_direction":"0","screen_height":"1080","screen_width":"2348","sessionid":"2","sourcetype":"3","timestamp":"1755845646763","udid":"5f5607f9a1dba516ce20e67af230f7c4","wifi":"1"}
14:54:06.766  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.766  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.766  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.766  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.766  2559  2559 D report_tag: │ ReportManager.addContentShowEvent  (ReportManager.java:897)
14:54:06.766  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:255)
14:54:06.766  2559  2559 D report_tag: │       ReportHelper.addEvent  (ReportHelper.java:300)
14:54:06.766  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.766  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.766  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.766  2559  2559 D report_tag: │ 上传到神策
14:54:06.766  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.766  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.766  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.766  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.766  2559  2559 D report_tag: │ ReportHelper.addEvent  (ReportHelper.java:255)
14:54:06.766  2559  2559 D report_tag: │    ReportHelper.addEvent  (ReportHelper.java:311)
14:54:06.766  2559  2559 D report_tag: │       ReportTaskHelper.insertTask  (ReportTaskHelper.java:40)
14:54:06.766  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.766  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.767  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.767  2559  2559 D report_tag: │ 队列大小 = 1
14:54:06.767  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.773  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.773  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.773  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.773  2559  2559 D report_tag: │ ConsumerSingleObserver.onSuccess  (ConsumerSingleObserver.java:63)
14:54:06.773  2559  2559 D report_tag: │    -$$Lambda$ReportTaskHelper$0_306wrLG-ZqLBvoT6kuPoy_UZk.accept  (null:4)
14:54:06.773  2559  2559 D report_tag: │       ReportTaskHelper.lambda$run$0  (ReportTaskHelper.java:76)
14:54:06.773  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.773  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.773  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.773  2559  2559 D report_tag: │ 数据库大小 = 3
14:54:06.773  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.773  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.773  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.773  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.773  2559  2559 D report_tag: │ ReportTaskHelper.taskDone  (ReportTaskHelper.java:51)
14:54:06.773  2559  2559 D report_tag: │    ReportTaskHelper.run  (ReportTaskHelper.java:63)
14:54:06.773  2559  2559 D report_tag: │       ReportTaskHelper.getTask  (ReportTaskHelper.java:55)
14:54:06.773  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.773  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.773  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.773  2559  2559 D report_tag: │ 获取队列大小 = 2
14:54:06.773  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.773  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.773  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.773  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.773  2559  2559 D report_tag: │ ReportTaskHelper.lambda$run$0  (ReportTaskHelper.java:80)
14:54:06.773  2559  2559 D report_tag: │    ReportTaskHelper.taskDone  (ReportTaskHelper.java:51)
14:54:06.773  2559  2559 D report_tag: │       ReportTaskHelper.run  (ReportTaskHelper.java:74)
14:54:06.773  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.773  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.773  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.773  2559  2559 D report_tag: │ 插入数据
14:54:06.773  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.786  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref++: 3 
14:54:06.787  2559  2559 V IJKMEDIA: mp[0xd7a8ae90] ref--: 2 
14:54:06.787  2559  2559 D ActionQueue: enqueue nullaction = ACTION_NORMAL duration = 0
14:54:06.787  2559  2559 I player.subscribe: onResult: com.kaolafm.kradio.component.ComponentResult@7bd3ea1
14:54:06.787  2559  2559 I player.subscribe: isSubscribed:onResult: errorInfo=ErrorInfo{code=0, des='null', exception=null}
14:54:06.787  2559  2559 I RadioPlayerFragment: 是否已经订阅= false
14:54:06.788  2559  2559 D EventBus: No subscribers registered for event class com.kaolafm.kradio.common.event.PlayerSubscribeResultEBData
14:54:06.788  2559  2559 D EventBus: No subscribers registered for event class org.greenrobot.eventbus.NoSubscriberEvent
14:54:06.794  2559  3011 D K-radio-Response: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.794  2559  3011 D K-radio-Response: │ Thread: RxCachedThreadScheduler-7
14:54:06.794  2559  3011 D K-radio-Response: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.794  2559  3011 D K-radio-Response: │ ConnectInterceptor.intercept  (ConnectInterceptor.java:45)
14:54:06.794  2559  3011 D K-radio-Response: │    RealInterceptorChain.proceed  (RealInterceptorChain.java:147)
14:54:06.794  2559  3011 D K-radio-Response: │       RequestInterceptor.intercept  (RequestInterceptor.java:85)
14:54:06.794  2559  3011 D K-radio-Response: │          FormatPrinter.printJsonResponse  (FormatPrinter.java:129)
14:54:06.794  2559  3011 D K-radio-Response: │             FormatPrinter.logInfo  (FormatPrinter.java:277)
14:54:06.794  2559  3011 D K-radio-Response: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.794  2559  3011 D K-radio-Response: │ ┌────── Response ───────────────────────────────────────────────────────────────────────
14:54:06.794  2559  3011 D K-radio-Response: │ │ URL: https://iovopen.radio.cn/v2/album/detail?ids=1100002161226&capabilities=NEW_DOMAIN_SUPPORTTED%2CPAY_CONTE
14:54:06.794  2559  3011 D K-radio-Response: │ │ NT_SUPPORTTED%2CMEDIA_URL_MUST_BE_HTTPS&os=android&lng=121.476035847&openid=eh71692023091210042759&packagename
14:54:06.794  2559  3011 D K-radio-Response: │ │ =com.edog.car.ford_kradio&sign=a3d018f64aa8125f3874fe44e756c6ff&channel=ford_kradio&deviceid=5f5607f9a1dba516c
14:54:06.794  2559  3011 D K-radio-Response: │ │ e20e67af230f7c4&version=2.10.4.0088&sdkversion=1.6.0&carType=PD2366&appid=eh7169&udid=5f5607f9a1dba516ce20e67a
14:54:06.794  2559  3011 D K-radio-Response: │ │ f230f7c4&lat=31.2378881115
14:54:06.794  2559  3011 D K-radio-Response: │ │ /v2/album/detail - is success : true - Received in: 64ms
14:54:06.794  2559  3011 D K-radio-Response: │ │ 
14:54:06.794  2559  3011 D K-radio-Response: │ │ Status Code: 200 / 
14:54:06.794  2559  3011 D K-radio-Response: │ │ 
14:54:06.794  2559  3011 D K-radio-Response: │ │ Headers:
14:54:06.794  2559  3011 D K-radio-Response: │ │ ┌ date: Fri, 22 Aug 2025 06:54:08 GMT
14:54:06.794  2559  3011 D K-radio-Response: │ │ ├ content-type: application/json;charset=UTF-8
14:54:06.794  2559  3011 D K-radio-Response: │ │ ├ alt-svc: h3=":443"; ma=3600,quic=":443"; v="46,43,39"; ma=3600
14:54:06.794  2559  3011 D K-radio-Response: │ │ ├ vary: Accept-Encoding
14:54:06.794  2559  3011 D K-radio-Response: │ │ ├ vary: Origin
14:54:06.794  2559  3011 D K-radio-Response: │ │ ├ vary: Access-Control-Request-Method
14:54:06.794  2559  3011 D K-radio-Response: │ │ ├ vary: Access-Control-Request-Headers
14:54:06.794  2559  3011 D K-radio-Response: │ │ ├ content-language: en
14:54:06.794  2559  3011 D K-radio-Response: │ │ └ content-encoding: gzip
14:54:06.794  2559  3011 D K-radio-Response: │ │ 
14:54:06.794  2559  3011 D K-radio-Response: │ │ Body:
14:54:06.794  2559  3011 D K-radio-Response: │ │ {
14:54:06.794  2559  3011 D K-radio-Response: │ │     "result": [
14:54:06.794  2559  3011 D K-radio-Response: │ │         {
14:54:06.794  2559  3011 D K-radio-Response: │ │             "id": 1100002161226,
14:54:06.794  2559  3011 D K-radio-Response: │ │             "name": "大江东去",
14:54:06.794  2559  3011 D K-radio-Response: │ │             "img": "https:\/\/iovimg.radio.cn\/mz\/images\/202105\/38d61b27-37e3-472b-84ff-b16686c3fcd3\/defau
14:54:06.794  2559  3011 D K-radio-Response: │ │ lt.jpg",
14:54:06.794  2559  3011 D K-radio-Response: │ │             "followedNum": 929,
14:54:06.794  2559  3011 D K-radio-Response: │ │             "countNum": 41,
14:54:06.794  2559  3011 D K-radio-Response: │ │             "isOnline": 1,
14:54:06.794  2559  3011 D K-radio-Response: │ │             "desc": "",
14:54:06.794  2559  3011 D K-radio-Response: │ │             "listenNum": 60,
14:54:06.794  2559  3011 D K-radio-Response: │ │             "sortType": 1,
14:54:06.794  2559  3011 D K-radio-Response: │ │             "hasCopyright": 1,
14:54:06.794  2559  3011 D K-radio-Response: │ │             "host": [
14:54:06.794  2559  3011 D K-radio-Response: │ │                 {
14:54:06.794  2559  3011 D K-radio-Response: │ │                     "name": "云听",
14:54:06.794  2559  3011 D K-radio-Response: │ │                     "des": "",
14:54:06.795  2559  3011 D K-radio-Response: │ │                     "img": ""
14:54:06.795  2559  3011 D K-radio-Response: │ │                 }
14:54:06.795  2559  3011 D K-radio-Response: │ │             ],
14:54:06.795  2559  3011 D K-radio-Response: │ │             "produce": "",
14:54:06.795  2559  3011 D K-radio-Response: │ │             "status": "已完结",
14:54:06.795  2559  3011 D K-radio-Response: │ │             "updateDay": "",
14:54:06.795  2559  3011 D K-radio-Response: │ │             "copyrightLabel": "",
14:54:06.795  2559  3011 D K-radio-Response: │ │             "keyWords": [
14:54:06.795  2559  3011 D K-radio-Response: │ │                 "小说",
14:54:06.795  2559  3011 D K-radio-Response: │ │                 "名著",
14:54:06.795  2559  3011 D K-radio-Response: │ │                 "人文",
14:54:06.795  2559  3011 D K-radio-Response: │ │                 "文学",
14:54:06.795  2559  3011 D K-radio-Response: │ │                 "听书专区"
14:54:06.795  2559  3011 D K-radio-Response: │ │             ],
14:54:06.795  2559  3011 D K-radio-Response: │ │             "commentNum": 0,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "lastCheckDate": 0,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "type": 0,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "isSubscribe": 0,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "sourceLogo": null,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "sourceName": "云听",
14:54:06.795  2559  3011 D K-radio-Response: │ │             "breakPointContinue": "1",
14:54:06.795  2559  3011 D K-radio-Response: │ │             "noSubscribe": 0,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "copyrightLogo": null,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "copyrightId": 1,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "catalogName": "有声书读物",
14:54:06.795  2559  3011 D K-radio-Response: │ │             "enableReverse": 0,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "aSentenceRecommend": "",
14:54:06.795  2559  3011 D K-radio-Response: │ │             "expireTime": null,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "foreverHearFlag": null,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "playLatestAudio": 0,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "cornerMark": null,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "cornerIcon": null,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "latestAudioReleaseTime": 1608540905000,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "vip": 0,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "fine": 1,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "songNeedPay": 1,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "buyType": 2,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "buyStatus": 0,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "payMethod": [
14:54:06.795  2559  3011 D K-radio-Response: │ │                 {
14:54:06.795  2559  3011 D K-radio-Response: │ │                     "buyNotice": null,
14:54:06.795  2559  3011 D K-radio-Response: │ │                     "payType": 1,
14:54:06.795  2559  3011 D K-radio-Response: │ │                     "originPrice": 0,
14:54:06.795  2559  3011 D K-radio-Response: │ │                     "currentPrice": null,
14:54:06.795  2559  3011 D K-radio-Response: │ │                     "payTypeName": "人民币",
14:54:06.795  2559  3011 D K-radio-Response: │ │                     "resourceId": 1100002161226
14:54:06.795  2559  3011 D K-radio-Response: │ │                 }
14:54:06.795  2559  3011 D K-radio-Response: │ │             ],
14:54:06.795  2559  3011 D K-radio-Response: │ │             "source": 34,
14:54:06.795  2559  3011 D K-radio-Response: │ │             "tagInfo": null
14:54:06.795  2559  3011 D K-radio-Response: │ │         }
14:54:06.795  2559  3011 D K-radio-Response: │ │     ],
14:54:06.795  2559  3011 D K-radio-Response: │ │     "requestId": "eh71691755845648607718",
14:54:06.795  2559  3011 D K-radio-Response: │ │     "serverTime": "1755845648613"
14:54:06.795  2559  3011 D K-radio-Response: │ │ }
14:54:06.795  2559  3011 D K-radio-Response: │ └───────────────────────────────────────────────────────────────────────────────────────
14:54:06.795  2559  3011 D K-radio-Response: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.809  2559  2559 I player.subscribe: onResult: com.kaolafm.kradio.component.ComponentResult@62ee8c6
14:54:06.810  2559  2559 I player.subscribe: isSubscribed:onResult: errorInfo=ErrorInfo{code=0, des='null', exception=null}
14:54:06.810  2559  2559 D RadioPlayTag: ErrorInfo{code=0, des='null', exception=null}
14:54:06.813  2559  2559 I player.subscribe: onResult: com.kaolafm.kradio.component.ComponentResult@c09a087
14:54:06.813  2559  2559 I player.subscribe: isSubscribed:onResult: errorInfo=ErrorInfo{code=0, des='null', exception=null}
14:54:06.813  2559  2559 D RadioPlayTag: ErrorInfo{code=0, des='null', exception=null}
14:54:06.814  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.814  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.814  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.814  2559  2559 D report_tag: │ ConsumerSingleObserver.onSuccess  (ConsumerSingleObserver.java:63)
14:54:06.814  2559  2559 D report_tag: │    -$$Lambda$ReportTaskHelper$0_306wrLG-ZqLBvoT6kuPoy_UZk.accept  (null:4)
14:54:06.814  2559  2559 D report_tag: │       ReportTaskHelper.lambda$run$0  (ReportTaskHelper.java:76)
14:54:06.814  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.814  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.814  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.814  2559  2559 D report_tag: │ 数据库大小 = 4
14:54:06.814  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.814  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.814  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.814  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.814  2559  2559 D report_tag: │ ReportTaskHelper.taskDone  (ReportTaskHelper.java:51)
14:54:06.814  2559  2559 D report_tag: │    ReportTaskHelper.run  (ReportTaskHelper.java:63)
14:54:06.814  2559  2559 D report_tag: │       ReportTaskHelper.getTask  (ReportTaskHelper.java:55)
14:54:06.814  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.814  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.814  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.814  2559  2559 D report_tag: │ 获取队列大小 = 1
14:54:06.814  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.814  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.814  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.814  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.814  2559  2559 D report_tag: │ ReportTaskHelper.lambda$run$0  (ReportTaskHelper.java:80)
14:54:06.814  2559  2559 D report_tag: │    ReportTaskHelper.taskDone  (ReportTaskHelper.java:51)
14:54:06.814  2559  2559 D report_tag: │       ReportTaskHelper.run  (ReportTaskHelper.java:74)
14:54:06.814  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.814  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.814  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.814  2559  2559 D report_tag: │ 插入数据
14:54:06.814  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.815  2559  2559 D ActionQueue: enqueueAction mQueue.size() = 0
14:54:06.818  2559  2559 D ActionQueue: executeNextAction postDelayed mQueue.size() = 1
14:54:06.823  2559  2559 I RadioPlayerPresenter: initPayInfo result albumId: 1100002161226 , player album id 1100002161226
14:54:06.823  2559  2559 I RadioPlayerFragment: showPayInfo ,album buy status 0 , album vip 0 , album fine 1 , user vip 0
14:54:06.823  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.823  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.823  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.823  2559  2559 D report_tag: │ ConsumerSingleObserver.onSuccess  (ConsumerSingleObserver.java:63)
14:54:06.823  2559  2559 D report_tag: │    -$$Lambda$ReportTaskHelper$0_306wrLG-ZqLBvoT6kuPoy_UZk.accept  (null:4)
14:54:06.823  2559  2559 D report_tag: │       ReportTaskHelper.lambda$run$0  (ReportTaskHelper.java:76)
14:54:06.823  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.823  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.823  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.823  2559  2559 D report_tag: │ 数据库大小 = 5
14:54:06.823  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.823  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.823  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.823  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.823  2559  2559 D report_tag: │ ReportTaskHelper.taskDone  (ReportTaskHelper.java:51)
14:54:06.823  2559  2559 D report_tag: │    ReportTaskHelper.run  (ReportTaskHelper.java:63)
14:54:06.823  2559  2559 D report_tag: │       ReportTaskHelper.getTask  (ReportTaskHelper.java:55)
14:54:06.823  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.823  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.823  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.823  2559  2559 D report_tag: │ 获取队列大小 = 0
14:54:06.823  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.823  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.823  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:06.823  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.824  2559  2559 D report_tag: │ ReportTaskHelper.lambda$run$0  (ReportTaskHelper.java:80)
14:54:06.824  2559  2559 D report_tag: │    ReportTaskHelper.taskDone  (ReportTaskHelper.java:51)
14:54:06.824  2559  2559 D report_tag: │       ReportTaskHelper.run  (ReportTaskHelper.java:65)
14:54:06.824  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:06.824  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:06.824  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:06.824  2559  2559 D report_tag: │ 没有任何任务了. 停止运行
14:54:06.824  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:06.824  2559  2559 D ActionQueue: executeNextAction before poll mQueue.size() = 1
14:54:06.824  2559  2559 D ActionQueue: executeNextAction after poll mQueue.size() = 0
14:54:06.824  2559  2559 D ActionQueue: handleAction mQueue is empty
14:54:07.017  2559  3175 I SocketClient: EVENT_PACKET----->type = ping---->data = null
14:54:08.609  2559  2616 I com.edog.car: Note: end time exceeds INT32_MAX: 9223372036854300
14:54:10.008  2559  2559 D BridgeManager: retry bindService
14:54:10.008  2559  2559 D BridgeManager: bind result = false
14:54:10.529  2559  2890 V IJKMEDIA: read thread: run in read loop - 477404
14:54:10.775  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:10.775  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:10.775  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:10.775  2559  2559 D report_tag: │ -$$Lambda$PlayReportManager$idK6DWGClVtUmKg701x3AbZZPDM.accept  (null:4)
14:54:10.775  2559  2559 D report_tag: │    PlayReportManager.lambda$savePlayPosition$0  (PlayReportManager.java:231)
14:54:10.775  2559  2559 D report_tag: │       PlayReportManager.saveDB  (PlayReportManager.java:235)
14:54:10.775  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:10.775  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:10.775  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:10.775  2559  2559 D report_tag: │ save play position db !
14:54:10.775  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:10.775  2559  2559 D report_tag: ┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────
14:54:10.775  2559  2559 D report_tag: │ Thread: CallServiceRunnable-com.kaolafm.kradio.user.UserComponent-com.edog.car:21
14:54:10.775  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:10.775  2559  2559 D report_tag: │ -$$Lambda$PlayReportManager$idK6DWGClVtUmKg701x3AbZZPDM.accept  (null:4)
14:54:10.775  2559  2559 D report_tag: │    PlayReportManager.lambda$savePlayPosition$0  (PlayReportManager.java:231)
14:54:10.775  2559  2559 D report_tag: │       PlayReportManager.saveDB  (PlayReportManager.java:241)
14:54:10.775  2559  2559 D report_tag: │          Logging.d  (Logging.java:106)
14:54:10.775  2559  2559 D report_tag: │             Logging.log  (Logging.java:49)
14:54:10.775  2559  2559 D report_tag: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
14:54:10.775  2559  2559 D report_tag: │ save play position db ! test return
14:54:10.775  2559  2559 D report_tag: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────
