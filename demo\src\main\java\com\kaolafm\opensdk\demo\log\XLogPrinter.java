package com.kaolafm.opensdk.demo.log;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.elvishew.xlog.LogConfiguration;
import com.elvishew.xlog.LogConfiguration.Builder;
import com.elvishew.xlog.LogLevel;
import com.elvishew.xlog.Logger;
import com.elvishew.xlog.XLog;
import com.kaolafm.opensdk.log.Printer;

/**
 * <AUTHOR>
 * @date 2018/12/20
 */
public class XLogPrinter implements Printer {

    private Logger.Builder mTag;

    public XLogPrinter() {
        this(null);
    }

    public XLogPrinter(String tag) {
        Builder builder = new LogConfiguration.Builder();
        if (TextUtils.isEmpty(tag)) {
            builder.logLevel(LogLevel.ALL)
                    .tag("Demo")
                    .t().st(5).b();
            XLog.init(builder.build());
        }else {
            mTag = new Logger.Builder().tag(tag);
        }
    }

    @Override
    public Printer tag(@Nullable String tag) {

        return this;
    }

    @Override
    public void d(@NonNull String message, @Nullable Object... args) {
        XLog.d(message, args);
    }

    @Override
    public void d(@Nullable Object object) {
        XLog.d(object);
    }
    @Override
    public void e(@NonNull String message, @Nullable Object... args) {
        XLog.e(message, args);
    }

    @Override
    public void e(@Nullable Throwable throwable, @NonNull String message, @Nullable Object... args) {
        XLog.e(String.format(message, args), throwable);
    }

    @Override
    public void w(@NonNull String message, @Nullable Object... args) {
        XLog.w(message, args);
    }

    @Override
    public void i(@NonNull String message, @Nullable Object... args) {
        XLog.i(message, args);
    }

    @Override
    public void v(@NonNull String message, @Nullable Object... args) {
        XLog.v(message, args);
    }

    @Override
    public void wtf(@NonNull String message, @Nullable Object... args) {
    }

    @Override
    public void json(@Nullable String json) {
        XLog.json(json);
    }

    @Override
    public void xml(@Nullable String xml) {
        XLog.xml(xml);
    }

    @Override
    public void println(int logLevel, @Nullable String tag, @Nullable String message, @Nullable Throwable throwable) {
        XLog.tag(tag).nb().nt().nst().log(logLevel, message, throwable);
    }
}
