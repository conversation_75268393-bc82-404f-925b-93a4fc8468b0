package com.kaolafm.opensdk.player.logic.model;

import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * <AUTHOR> on 2019/3/28.
 */

public class PlayerBuilder {
    private String mId;

    private int mType = PlayerConstants.RESOURCES_TYPE_INVALID;

    public PlayerBuilder setId(String id) {
        this.mId = id;
        return this;
    }

    public String getId() {
        return mId;
    }

    public int getType() {
        return mType;
    }

    public PlayerBuilder setType(int type) {
        this.mType = type;
        return this;
    }
    /** 是否可以订阅 只有为1的时候不能订阅 */
    private int noSubscribe;

    public int getNoSubscribe() {
        return noSubscribe;
    }

    public void setNoSubscribe(int noSubscribe) {
        this.noSubscribe = noSubscribe;
    }
}
