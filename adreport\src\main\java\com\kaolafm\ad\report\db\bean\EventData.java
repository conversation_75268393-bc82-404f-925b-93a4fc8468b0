package com.kaolafm.ad.report.db.bean;

import com.kaolafm.ad.report.Constant;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Keep;

@Entity
public class EventData {
    @Id(autoincrement=true)
    private Long id;

    /**
     * 创意id（广告唯一id）
     */
    private long creativeId;

    /**
     * EventType （pv、play、click、skip等事件类型）
     */
    private int type;

    /**
     * 0：未上报
     * 1：上报中
     * 2: 上报完成
     */
    private int status;

    /**
     * 上报事件json
     */
    private String reportData;

    

    public EventData() {
    }

    @Keep
    public EventData(Long id, long creativeId, int type, int status,
            String reportData) {
        this.id = id;
        this.creativeId = creativeId;
        this.type = type;
        this.status = status;
        this.reportData = reportData;
    }















    



    public Long getId() {
        return this.id;
    }



    public void setId(Long id) {
        this.id = id;
    }



    public long getCreativeId() {
        return this.creativeId;
    }



    public void setCreativeId(long creativeId) {
        this.creativeId = creativeId;
    }



    public int getType() {
        return this.type;
    }



    public void setType(int type) {
        this.type = type;
    }



    public int getStatus() {
        return this.status;
    }



    public void setStatus(int status) {
        this.status = status;
    }



    public String getReportData() {
        return this.reportData;
    }



    public void setReportData(String reportData) {
        this.reportData = reportData;
    }


    @Override
    public String toString() {
        return "EventData{" +
                "id=" + id +
                ", creativeId=" + creativeId +
                ", type=" + type +
                ", status=" + status +
                ", reportData='" + reportData + '\'' +
                '}';
    }
}
