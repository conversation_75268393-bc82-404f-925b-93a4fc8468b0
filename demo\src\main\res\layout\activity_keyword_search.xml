<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <Spinner
        android:id="@+id/spinner_search_resource_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toTopOf="parent"
        android:entries="@array/search_type_list"
        />
    <EditText
        android:id="@+id/et_search_keyword"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:hint="请输入关键词"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toRightOf="@id/spinner_search_resource_type"
        app:layout_constraintRight_toRightOf="parent"
        android:maxLines="1"
        />
    <TextView
        android:id="@+id/tv_keyword_search_commit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="搜索"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="5dp"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginRight="10dp"
        android:textColor="@android:color/white"
        android:background="@color/colorAccent"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        />

    <com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout
        android:id="@+id/trf_search"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/et_search_keyword"
        app:layout_constraintBottom_toBottomOf="parent">
        <android.support.v7.widget.RecyclerView
            android:id="@+id/rv_search_suggested_word"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout>

</android.support.constraint.ConstraintLayout>