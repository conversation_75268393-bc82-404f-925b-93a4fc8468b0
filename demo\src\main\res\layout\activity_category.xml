<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:layout_margin="10dp">

    <Switch
        android:id="@+id/switch_category_stratify"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="分层获取数据" />

    <EditText
        android:id="@+id/et_column_zone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:hint="请输入分区"
        android:textSize="14sp"
        app:layout_constraintTop_toBottomOf="@id/switch_category_stratify"
        android:text="mainPage"/>

    <EditText
        android:id="@+id/et_column_extra"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:hint="请输入额外信息，\n需要和服务端约定，格式示例{a=1,b=2}"
        android:textSize="10sp"
        app:layout_constraintLeft_toRightOf="@id/et_column_zone"
        app:layout_constraintTop_toBottomOf="@id/switch_category_stratify" />

    <TextView
        android:id="@+id/tv_column_commit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/color_black_50_transparent"
        android:paddingBottom="5dp"
        android:paddingEnd="10dp"
        android:paddingStart="10dp"
        android:paddingTop="5dp"
        android:text="确定"
        android:textColor="@color/colorAccent"
        app:layout_constraintLeft_toRightOf="@id/et_column_extra"
        app:layout_constraintTop_toBottomOf="@id/switch_category_stratify" />

    <com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout
        android:id="@+id/trf_category_refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/tv_column_commit"
        app:layout_constraintBottom_toBottomOf="parent"
        android:paddingTop="30dp">

        <android.support.v7.widget.RecyclerView
            android:id="@+id/rv_category_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout>


</android.support.constraint.ConstraintLayout>