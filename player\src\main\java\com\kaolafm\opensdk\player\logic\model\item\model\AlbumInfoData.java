package com.kaolafm.opensdk.player.logic.model.item.model;

public class AlbumInfoData {
    /**
     * 专辑订阅数
     */
    private long followedNum;
    /**
     * 总期数
     */
    private long countNum;

    private String breakPointContinue;

    /**
     * 收听数
     */
    private long listenNum;
    public long getFollowedNum() {
        return followedNum;
    }

    public void setFollowedNum(long followedNum) {
        this.followedNum = followedNum;
    }

    public long getCountNum() {
        return countNum;
    }

    public void setCountNum(long countNum) {
        this.countNum = countNum;
    }

    public long getListenNum() {
        return listenNum;
    }

    public void setListenNum(long listenNum) {
        this.listenNum = listenNum;
    }

    public String getBreakPointContinue() {
        return breakPointContinue;
    }

    public void setBreakPointContinue(String breakPointContinue) {
        this.breakPointContinue = breakPointContinue;
    }
}
