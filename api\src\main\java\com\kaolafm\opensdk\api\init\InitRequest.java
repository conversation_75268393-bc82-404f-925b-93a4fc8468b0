package com.kaolafm.opensdk.api.init;

import android.text.TextUtils;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.component.SessionComponent;
import com.kaolafm.opensdk.di.qualifier.KaolaInitParam;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.model.KaolaActivateData;

import java.util.Map;

import javax.inject.Inject;
import javax.inject.Provider;

import io.reactivex.Single;
import io.reactivex.SingleEmitter;

/**
 * 初始化激活相关网络请求
 *
 * <AUTHOR>
 * @date 2018/7/25
 */
@AppScope
public class InitRequest extends BaseRequest {

    private static final int ERROR_CODE_REPEAT_ACTIVATION = 50500;

    /**
     * 公共参数在{@link com.kaolafm.opensdk.di.module.CommonParamModule}中提供, 每次get()都会重新获取参数
     */
    @Inject
    @KaolaInitParam
    Provider<Map<String, String>> mKaolaInitParams;


    private InitService mInitService;

    @Inject
    public InitRequest() {
        SessionComponent subcomponent = ComponentKit.getInstance().getSubcomponent();
        subcomponent.inject(this);
        mInitService = obtainRetrofitService(InitService.class);
    }

    /**
     * 初始化考拉,获取openId
     *
     * @param callback 回调,返回openId
     */
    private void initKaola(HttpCallback<String> callback) {
        doHttpDeal(mInitService.initKaola(), baseResult -> {
            if (baseResult != null) {
                KaolaActivateData activateData = baseResult.getResult();
                if (activateData != null) {
                    setCarInfoToReport(activateData);
                    return activateData.getOpenid();
                }
            }
            return "";
        }, callback);
    }


    /**
     * 设备激活考拉
     *
     * @param callback 回调,返回openId
     */
    private void activeKaola(HttpCallback<String> callback) {

        doHttpDeal(mInitService.activeKaola(getInitParams()), baseResult -> {
            if (baseResult != null) {
                KaolaActivateData activateData = baseResult.getResult();
                if (activateData != null) {
                    setCarInfoToReport(activateData);
                    return activateData.getOpenid();
                }
            }
            return "";
        }, callback);
    }

    private Map<String, String> getInitParams() {
        return mKaolaInitParams.get();
    }

    /**
     * 激活或者初始化考拉接口，先调用{@link #activeKaola(HttpCallback)}接口，
     * 当已经激活或者激活成功没有获取openId就自动调用{@link #initKaola(HttpCallback)}接口获取openId.
     */
    private Single<String> activateOrInitKaola() {
        return Single.create(emitter -> activeKaola(new HttpCallback<String>() {
            @Override
            public void onSuccess(String s) {
                if (!TextUtils.isEmpty(s)) {
                    InitRequest.this.onSuccess(emitter, s);
                } else {//如果获取openId为空就初始化一下。
                    initKaola(emitter);
                }
            }

            @Override
            public void onError(ApiException exception) {
                //如果重复激活，就自动调用init方法获取autoId
                if (exception != null && exception.getCode() == ERROR_CODE_REPEAT_ACTIVATION) {
                    initKaola(emitter);
                } else {
                    InitRequest.this.onError(emitter, exception);
                }
            }
        }));
    }

    public void activateOrInitKaola(HttpCallback<String> callback) {
        doHttpDeal(activateOrInitKaola(), callback);
    }

    private void initKaola(SingleEmitter<String> emitter) {
        initKaola(new HttpCallback<String>() {
            @Override
            public void onSuccess(String s) {
                if (!TextUtils.isEmpty(s)) {
                    InitRequest.this.onSuccess(emitter, s);
                } else {
                    InitRequest.this.onError(emitter, new ApiException("激活失败"));
                }
            }

            @Override
            public void onError(ApiException exception) {
                InitRequest.this.onError(emitter, exception);
            }
        });
    }

    private <T> void onSuccess(SingleEmitter<T> emitter, T t) {
        if (emitter != null && !emitter.isDisposed()) {
            emitter.onSuccess(t);
        }
    }

    private <T> void onError(SingleEmitter<T> emitter, ApiException e) {
        if (emitter != null && !emitter.isDisposed()) {
            emitter.onError(e);
        }
    }

    private void setCarInfoToReport(KaolaActivateData activateData) {
        if (activateData == null) {
            return;
        }
        ReportHelper.getInstance().setCarParameter(activateData);
    }

    String activate() {
        BaseResult<KaolaActivateData> baseResult = doHttpDealSync(mInitService.activeKaolaSync(getInitParams()));
        if (baseResult == null) {
            return "";
        }
        int code = baseResult.getCode();
        KaolaActivateData activateData = null;
        if (code != 0 && code != 10000) {
            if (code == ERROR_CODE_REPEAT_ACTIVATION) {
                BaseResult<KaolaActivateData> initResult = doHttpDealSync(mInitService.initKaolaSync());
                int initCode = initResult.getCode();
                if (initCode == 0 || initCode == 10000) {
                    activateData = initResult.getResult();
                }
            }
        }else {
            activateData = baseResult.getResult();
        }
        if (activateData != null) {
            setCarInfoToReport(activateData);
            return activateData.getOpenid();
        }
        return "";
    }

    public void getBrand(HttpCallback<String> callback) {
        doHttpDeal(mInitService.getBrand(), BaseResult::getResult, callback);
    }

}
