package com.kaolafm.opensdk.api.init;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.api.HostConstant;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpBeforeHandler;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.core.VerifyActivation;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.Logging;

import java.net.URL;
import java.util.concurrent.Semaphore;

import javax.inject.Inject;

import dagger.Lazy;
import okhttp3.Interceptor;
import okhttp3.Request;

/**
 * 验证激活的接口实现类。
 * 使用信号量来保证激活只执行一次。
 * 信号量设置1，只有一个信号，如果有多线程的多次激活请求会进行抢信号，如果没有抢到就会等待。
 * 抢到后需要判断是否已经激活了，未激活就走激活流程，无论成功失败都释放锁。
 * 激活过了就释放锁走下面的流程。
 * <AUTHOR> Yan
 * @date 2019-11-12
 */
@AppScope
public class VerifyActivationImpl implements VerifyActivation , HttpBeforeHandler {

    private static final String TAG = "VerifyActivationImpl";

    @Inject
    @AppScope
    Lazy<AccessTokenManager> mTokenManager;

    @Inject
    @AppScope
    Lazy<InitRequest> mInitRequestLazy;

    /**
     * 是否正在激活， true正在激活
     */
    private volatile boolean activating = false;

    /**
     * 是否已经激活。true表示已经激活
     */
    private volatile boolean activated;

    private HttpCallback<Boolean> mCallback;

    /**
     * 信号量，获取不到许可就会一直等待。
     */
    private Semaphore mSemaphore;

    @Inject
    public VerifyActivationImpl() {
        mSemaphore = new Semaphore(1, true);
    }

    @Override
    public void autoActivate(Request request) {
        Logging.d("VerifyActivation", "autoActivate: activating="+activating +">>>"+Thread.currentThread()+ ">>>"+ request.url().url());
        try {
            mSemaphore.acquire();
            //不是正在激活并且是未激活才同步执行激活
            if (!activating && !activated) {
                activating = true;
                String openId = mInitRequestLazy.get().activate();
                saveOpenId(openId);
                activating = false;
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            activating = false;
            mSemaphore.release();
        }
    }

    private void saveOpenId(String openId) {
        if (!TextUtils.isEmpty(openId)) {
            KaolaAccessToken kaolaAccessToken = mTokenManager.get().getKaolaAccessToken();
            kaolaAccessToken.setOpenId(openId);
            mTokenManager.get().setCurrentAccessToken(kaolaAccessToken);
            activated = true;
            new Handler(Looper.getMainLooper()).post(() -> {
                if (mCallback != null) {
                    mCallback.onSuccess(true);
                    mCallback = null;
                }
            });
        }else {
            new Handler(Looper.getMainLooper()).post(() -> {
                if (mCallback != null) {
                    mCallback.onError(new ApiException("同步激活失败"));
                    mCallback = null;
                }
            });
        }
    }

    /**
     * 判断是否重新激活
     * @return
     * @param url
     */
    private boolean shouldReactivate(URL url) {
        if (activated || !HostConstant.OPEN_KAOLA_HOST.equals(url.getHost())) {
            return false;
        }
        String urlPath = url.getPath();
        //不是初始化接口
        return!KaolaApiConstant.REQUEST_KAOLA_INIT.equals(urlPath)
                //不是激活接口
                && !KaolaApiConstant.REQUEST_KAOLA_ACTIVATE.equals(urlPath);
    }

    @Override
    public void activate(HttpCallback<Boolean> callback) {
        mCallback = callback;
        if (activating) {
            return;
        }
        activating = true;
        try {
            mSemaphore.acquire();
            if (activated) {
                mSemaphore.release();
                return;
            }
            mInitRequestLazy.get().activateOrInitKaola(new HttpCallback<String>() {
                @Override
                public void onSuccess(String s) {
                    saveOpenId(s);
                    activating = false;
                    mSemaphore.release();
                }

                @Override
                public void onError(ApiException exception) {
                    callback.onError(exception);
                    activating = false;
                    mSemaphore.release();
                }
            });
        } catch (InterruptedException e) {
            e.printStackTrace();
            activating = false;
            mSemaphore.release();
        }
    }

    @Override
    public boolean isActivate() {
        try {
            KaolaAccessToken kaolaAccessToken = mTokenManager.get().getKaolaAccessToken();
            activated = !TextUtils.isEmpty(kaolaAccessToken.getOpenId());
            Log.i(TAG, "isActivate() normal execute");
            return activated;
        } catch (Exception | Error e) {
            Log.i(TAG, "isActivate() error execute --- e = " + e);
            return false;
        }
    }

    @Override
    public Request onHttpRequestBefore(Interceptor.Chain chain, Request request) {
        if (shouldReactivate(request.url().url())) {
            autoActivate(request);
        }
        return request;
    }
    //由于是接口的default方法，加override会编译不通过。
    public int priority() {
        return 10;
    }
}
