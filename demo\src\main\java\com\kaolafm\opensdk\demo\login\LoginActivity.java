package com.kaolafm.opensdk.demo.login;

import android.content.Intent;
import android.graphics.Paint;
import android.os.Bundle;
import android.support.constraint.Group;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.OnClick;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;

/**
 * Kradio登录注册页面。不再使用。
 * <AUTHOR>
 * @date 2018/8/2
 */

public class LoginActivity extends BaseActivity {

    @BindView(R.id.btn_kaola_logout)
    Button mBtnKaolaLogout;

    @BindView(R.id.btn_login_check)
    Button mBtnLoginCheck;

    @BindView(R.id.btn_login_get_code)
    Button mBtnLoginGetCode;

    @BindView(R.id.btn_login_register_commit)
    Button mBtnLoginRegisterCommit;

    @BindView(R.id.et_login_age)
    EditText mEtLoginAge;

    @BindView(R.id.et_login_code)
    EditText mEtLoginCode;

    @BindView(R.id.et_login_phone_num)
    EditText mEtLoginPhoneNum;

    @BindView(R.id.group)
    Group mGroup;

    @BindView(R.id.rb_login_man)
    RadioButton mRbLoginMan;

    @BindView(R.id.rb_login_woman)
    RadioButton mRbLoginWoman;

    @BindView(R.id.rg_login_sex)
    RadioGroup mRgLoginSex;

    @BindView(R.id.tv_login_or_register)
    TextView mTvLoginOrRegister;

    @BindView(R.id.tv_login_user_info)
    TextView mTvLoginUserInfo;

    private boolean isLogin = true;

    private int mType;


    @Override
    public int getLayoutId() {
        return R.layout.activity_login;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        mTvLoginOrRegister.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG);
        showRightLayout();
    }

    @Override
    public void initArgs() {
        super.initArgs();
        Intent intent = getIntent();
        if (intent != null) {
            mType = intent.getIntExtra("type", 0);
            isLogin = mType == 2;
        }
    }

    @Override
    public void initData() {

    }

    @OnClick({R.id.btn_login_check, R.id.btn_login_get_code, R.id.tv_login_or_register,
            R.id.btn_login_register_commit})
    public void onViewClicked(View view) {
        String phone = mEtLoginPhoneNum.getText().toString().trim();
        switch (view.getId()) {
            case R.id.btn_login_check:
                if (!TextUtils.isEmpty(phone)) {

                }
                break;
            case R.id.btn_login_get_code:
                if (!TextUtils.isEmpty(phone)) {

                }
                break;
            case R.id.btn_login_register_commit:
                // TODO: 2018/8/15 注册/登录时应该先检查手机是否已经注册过了 如果已经注册过了再注册会报错
                String code = mEtLoginCode.getText().toString().trim();
                if (!TextUtils.isEmpty(phone) && !TextUtils.isEmpty(code)) {
                    if (isLogin) {

                    } else {

                        String age = mEtLoginAge.getText().toString().trim();
                        int sex = 0;
                        int checkedRadioButtonId = mRgLoginSex.getCheckedRadioButtonId();
                        if (checkedRadioButtonId == R.id.rb_login_man) {
                            sex = 0;
                        } else if (checkedRadioButtonId == R.id.rb_login_woman) {
                            sex = 1;
                        }
                    }
                }
                break;
            case R.id.tv_login_or_register:
                isLogin = !isLogin;
                showRightLayout();
                break;
            default:
                break;

        }
    }


    @OnClick(R.id.btn_kaola_logout)
    public void onViewClicked() {

    }

    private void showUserInfo() {
        StringBuilder sb = new StringBuilder();
        KaolaAccessToken accessToken = AccessTokenManager.getInstance().getKaolaAccessToken();
        sb.append("UserId:").append(accessToken.getUserId()).append("\r\n")
                .append("Token:").append(accessToken.getAccessToken());
        mTvLoginUserInfo.setText(sb);
    }

    private void showRightLayout() {
        if (!isLogin) {
            mGroup.setVisibility(View.VISIBLE);
            mTvLoginOrRegister.setText("去登陆");
            mBtnLoginRegisterCommit.setText("注册");
            setTitle("注册");
        } else {
            mGroup.setVisibility(View.GONE);
            mTvLoginOrRegister.setText("去注册");
            mBtnLoginRegisterCommit.setText("登录");
            setTitle("登录");
        }
        boolean login = AccessTokenManager.getInstance().getKaolaAccessToken().isLogin();
        if (login) {
            mBtnKaolaLogout.setVisibility(View.VISIBLE);
            mBtnKaolaLogout.setText("已登录，退出登录");
        }else {
            mBtnKaolaLogout.setVisibility(View.GONE);
        }
    }

}
