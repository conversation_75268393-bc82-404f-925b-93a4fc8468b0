package com.kaolafm.opensdk.demo.brandinfo;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.method.LinkMovementMethod;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.api.brand.BrandRequest;
import com.kaolafm.opensdk.api.brand.model.BrandDetails;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import butterknife.BindView;

public class BrandInfoActivity extends BaseActivity {
    @BindView(R.id.iv_brand_logo)
    ImageView mIvBrandLogo;

    @BindView(R.id.tv_brand_agreement)
    TextView mTvBrandAgreement;

    @BindView(R.id.tv_brand_name)
    TextView mTvBrandName;

    @Override
    public int getLayoutId() {
        return R.layout.activity_brand_info;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("品牌信息");
        mTvBrandAgreement.setMovementMethod(LinkMovementMethod.getInstance());

    }

    @Override
    public void initData() {
        new BrandRequest().getBrandInfo(new HttpCallback<BrandDetails>() {
            @Override
            public void onSuccess(BrandDetails brandDetails) {
                if (brandDetails != null) {
                    mTvBrandName.setText(brandDetails.getBrand());
                    Glide.with(BrandInfoActivity.this).load(brandDetails.getLogo()).into(mIvBrandLogo);
                    mTvBrandAgreement.setOnClickListener(v -> {
                        Intent intent=new  Intent();
                        intent.setAction(Intent.ACTION_VIEW);
                        intent.setData(Uri.parse(brandDetails.getUserAgreement()));
                        startActivity(intent);
                    });
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取品牌信息失败，", exception);
            }
        });
    }

}
