<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView
        android:id="@+id/iv_item_scene_icon"
        android:layout_width="80dp"
        android:layout_height="80dp"
        />
    <TextView
        android:id="@+id/tv_item_scene_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="中国原创音乐"
        app:layout_constraintLeft_toRightOf="@id/iv_item_scene_icon"
        android:paddingTop="5dp"
        android:maxLines="2"
        android:textColor="@color/colorBlack"
        android:textSize="16sp"
        />
    <TextView
        android:id="@+id/tv_item_scene_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@id/iv_item_scene_icon"
        app:layout_constraintTop_toBottomOf="@id/tv_item_scene_message"
        android:paddingTop="5dp"
        android:textSize="12sp"
        android:textColor="@color/color_black_50_transparent"
        android:text="中国原创"
        />


</android.support.constraint.ConstraintLayout>