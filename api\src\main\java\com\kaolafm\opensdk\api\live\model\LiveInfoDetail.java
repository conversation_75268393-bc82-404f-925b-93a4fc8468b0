package com.kaolafm.opensdk.api.live.model;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.gson.annotations.SerializedName;

/**
 * 直播节目详信息
 * <AUTHOR>
 */
public class LiveInfoDetail implements Parcelable {


    /** 直播已经结束*/
    public static final int STATUS_FINISHED = 0;

    /** 正在直播*/
    public static final int STATUS_LIVING = 1;

    /** 即将开始*/
    public static final int STATUS_COMING = 2;

    /** 今天开播, 今天的某个时间*/
    public static final int STATUS_START_TODAY = 3;

    /** 明天开播*/
    public static final int STATUS_START_TOMORROW = 4;

    /** 后天开播*/
    public static final int STATUS_START_AFTER_TOMORROW = 5;

    /** 未开播*/
    public static final int STATUS_NOT_START = 6;

    /** 已延期*/
    public static final int STATUS_DELAYED = 7;

    /** 直播转录播*/
    public static final int STATUS_LIVE_TO_RECORDING = 8;

    /**
     * liveId : 1497957386
     * programId : 1581897498
     * endtime : 2020-01-30 00:00:00
     * showStartTime : 直播中
     * guests :
     * albumId : 0
     * shareUrl : http://m.kaolafm.com/share/liveplay/index.html
     * onLineNum : 0
     * isCanSubscribe : 0
     * isAlreadySubscribe : 0
     * subscribeNum : 0
     * bgColor :
     * programLikedNum : 0
     * programSharedNum : 0
     * programFollowedNum : 24
     * uid : 1014026
     * avatar : http://img.kaolafm.net/mz/images/201609/732b0a22-1ed6-4c26-82c3-1ac76c4f2b02/default.jpg
     * isVanchor : 1
     * gender : 2
     * backLiveUrl :
     * canPlayBack : 0
     * pushHost : pub.c.l.kaolafm.net
     * accessKey : ugc
     * lockType : 0
     * isAlreadyFollowed : 0
     * roomId : 56891516
     * userType : null
     * rtmpUrl : rtmp://play.c.l.kaolafm.net/ugc/1497957386_1581897498
     */

    /** 直播间名称*/
    private String liveName;

    /** 直播间id*/
    private long liveId;

    /** 直播间描述*/
    private String liveDesc;

    /** 当前直播节目id*/
    private long programId;

    /** 当前直播节目名称*/
    private String programName;

    /** 当前直播节目描述*/
    private String programDesc;

    /** 开始时间。格式化的时间，2018-12-12 00:00:00*/
    @SerializedName("begintime")
    private String beginTime;

    /** 结束时间。格式化的时间，2020-01-30 00:00:00*/
    @SerializedName("endTime")
    private String endTime;

    /** 节目状态。@see {@link #STATUS_LIVING}等*/
    private int status;

    /** 节目期号*/
    private int period;

    /** 主播姓名*/
    private String comperes;

    /** 直播地址*/
    private String liveUrl;

    /** 节目图片url*/
    private String programPic;

    /** 直播间图片url*/
    private String livePic;

    /** 直播时长*/
    private String timeLength;

    /** 开始时间，时间戳，单位毫秒*/
    private long startTime;

    /** 结束时间，时间戳，单位毫秒*/
    @SerializedName("finshTime")
    private long finishTime;

    /** */
    @SerializedName("serveTime")
    private long serverTime;

    /** 直播时长*/
    private int duration;

    private String roomId;

    /** 时间描述，如直播中*/
    private String showStartTime;
    /**
     * 主播id
     */
    @SerializedName("uid")
    private long comperesId;

    public LiveInfoDetail() {
    }

    public String getLiveName() {
        return liveName;
    }

    public void setLiveName(String liveName) {
        this.liveName = liveName;
    }

    public long getLiveId() {
        return liveId;
    }

    public void setLiveId(long liveId) {
        this.liveId = liveId;
    }

    public String getLiveDesc() {
        return liveDesc;
    }

    public void setLiveDesc(String liveDesc) {
        this.liveDesc = liveDesc;
    }

    public long getProgramId() {
        return programId;
    }

    public void setProgramId(long programId) {
        this.programId = programId;
    }

    public String getProgramName() {
        return programName;
    }

    public void setProgramName(String programName) {
        this.programName = programName;
    }

    public String getProgramDesc() {
        return programDesc;
    }

    public void setProgramDesc(String programDesc) {
        this.programDesc = programDesc;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getPeriod() {
        return period;
    }

    public void setPeriod(int period) {
        this.period = period;
    }

    public String getComperes() {
        return comperes;
    }

    public void setComperes(String comperes) {
        this.comperes = comperes;
    }

    public String getLiveUrl() {
        return liveUrl;
    }

    public void setLiveUrl(String liveUrl) {
        this.liveUrl = liveUrl;
    }

    public String getProgramPic() {
        return programPic;
    }

    public void setProgramPic(String programPic) {
        this.programPic = programPic;
    }

    public String getLivePic() {
        return livePic;
    }

    public void setLivePic(String livePic) {
        this.livePic = livePic;
    }

    public String getTimeLength() {
        return timeLength;
    }

    public void setTimeLength(String timeLength) {
        this.timeLength = timeLength;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(long finishTime) {
        this.finishTime = finishTime;
    }

    public long getServerTime() {
        return serverTime;
    }

    public void setServerTime(long serverTime) {
        this.serverTime = serverTime;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getShowStartTime() {
        return showStartTime;
    }

    public void setShowStartTime(String showStartTime) {
        this.showStartTime = showStartTime;
    }

    public long getComperesId() {
        return comperesId;
    }

    public void setComperesId(long comperesId) {
        this.comperesId = comperesId;
    }

    protected LiveInfoDetail(Parcel in) {
        liveName = in.readString();
        liveId = in.readLong();
        liveDesc = in.readString();
        programId = in.readLong();
        programName = in.readString();
        programDesc = in.readString();
        beginTime = in.readString();
        endTime = in.readString();
        status = in.readInt();
        period = in.readInt();
        comperes = in.readString();
        liveUrl = in.readString();
        programPic = in.readString();
        livePic = in.readString();
        timeLength = in.readString();
        startTime = in.readLong();
        finishTime = in.readLong();
        serverTime = in.readLong();
        duration = in.readInt();
        roomId = in.readString();
        showStartTime = in.readString();
        comperesId = in.readLong();
    }

    public static final Creator<LiveInfoDetail> CREATOR = new Creator<LiveInfoDetail>() {
        @Override
        public LiveInfoDetail createFromParcel(Parcel in) {
            return new LiveInfoDetail(in);
        }

        @Override
        public LiveInfoDetail[] newArray(int size) {
            return new LiveInfoDetail[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(liveName);
        dest.writeLong(liveId);
        dest.writeString(liveDesc);
        dest.writeLong(programId);
        dest.writeString(programName);
        dest.writeString(programDesc);
        dest.writeString(beginTime);
        dest.writeString(endTime);
        dest.writeInt(status);
        dest.writeInt(period);
        dest.writeString(comperes);
        dest.writeString(liveUrl);
        dest.writeString(programPic);
        dest.writeString(livePic);
        dest.writeString(timeLength);
        dest.writeLong(startTime);
        dest.writeLong(finishTime);
        dest.writeLong(serverTime);
        dest.writeInt(duration);
        dest.writeString(roomId);
        dest.writeString(showStartTime);
        dest.writeLong(comperesId);
    }
}