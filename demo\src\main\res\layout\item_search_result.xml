<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="10dp">

    <ImageView
        android:id="@+id/iv_search_result_img"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_launcher_background" />

    <TextView
        android:id="@+id/tv_search_result_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lines="1"
        android:paddingStart="10dp"
        android:textColor="@color/colorBlack"
        android:textSize="16sp"
        app:layout_constraintStart_toEndOf="@id/iv_search_result_img"
        tools:text="等一个晴天" />

    <TextView
        android:id="@+id/tv_search_result_album"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lines="1"
        android:paddingStart="10dp"
        android:textColor="@color/color_black_50_transparent"
        android:textSize="12sp"
        app:layout_constraintStart_toEndOf="@id/iv_search_result_img"
        app:layout_constraintTop_toBottomOf="@id/tv_search_result_name"
        android:layout_marginTop="10dp"
        tools:text="QQ音乐" />

</android.support.constraint.ConstraintLayout>
