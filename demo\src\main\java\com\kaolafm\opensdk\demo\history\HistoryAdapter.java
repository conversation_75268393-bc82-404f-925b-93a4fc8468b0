package com.kaolafm.opensdk.demo.history;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.api.history.model.ListeningHistory;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;

/**
 * <AUTHOR>
 * @date 2019/3/11
 */
public class HistoryAdapter extends BaseAdapter<ListeningHistory> {

    @Override
    protected BaseHolder<ListeningHistory> getViewHolder(View view, int viewType) {
        return new HistoryHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_history;
    }

    static class HistoryHolder extends BaseHolder<ListeningHistory> {

        @BindView(R.id.iv_history_item_icon)
        ImageView mIvHistoryItemIcon;

        @BindView(R.id.tv_history_item_title)
        TextView mTvHistoryItemTitle;


        public HistoryHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(ListeningHistory listeningHistory, int position) {
            Glide.with(itemView).load(listeningHistory.getPicUrl()).into(mIvHistoryItemIcon);
            mTvHistoryItemTitle.setText(listeningHistory.getRadioTitle());
        }
    }
}
