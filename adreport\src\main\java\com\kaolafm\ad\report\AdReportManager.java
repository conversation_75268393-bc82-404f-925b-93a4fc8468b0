package com.kaolafm.ad.report;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.kaolafm.ad.report.bean.AdReportMonitorEvent;
import com.kaolafm.ad.report.bean.AdReportPlayEndEvent;
import com.kaolafm.ad.report.bean.BaseAdEvent;
import com.kaolafm.ad.report.db.bean.EventData;
import com.kaolafm.base.utils.ListUtil;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class AdReportManager {

    private ExecutorService executors = Executors.newSingleThreadExecutor();

    private Context mContext;

    private static final String TAG = AdReportManager.class.getSimpleName();

    public boolean hasInit = false;

    private AdReportManager() {

    }

    public static class KRADIO_AD_REPORT_MANAGER {
        private static final AdReportManager INSTANCE = new AdReportManager();
    }

    public static AdReportManager getInstance() {
        return KRADIO_AD_REPORT_MANAGER.INSTANCE;
    }

    public ExecutorService getExecutorService() {
        return executors;
    }

    public void init(Context context) {
        mContext = context;
        AdReportDBManager.getInstance().init();
        MonitorParameterManager.getInstance().loadMonitorParameter();
        hasInit = true;
    }

    public Context getContext() {
        return this.mContext;
    }

    public synchronized void addEvent(BaseAdEvent baseAdEvent) {
        EventPushTask eventPushTask = new EventPushTask(baseAdEvent);
        eventPushTask.report();
        checkNotReportedAndExecute();
    }

    /**
     * 检测数据库中是否有上报失败的数据
     */
    private void checkNotReportedAndExecute() {
        List<EventData> notReportList = AdReportDBManager.getInstance().queryNotReported();
        if (ListUtil.isEmpty(notReportList)) {
            return;
        }
        for (EventData eventData : notReportList) {
            Log.i(TAG, "notReportData:" + eventData.toString());
            new EventPushTask(eventData.getId(),createAdEvent(eventData.getType(),eventData.getReportData())).report();
            //设置状态为 REPORTING
            eventData.setStatus(Constant.REPORTING);

        }
        AdReportDBManager.getInstance().update(notReportList);
    }

    private BaseAdEvent createAdEvent(int type, String data) {
        BaseAdEvent baseAdEvent;
        switch (type) {
            case AdReportAgent.EventType
                    .PLAY_END:
                baseAdEvent = new Gson().fromJson(data, AdReportPlayEndEvent.class);
                break;
            case AdReportAgent.EventType
                    .MIAOZHEN_MONITOR:
            case AdReportAgent.EventType
                    .TALKING_DATA_MONITOR:
                baseAdEvent = new Gson().fromJson(data, AdReportMonitorEvent.class);
                break;
            default:
                baseAdEvent = new Gson().fromJson(data, BaseAdEvent.class);
                break;
        }

        Log.i(TAG,"createAdEvent :"+baseAdEvent.toString());
        return baseAdEvent;
    }

}


