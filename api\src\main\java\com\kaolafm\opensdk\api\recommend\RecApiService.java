package com.kaolafm.opensdk.api.recommend;

import com.kaolafm.opensdk.api.recommend.model.BaseSceneListData;
import com.kaolafm.opensdk.api.recommend.model.SceneDataList;

import java.util.HashMap;
import java.util.List;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.QueryMap;

/**
 * <AUTHOR> on 2019-07-18.
 */

public interface RecApiService {

    /**
     * 获取场景电台
     * @param tempMap
     * @return
     */
    @Headers(RecRequestConstant.DOMAIN_HEADER_MINUS_FEED_BACK)
    @GET(RecRequestConstant.REQUEST_GET_RADIO_SCENE)
    Single<BaseSceneListData<List<SceneDataList>>> getSceneRadioList(@QueryMap HashMap<String, String> tempMap);

}
