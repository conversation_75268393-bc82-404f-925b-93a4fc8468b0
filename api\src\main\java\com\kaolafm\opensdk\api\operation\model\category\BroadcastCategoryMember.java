package com.kaolafm.opensdk.api.operation.model.category;

/**
 * 人工运营分类成员：在线广播
 */
public class BroadcastCategoryMember extends CategoryMember {

    /** 在线广播资源id*/
    private long broadcastId;

    /** 在线广播的收听数*/
    private int playTimes;

    private String freq;

    public long getBroadcastId() {
        return broadcastId;
    }

    public void setBroadcastId(long broadcastId) {
        this.broadcastId = broadcastId;
    }

    public int getPlayTimes() {
        return playTimes;
    }

    public void setPlayTimes(int playTimes) {
        this.playTimes = playTimes;
    }

    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    @Override
    public String toString() {
        return "BroadcastCategoryMember{" +
                "broadcastId=" + broadcastId +
                ", playTimes=" + playTimes +
                '}';
    }
}
