package com.kaolafm.opensdk.demo.search;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;

import butterknife.BindView;

/**
 * 老接口的adapter
 * <AUTHOR>
 * @date 2018/8/6
 */

public class SearchResultAdapter extends BaseAdapter<SearchProgramBean> {

    @Override
    protected BaseHolder<SearchProgramBean> getViewHolder(View view, int viewType) {
        return new SearchResultHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_search_result;
    }

    static class SearchResultHolder extends BaseHolder<SearchProgramBean> {

        @BindView(R.id.iv_search_result_img)
        ImageView mIvSearchResultImg;

        @BindView(R.id.tv_search_result_name)
        TextView mTvSearchResultName;

        @BindView(R.id.tv_search_result_album)
        TextView mTvSearchResultAlbum;


        public SearchResultHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(SearchProgramBean searchProgramBean, int position) {
            Glide.with(itemView).load(searchProgramBean.getImg()).into(mIvSearchResultImg);
            mTvSearchResultName.setText(searchProgramBean.getName());
            mTvSearchResultAlbum.setText(searchProgramBean.getAlbumName());
        }
    }
}
