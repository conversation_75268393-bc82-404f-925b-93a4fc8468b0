package com.kaolafm.opensdk.demo.search;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.support.v7.widget.DividerItemDecoration;
import android.support.v7.widget.GridLayoutManager;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemSelectedListener;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;

import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.search.SearchRequest;
import com.kaolafm.opensdk.api.search.model.SearchProgramBean;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.lcodecore.tkrefreshlayout.RefreshListenerAdapter;
import com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout;

import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 关键词搜索页面
 * <AUTHOR> Yan
 * @date 2019/4/26
 */
public class KeywordSearchActivity extends BaseActivity {

    @BindView(R.id.et_search_keyword)
    EditText mEtSearchKeyword;

    @BindView(R.id.trf_search)
    TwinklingRefreshLayout mTwinklingRefreshLayout;

    @BindView(R.id.rv_search_suggested_word)
    RecyclerView mRvSearchSuggestedWord;

    @BindView(R.id.spinner_search_resource_type)
    Spinner mSpinnerSearchResourceType;

    @BindView(R.id.tv_keyword_search_commit)
    TextView mTvKeywordSearchCommit;

    private KeywordAdapter mKeywordAdapter;

    private SearchResultAdapter mSearchAdapter;

    private int mResourceType = ResType.TYPE_ALL;

    private TextWatcher mTextWatcher;
    private GridLayoutManager mGridLayoutManager;
    private LinearLayoutManager mLinearLayoutManager;

    private int mPageNum = 1;

    @Override
    public int getLayoutId() {
        return R.layout.activity_keyword_search;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("关键词搜索");

        mTwinklingRefreshLayout.setEnableRefresh(false);
        mRvSearchSuggestedWord.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
        mRvSearchSuggestedWord.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.HORIZONTAL));
        mKeywordAdapter = new KeywordAdapter();
        mKeywordAdapter.setOnItemClickListener((view, viewType, s, position) -> {
            mEtSearchKeyword.setText(s);
            search();
        });
        mTextWatcher = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String keyword = s.toString().trim();
                getSuggestedWord(keyword);
            }
        };
        mEtSearchKeyword.addTextChangedListener(mTextWatcher);

        mSearchAdapter = new SearchResultAdapter();
        mSearchAdapter.setOnItemClickListener((view, viewType, searchProgramBean, position) -> {
            Intent detailIntent = new Intent(KeywordSearchActivity.this, ProgramDetailActivity.class);
            detailIntent.putExtra(ProgramDetailActivity.KEY_PROGRAM, searchProgramBean);
            startActivity(detailIntent);
        });
        mTwinklingRefreshLayout.setOnRefreshListener(new RefreshListenerAdapter() {
            @Override
            public void onLoadMore(TwinklingRefreshLayout refreshLayout) {
                search();
            }
        });
        showHotWords();
    }

    private void getSuggestedWord(String keyword) {
        if (!TextUtils.isEmpty(keyword)) {
            showSuggestedWords(keyword);
        }else {
            mSearchAdapter.clear();
            showHotWords();
        }
    }

    private void showSuggestedWords(String keyword) {
        if (mLinearLayoutManager == null) {
            mLinearLayoutManager = new LinearLayoutManager(this);
        }
        mRvSearchSuggestedWord.setLayoutManager(mLinearLayoutManager);
        new SearchRequest().getSuggestedWords(keyword, new HttpCallback<List<String>>() {
            @Override
            public void onSuccess(List<String> strings) {
                mRvSearchSuggestedWord.setAdapter(mKeywordAdapter);
                if (mKeywordAdapter != null) {
                    mKeywordAdapter.setDataList(strings);
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("搜索联想词错误", exception);
            }
        });
    }

    private void showHotWords() {
        if (mGridLayoutManager == null) {
            mGridLayoutManager = new GridLayoutManager(this, 4, LinearLayoutManager.VERTICAL, false);
        }
        mRvSearchSuggestedWord.setLayoutManager(mGridLayoutManager);
        new SearchRequest().getHotWords(new HttpCallback<List<String>>() {
            @Override
            public void onSuccess(List<String> strings) {
                mRvSearchSuggestedWord.setAdapter(mKeywordAdapter);
                if (mKeywordAdapter != null) {
                    mKeywordAdapter.setDataList(strings);
                }
            }

            @Override
            public void onError(ApiException exception) {

            }
        });
    }

    private void hideInputMethod() {
        mEtSearchKeyword.clearFocus();
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(mEtSearchKeyword.getWindowToken(), 0);
    }

    @Override
    public void initData() {

    }

    @OnClick(R.id.tv_keyword_search_commit)
    public void onViewClicked() {
        search();
    }

    private void search() {
        String keyword = mEtSearchKeyword.getText().toString().trim();
        int selectType = getResourceType(mSpinnerSearchResourceType.getSelectedItemPosition());
        if(mResourceType != selectType){
            mPageNum = 1;
            mTwinklingRefreshLayout.setEnableLoadmore(true);
        }
        if(!(mRvSearchSuggestedWord.getAdapter() instanceof SearchResultAdapter)){
            mRvSearchSuggestedWord.setAdapter(mSearchAdapter);
        }
        mResourceType = selectType;
        if(TextUtils.isEmpty(keyword)){
            return;
        }
        hideInputMethod();
        if (mResourceType == ResType.TYPE_ALL) {
            searchAll(keyword);
        }else {
            searchByType(keyword);
        }
    }

    private int getResourceType(int position) {
        int type = ResType.TYPE_ALL;
        switch (position) {
            case 0:
                type = ResType.TYPE_ALL;
                break;
            case 1:
                type = ResType.TYPE_RADIO;
                break;
            case 2:
                type = ResType.TYPE_ALBUM;
                break;
            case 3:
                type = ResType.TYPE_AUDIO;
                break;
            case 4:
                type = ResType.TYPE_BROADCAST;
                break;
            default:
                break;
        }
        return type;
    }

    private void searchAll(String keyword) {

        new SearchRequest().searchAll(keyword, new HttpCallback<List<SearchProgramBean>>() {
            @Override
            public void onSuccess(List<SearchProgramBean> searchProgramBeans) {
                //mRvSearchSuggestedWord.setAdapter(mSearchAdapter);
                mSearchAdapter.setDataList(searchProgramBeans);
                mTwinklingRefreshLayout.setEnableLoadmore(false);
            }

            @Override
            public void onError(ApiException exception) {
                showError("搜索所有资源错误", exception);
            }
        });
    }
    private void searchByType(String keyword) {
        new SearchRequest().searchByType(keyword, mResourceType, mPageNum, 20,
                new HttpCallback<BasePageResult<List<SearchProgramBean>>>() {
                    @Override
                    public void onSuccess(BasePageResult<List<SearchProgramBean>> basePageResult) {
                        //加载更多参考其他加载更多方式。
                        if(mPageNum == 1){
                            //mRvSearchSuggestedWord.setAdapter(mSearchAdapter);
                            mSearchAdapter.setDataList(basePageResult.getDataList());
                        }else{
                            //mRvSearchSuggestedWord.setAdapter(mSearchAdapter);
                            mSearchAdapter.addDataList(basePageResult.getDataList());
                        }
                        mTwinklingRefreshLayout.finishLoadmore();
                        if(basePageResult.getHaveNext() == 1){
                            mPageNum ++;
                        }else{
                            mTwinklingRefreshLayout.setEnableLoadmore(false);
                        }
                    }

                    @Override
                    public void onError(ApiException exception) {
                        showError("指定类型搜索错误", exception);
                        Log.e("KeywordSearchActivity", "onError: "+exception.getDetailMessage());
                    }
                });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mEtSearchKeyword != null) {
            mEtSearchKeyword.removeTextChangedListener(mTextWatcher);
        }
    }

    static class KeywordAdapter extends BaseAdapter<String> {

        @Override
        protected BaseHolder<String> getViewHolder(View view, int viewType) {
            return new KeywordViewHolder(view);
        }

        @Override
        protected int getLayoutId(int viewType) {
            return android.R.layout.test_list_item;
        }
    }

    static class KeywordViewHolder extends BaseHolder<String> {

        @BindView(android.R.id.text1)
        TextView mTextView;

        public KeywordViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(String s, int position) {
            mTextView.setPadding(20, 0, 0, 0);
            mTextView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 20F);
            mTextView.setText(s);
        }
    }

}
