package com.kaolafm.opensdk.player.core.model;

import com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener;

/**
 * <AUTHOR> on 2019-05-23.
 */

public abstract class AMediaPlayer {

    public abstract void play();

    public abstract void pause();

    public abstract void stop();

    public abstract void start(String url, long duration, long position, int streamTypeChannel, boolean audioFadeEnabled, AudioFadeConfig audioFadeConfig, String httpProxy, boolean clearDnsCache);

    public abstract void seek(long mSec);

    public abstract boolean isPlaying();

    public abstract void setPlayerStateListener(IPlayerStateCoreListener listener);

    public abstract void setBufferProgressListener(IPlayerBufferProgressListener progressListener);

    /**
     * 设置播放器初始化成功回调
     *
     * @param initPlayerInitCompleteListener
     */
    public abstract void setInitPlayerInitCompleteListener(IPlayerInitCompleteListener initPlayerInitCompleteListener);


    /**
     * 设置进度
     *
     * @param urlDuration
     * @param totalDuration
     */
    abstract public void setDuration(long urlDuration, long totalDuration);

    public abstract void setPlayRatio(float ratio);

    public abstract void seekAtStart(long msec);

    public abstract int getPlayStatus();

    abstract public long getDuration();

    abstract public long getCurrentPosition();

    abstract public void preload(String url);

    abstract public void reset();

    abstract public void release();

    abstract public void prepare();

    abstract public void prepare(int needSeek);

    abstract public void prepare(int needSeek, int stream_type_channel);

    abstract public void setDataSource(String source);

    public String getDnsAddress() {
        return null;
    }

    public void setMediaVolume(float leftVolume, float rightVolume) {

    }

    public void setLoudnessNormalization(int active) {

    }

    public void setLogInValid() {

    }

    public void setAutoPlayOnPrepared(boolean enabled) {

    }

    public void setAudioFadeEnabled(boolean enabled) {

    }

    public void setAudioFadeConfig(AudioFadeConfig audioFadeConfig) {

    }


    public void setAttributesContentType(int content_type){
    }


    public void setAttributesUsage(int usage) {
    }

}
