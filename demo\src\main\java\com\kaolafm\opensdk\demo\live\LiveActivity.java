package com.kaolafm.opensdk.demo.live;

import android.content.Intent;
import android.os.Bundle;
import android.support.v4.app.FragmentManager;
import android.support.v4.app.FragmentTransaction;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.demo.live.ui.InputInfoDialog;
import com.kaolafm.opensdk.demo.live.ui.LiveFragment;
import com.kaolafm.opensdk.demo.live.ui.UserInfoManager;

public class LiveActivity extends BaseActivity {

    private static final int CODE_PERMISSION_REQUEST = 1;

    public static final String LIVE_ID = "id";

    private long mLiveId = 0;

    @Override
    public int getLayoutId() {
        return R.layout.activity_live;
    }

    @Override
    public void initArgs() {
        super.initArgs();
        Intent intent = getIntent();
        if (intent != null) {
            mLiveId = intent.getLongExtra(LIVE_ID, 1558796321L);
        }
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        new InputInfoDialog(this).setPositiveButton((type, id, nicknameOrPhone, avatar) -> {
            UserInfoManager infoManager = UserInfoManager.getInstance();
            infoManager.setUserId(id);
            infoManager.setAvatar(avatar);
            if (type == InputInfoDialog.TYPE_PHONE) {
                infoManager.setUsePhone(true);
                infoManager.setPhone(nicknameOrPhone);
            } else {
                infoManager.setUsePhone(false);
                infoManager.setNickName(nicknameOrPhone);
            }
            FragmentManager fm = getSupportFragmentManager();
            FragmentTransaction tr = fm.beginTransaction();
            tr.add(R.id.live_fragment_layout, LiveFragment.create(String.valueOf(mLiveId)));
            tr.commit();
        }).show();
    }

    @Override
    public void initData() {

    }
}
