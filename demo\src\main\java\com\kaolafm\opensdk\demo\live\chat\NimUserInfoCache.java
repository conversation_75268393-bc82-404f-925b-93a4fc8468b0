package com.kaolafm.opensdk.demo.live.chat;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.opensdk.demo.live.ui.LivePresenter;
import com.netease.nimlib.sdk.NIMClient;
import com.netease.nimlib.sdk.Observer;
import com.netease.nimlib.sdk.RequestCallback;
import com.netease.nimlib.sdk.RequestCallbackWrapper;
import com.netease.nimlib.sdk.ResponseCode;
import com.netease.nimlib.sdk.chatroom.model.ChatRoomMember;
import com.netease.nimlib.sdk.uinfo.UserService;
import com.netease.nimlib.sdk.uinfo.UserServiceObserve;
import com.netease.nimlib.sdk.uinfo.model.NimUserInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class NimUserInfoCache {

    private NimUserInfoCache() {
    }

    public static NimUserInfoCache getInstance() {
        return InstanceHolder.instance;
    }

    private Map<String, NimUserInfoCustomer> account2UserMap = new ConcurrentHashMap<>();

    private Map<String, List<RequestCallback<NimUserInfoCustomer>>> requestUserInfoMap = new ConcurrentHashMap<>(); // 重复请求处理

    /**
     * 构建缓存与清理
     */
    public void buildCache() {
        List<NimUserInfo> users = NIMClient.getService(UserService.class).getAllUserInfo();
        addOrUpdateUsers(users, false);
    }

    public void clear() {
        clearUserCache();
    }

    /**
     * 从云信服务器获取用户信息（重复请求处理）[异步]
     */
    public void getUserInfoFromRemote(final String account, final RequestCallback<NimUserInfoCustomer> callback) {
        NimUserInfoCustomer nimUserInfo = getUserInfo(account);
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(NimManager.TAG, "NimUserInfoCache getUserInfoFromRemote size = "
                            + account2UserMap.size() + " nimUserInfo = " + nimUserInfo);
        }
        if (nimUserInfo != null) {
            callback.onSuccess(nimUserInfo);
            return;
        }

        if (requestUserInfoMap.containsKey(account)) {
            if (callback != null) {
                requestUserInfoMap.get(account).add(callback);
            }
            return; // 已经在请求中，不要重复请求
        } else {
            List<RequestCallback<NimUserInfoCustomer>> cbs = new ArrayList<>();
            if (callback != null) {
                cbs.add(callback);
            }
            requestUserInfoMap.put(account, cbs);
        }

        List<String> accounts = new ArrayList<>(1);
        accounts.add(account);
        NIMClient.getService(UserService.class).fetchUserInfo(accounts).setCallback(new RequestCallbackWrapper<List<NimUserInfo>>() {

            @Override
            public void onResult(int code, List<NimUserInfo> users, Throwable exception) {
                NimUserInfo user = null;
                boolean hasCallback = requestUserInfoMap.get(account).size() > 0;
                if (code == ResponseCode.RES_SUCCESS && users != null && !users.isEmpty()) {
                    user = users.get(0);
                    if (user != null && !hasUser(user.getAccount())) {
                        if (LivePresenter.DEBUG_LIVE) {
                            Log.i(NimManager.TAG, "NimUserInfoCache " +
                                    "fetchUserInfo onResult error case 1");
                        }
                        account2UserMap.put(user.getAccount(), translateNimUserInfoToNimUserInfoCustomer(user));
                    }
                }
                // 处理回调
                if (hasCallback) {
                    List<RequestCallback<NimUserInfoCustomer>> cbs = requestUserInfoMap.get(account);
                    for (int i = 0; i < cbs.size(); i++) {
                        RequestCallback<NimUserInfoCustomer> cb = cbs.get(i);
                        requestUserInfoMap.remove(account);//fix /issues/23117
                        if (code == ResponseCode.RES_SUCCESS) {
                            cb.onSuccess(translateNimUserInfoToNimUserInfoCustomer(user));
                        } else {
                            cb.onFailed(code);
                        }
                    }
                }
            }
        });
    }

    /**
     * 从云信服务器获取批量用户信息[异步]
     */
    public void getUserInfoFromRemote(List<String> accounts, final RequestCallback<List<NimUserInfoCustomer>> callback) {
        NIMClient.getService(UserService.class).fetchUserInfo(accounts).setCallback(new RequestCallback<List<NimUserInfo>>() {
            @Override
            public void onSuccess(List<NimUserInfo> users) {
                // 这里不需要更新缓存，由监听用户资料变更（添加）来更新缓存
                if (callback == null) {
                    return;
                }
                if (null == users || users.isEmpty()) {
                    callback.onFailed(-1);
                } else {
                    int size = users.size();
                    ArrayList<NimUserInfoCustomer> nimUserInfoCustomerArrayList = new ArrayList<>(size);
                    for (int i = 0; i < size; i++) {
                        NimUserInfo nimUserInfo = users.get(i);
                        if (nimUserInfo == null) {
                            continue;
                        }
                        NimUserInfoCustomer nimUserInfoCustomer = translateNimUserInfoToNimUserInfoCustomer(nimUserInfo);
                        account2UserMap.put(nimUserInfo.getAccount(), nimUserInfoCustomer);
                        nimUserInfoCustomerArrayList.add(nimUserInfoCustomer);
                    }
                    callback.onSuccess(nimUserInfoCustomerArrayList);
                }
            }

            @Override
            public void onFailed(int code) {
                if (callback != null) {
                    callback.onFailed(code);
                }
            }

            @Override
            public void onException(Throwable exception) {
                if (callback != null) {
                    callback.onException(exception);
                }
            }
        });
    }

    public static class NimUserInfoCustomer {
        public String account;
        public String name;
        public String avatar;
    }

    public ChatRoomMember translateNimUserInfoCustomerToChatRoomMember(NimUserInfoCustomer nimUserInfoCustomer) {
        if (nimUserInfoCustomer == null) {
            return null;
        }
        ChatRoomMember chatRoomMember = new ChatRoomMember();
        chatRoomMember.setAccount(nimUserInfoCustomer.account);
        chatRoomMember.setAvatar(nimUserInfoCustomer.avatar);
        chatRoomMember.setNick(nimUserInfoCustomer.name);
        return chatRoomMember;
    }

    public NimUserInfoCustomer translateChatRoomMemberToNimUserInfoCustomer(ChatRoomMember chatRoomMember) {
        if (chatRoomMember == null) {
            return null;
        }
        NimUserInfoCustomer nimUserInfoCustomer = new NimUserInfoCustomer();
        nimUserInfoCustomer.account = chatRoomMember.getAccount();
        nimUserInfoCustomer.name = chatRoomMember.getNick();
        nimUserInfoCustomer.avatar = chatRoomMember.getAvatar();
        return nimUserInfoCustomer;
    }

    public NimUserInfoCustomer translateNimUserInfoToNimUserInfoCustomer(NimUserInfo nimUserInfo) {
        if (nimUserInfo == null) {
            return null;
        }
        NimUserInfoCustomer nimUserInfoCustomer = new NimUserInfoCustomer();
        nimUserInfoCustomer.account = nimUserInfo.getAccount();
        nimUserInfoCustomer.name = nimUserInfo.getName();
        nimUserInfoCustomer.avatar = nimUserInfo.getAvatar();
        return nimUserInfoCustomer;
    }

    public NimUserInfoCustomer translateExtensionToNimUserInfoCustomer(String account, Map<String, Object> extension) {
        if (TextUtils.isEmpty(account) || extension == null || extension.size() == 0) {
            return null;
        }

        String nickName = extension.containsKey(NimManager.NICK_NAME_KEY) ? (String) extension.get(NimManager.NICK_NAME_KEY) : null;
        if (TextUtils.isEmpty(nickName)) {
            return null;
        }
        String avatar = extension.containsKey(NimManager.AVATAR_KEY) ? (String) extension.get(NimManager.AVATAR_KEY) : null;
        if (TextUtils.isEmpty(avatar)) {
            return null;
        }

        NimUserInfoCustomer nimUserInfoCustomer = new NimUserInfoCustomer();
        nimUserInfoCustomer.account = account;
        nimUserInfoCustomer.name = nickName;
        nimUserInfoCustomer.avatar = avatar;

        return nimUserInfoCustomer;
    }

    public NimUserInfoCustomer getUserInfo(String account) {
        NimUserInfoCustomer nimUserInfoCustomer = null;
        if (TextUtils.isEmpty(account)) {
            return null;
        }
        if (hasUser(account)) {
            nimUserInfoCustomer = account2UserMap.get(account);
        }

        if (nimUserInfoCustomer != null &&
                !TextUtils.isEmpty(nimUserInfoCustomer.avatar) &&
                !TextUtils.isEmpty(nimUserInfoCustomer.name) &&
                !TextUtils.isEmpty(nimUserInfoCustomer.account)) { // 解决22053问题
            return nimUserInfoCustomer;
        }

        String roomId = NimManager.getInstance().getRoomId();

        if (!TextUtils.isEmpty(roomId)) {
            ChatRoomMember chatRoomMember = ChatRoomMemberCache.getInstance().getChatRoomMember(roomId, account);
            nimUserInfoCustomer = translateChatRoomMemberToNimUserInfoCustomer(chatRoomMember);
        }
        if (nimUserInfoCustomer != null &&
                !TextUtils.isEmpty(nimUserInfoCustomer.avatar) &&
                !TextUtils.isEmpty(nimUserInfoCustomer.name) &&
                !TextUtils.isEmpty(nimUserInfoCustomer.account)) { // 解决22053问题
            return nimUserInfoCustomer;
        } else {
            return null;
        }
    }

    private boolean hasUser(String account) {
        if (TextUtils.isEmpty(account) || account2UserMap == null) {
            return false;
        }
        return account2UserMap.containsKey(account);
    }

    /**
     * 获取用户显示名称。
     * 若设置了备注名，则显示备注名。
     * 若没有设置备注名，用户有昵称则显示昵称，用户没有昵称则显示帐号。
     *
     * @param account 用户帐号
     * @return
     */
//    public String getUserDisplayName(String account) {
//        String alias = getAlias(account);
//        if (!TextUtils.isEmpty(alias)) {
//            return alias;
//        }
//
//        return getUserName(account);
//    }

//    public String getAlias(String account) {
//        Friend friend = FriendDataCache.getInstance().getFriendByAccount(account);
//        if (friend != null && !TextUtils.isEmpty(friend.getAlias())) {
//            return friend.getAlias();
//        }
//        return null;
//    }

    // 获取用户原本的昵称
    public String getUserName(String account) {
        NimUserInfoCustomer userInfo = getUserInfo(account);
        if (userInfo != null && !TextUtils.isEmpty(userInfo.name)) {
            return userInfo.name;
        } else {
            return account;
        }
    }

//    public String getUserDisplayNameEx(String account) {
//        if (account.equals(NimUIKit.getAccount())) {
//            return "我";
//        }
//
//        return getUserDisplayName(account);
//    }

//    public String getUserDisplayNameYou(String account) {
//        if (account.equals(NimUIKit.getAccount())) {
//            return "你";  // 若为用户自己，显示“你”
//        }
//
//        return getUserDisplayName(account);
//    }

    private void clearUserCache() {
        account2UserMap.clear();
    }

    /**
     * ************************************ 用户资料变更监听(监听SDK) *****************************************
     */

    /**
     * 在Application的onCreate中向SDK注册用户资料变更观察者
     */
    public void registerObservers() {
        NIMClient.getService(UserServiceObserve.class).observeUserInfoUpdate(userInfoUpdateObserver, true);
    }

    public void unRegisterObservers() {
        NIMClient.getService(UserServiceObserve.class).observeUserInfoUpdate(userInfoUpdateObserver, false);
    }

    private Observer<List<NimUserInfo>> userInfoUpdateObserver = new Observer<List<NimUserInfo>>() {
        @Override
        public void onEvent(List<NimUserInfo> users) {
            if (users == null || users.isEmpty()) {
                return;
            }
            addOrUpdateUsers(users, true);
        }
    };

    /**
     * *************************************** User缓存管理与变更通知 ********************************************
     */

    private void addOrUpdateUsers(final List<NimUserInfo> users, boolean notify) {
        if (users == null || users.isEmpty()) {
            return;
        }

        // update cache
        for (int i = 0, size = users.size(); i < size; i++) {
            NimUserInfo nimUserInfo = users.get(i);
            NimUserInfoCustomer nimUserInfoCustomer = translateNimUserInfoToNimUserInfoCustomer(nimUserInfo);
            account2UserMap.put(nimUserInfo.getAccount(), nimUserInfoCustomer);
        }

        // println
//        List<String> accounts = getAccounts(users);

        // 通知变更
//        if (notify && accounts != null && !accounts.isEmpty()) {
//            NimUIKit.notifyUserInfoChanged(accounts); // 通知到UI组件
//        }
    }

    private ArrayList<String> getAccounts(List<NimUserInfo> users) {
        if (users == null || users.isEmpty()) {
            return null;
        }

        ArrayList<String> accounts = new ArrayList<>(users.size());
        for (int i = 0, size = users.size(); i < size; i++) {
            NimUserInfo user = users.get(i);
            accounts.add(user.getAccount());
        }

        return accounts;
    }

    /**
     * ************************************ 单例 **********************************************
     */

    private static class InstanceHolder {
        private final static NimUserInfoCache instance = new NimUserInfoCache();
    }
}
