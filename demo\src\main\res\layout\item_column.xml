<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="10dp"
    android:paddingTop="10dp">

    <ImageView
        android:id="@+id/iv_columngrp_img"
        android:layout_width="73dp"
        android:layout_height="73dp"
        android:scaleType="centerCrop"
        app:srcCompat="@mipmap/ic_launcher" />

    <TextView
        android:id="@+id/tv_columngrp_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="@color/colorBlack"
        android:textSize="18sp"
        app:layout_constraintStart_toEndOf="@+id/iv_columngrp_img"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="TextView" />

    <TextView
        android:id="@+id/tv_columngrp_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_columngrp_img"
        app:layout_constraintTop_toBottomOf="@+id/tv_columngrp_title"
        app:layout_constraintVertical_bias="0.0"
        tools:text="TextView" />

    <TextView
        android:id="@+id/tv_columngrp_des"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:textSize="14sp"
        app:layout_constraintStart_toEndOf="@+id/iv_columngrp_img"
        app:layout_constraintTop_toBottomOf="@+id/tv_columngrp_subtitle"
        tools:text="TextView" />

    <TextView
        android:id="@+id/tv_columnggrp_code"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginStart="8dp"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:background="@android:color/darker_gray"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/iv_columngrp_img"
        app:layout_constraintStart_toStartOf="@+id/iv_columngrp_img"
        tools:text="TextView" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="@android:color/darker_gray"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_columngrp_des" />

    <ImageView
        android:id="@+id/iv_columngrp_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginTop="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_more" />

    <TextView
        android:id="@+id/tv_columngrp_mark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="角标"
        android:textSize="10sp"
        android:background="@color/colorAccent"
        android:textColor="@android:color/white"
        app:layout_constraintStart_toStartOf="@+id/iv_columngrp_img"
        app:layout_constraintTop_toTopOf="@+id/iv_columngrp_img"

        />

</android.support.constraint.ConstraintLayout>
