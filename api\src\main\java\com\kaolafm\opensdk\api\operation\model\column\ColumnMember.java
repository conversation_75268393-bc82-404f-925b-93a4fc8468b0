package com.kaolafm.opensdk.api.operation.model.column;

import com.kaolafm.opensdk.api.operation.model.ImageFile;
import java.io.Serializable;
import java.util.Map;

/**
 * 栏目成员父类。
 * <br></br>
 * 对于该类的子类一般操作可以使用{@link com.kaolafm.opensdk.utils.operation.OperationAssister}工具类。
 */
public abstract class ColumnMember implements Serializable {

    /** 栏目成员的code值，用于获取子栏目成员。该值是可变的。*/
    private String code;

    /** 标题*/
    private String title;

    /** 副标题*/
    private String subtitle;

    /** 描述*/
    private String description;

    /** 是否显示角标，1是，0否*/
    private int cornerMark;

    /** 图片信息集合*/
    private Map<String, ImageFile> imageFiles;

    /** 额外信息，用于一些定制需求*/
    private Map<String, String> extInfo;

    /** SDK内部使用，开发者不需要关心，会一直为空*/
    private String type;

    /** SDK内部使用，开发者不需要关心*/
    private String callBack;

    /** SDK内部使用，开发者不需要关心*/
    private String outputMode;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getCornerMark() {
        return cornerMark;
    }

    public void setCornerMark(int cornerMark) {
        this.cornerMark = cornerMark;
    }

    public Map<String, ImageFile> getImageFiles() {
        return imageFiles;
    }

    public void setImageFiles(Map<String, ImageFile> imageFiles) {
        this.imageFiles = imageFiles;
    }

    public Map<String, String> getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(Map<String, String> extInfo) {
        this.extInfo = extInfo;
    }

    public String getCallBack() {
        return callBack;
    }

    public void setCallBack(String callBack) {
        this.callBack = callBack;
    }

    public String getOutputMode() {
        return outputMode;
    }

    public void setOutputMode(String outputMode) {
        this.outputMode = outputMode;
    }

    public ImageFile getIcon() {
        if (imageFiles != null) {
            return imageFiles.get("icon");
        }
        return null;
    }

    @Override
    public String toString() {
        return "ColumnMember{" +
                "code='" + code + '\'' +
                ", title='" + title + '\'' +
                ", subtitle='" + subtitle + '\'' +
                ", description='" + description + '\'' +
                ", cornerMark=" + cornerMark +
                ", imageFiles=" + imageFiles +
                ", extInfo=" + extInfo +
                ", type='" + type + '\'' +
                ", callBack='" + callBack + '\'' +
                ", outputMode='" + outputMode + '\'' +
                '}';
    }
}
