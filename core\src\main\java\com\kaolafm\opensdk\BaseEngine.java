package com.kaolafm.opensdk;

import android.app.Application;

import com.kaolafm.base.internal.DeviceId;
import com.kaolafm.opensdk.account.profile.AbstractProfileManager;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpCallback;

import javax.inject.Inject;

/**
 * SDK内部入口的基类，封装SDK 初始化/激活的公共部分。
 * 各个模块可以单独打包，也需要一起打包，只能有一个对外入口(对内入口需要多个，即真正执行逻辑的类。)，
 * 所以分开打包和合并打包的对外入口不一样，但是公共部分都一样。
 *
 * <AUTHOR>
 * @date 2020-03-05
 */
public abstract class BaseEngine<O extends Options, M extends AbstractProfileManager> implements Engine<O> {

    private volatile boolean initialized = false;

    @Inject
    @AppScope
    public M mProfileManager;

    @Override
    public void init(Application application, O options, HttpCallback<Boolean> callback) {
        if (!initialized) {
            internalInit(application, options, callback);
            initialized = true;
            if (callback != null) {
                callback.onSuccess(true);
            }
        } else {
            if (callback != null) {
                callback.onSuccess(true);
            }
        }
    }

    /**
     * 真正的初始化。不需要关心各种限定条件，直接做初始化操作就行，SDK内部使用。
     *
     * @param application
     * @param options
     * @param callback
     */
    protected abstract void internalInit(Application application, O options, HttpCallback<Boolean> callback);

    @Override
    public void activate(HttpCallback<Boolean> callback) {
        if (!initialized) {
            throw new RuntimeException("请先初始化sdk");
        }
        if (!isActivated()) {
            internalActivate(callback);
        } else {
            if (callback != null) {
                callback.onSuccess(true);
            }
        }
    }

    /**
     * 真正的激活。不需要关心其他限定条件，直接调用激活就行，SDK内部使用。
     *
     * @param callback
     */
    protected abstract void internalActivate(HttpCallback<Boolean> callback);

    @Override
    public void setLocation(String lng, String lat) {
        if (mProfileManager != null) {
            mProfileManager.setLongitude(lng);
            mProfileManager.setLatitude(lat);
        }
    }

    /**
     * 传入经度
     *
     * @param longitude
     * @deprecated 已过时，请使用{@link #setLocation(String, String)}
     */
    public void setLongitude(String longitude) {
        if (mProfileManager != null) {
            mProfileManager.setLongitude(longitude);
        }
    }

    /**
     * 传入纬度
     *
     * @param latitude
     * @deprecated 已过时，请使用{@link #setLocation(String, String)}
     */
    public void setLatitude(String latitude) {
        if (mProfileManager != null) {
            mProfileManager.setLatitude(latitude);
        }
    }

    public String getDeviceId() {
        return mProfileManager.getDeviceId();
    }

    public void setDeviceId(String deviceId) {
        if (mProfileManager != null) {
            mProfileManager.setDeviceId(deviceId);
        }
    }
}
