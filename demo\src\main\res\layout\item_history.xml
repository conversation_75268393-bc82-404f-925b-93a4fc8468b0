<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="8dp">
    <ImageView
        android:id="@+id/iv_history_item_icon"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:scaleType="centerCrop"
        />
    <TextView
        android:id="@+id/tv_history_item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@id/iv_history_item_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:paddingLeft="10dp"
        />
</android.support.constraint.ConstraintLayout>