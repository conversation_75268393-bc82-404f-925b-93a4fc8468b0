package com.kaolafm.opensdk.player.core.utils;

import android.content.Context;
import android.media.AudioManager;

import com.kaolafm.opensdk.player.core.model.AAudioFocus;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

/**
 * <AUTHOR> on 2019-06-11.
 */

public class AudioFocusManager extends AAudioFocus {
    private AudioManager mAudioManager;

    public AudioFocusManager(Context context) {
        mAudioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
    }

    /**
     * 请求焦点
     *
     */
    @Override
    public boolean requestAudioFocus() {
        if (mAudioManager == null) {
            return false;
        }
        boolean request = false;
        //通知客户端
        //mKLAudioFocusOperationListener
        PlayerCustomizeManager.getInstance().beforeRequestAudioFocus(mAudioManager);
        int result = mAudioManager.requestAudioFocus(mOnAudioFocusChangeListener, AudioManager.STREAM_MUSIC,
                AudioManager.AUDIOFOCUS_GAIN);
        PlayerLogUtil.log(getClass().getSimpleName(), "requestAudioFocus", "result = " + result);
        // result返回1是指申请成功，不是音频焦点返回1
        if(result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED){
            request = true;
            notifyAudioFocusChange(false, AudioManager.AUDIOFOCUS_GAIN);
        }
        return request;
    }


    @Override
    public boolean abandonAudioFocus() {
        if (mAudioManager == null) {
            return false;
        }
        boolean abandon = false;
        int rect = mAudioManager.abandonAudioFocus(mOnAudioFocusChangeListener);
        PlayerLogUtil.log(getClass().getSimpleName(), "abandonAudioFocus", "result = " + rect);
        // rect返回1是指释放成功，不是音频焦点返回1
        if(rect == AudioManager.AUDIOFOCUS_REQUEST_GRANTED){
            abandon = true;
            notifyAudioFocusChange(false, AudioManager.AUDIOFOCUS_LOSS);
        }
        return abandon;
    }


    private AudioManager.OnAudioFocusChangeListener mOnAudioFocusChangeListener = focusChange -> {
        notifyAudioFocusChange(true, focusChange);
    };

}
