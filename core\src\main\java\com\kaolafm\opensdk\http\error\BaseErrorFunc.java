package com.kaolafm.opensdk.http.error;

import android.support.annotation.Keep;
import android.support.annotation.NonNull;

import java.util.List;

import io.reactivex.functions.Function;

/**
 * 处理ApiException的Function方法(rxjava)基类
 *
 * <AUTHOR>
 * @date 2018/6/11
 */

public abstract class BaseErrorFunc<T> implements Function<Throwable, T>{

    private List<ResponseErrorListener> mErrorListenerList;

    public BaseErrorFunc(List<ResponseErrorListener> errorListenerList) {
        mErrorListenerList = errorListenerList;
    }

    @NonNull
    private ApiException handleError(Throwable throwable) {
        ApiException exception = HandleResponseError.handleError(throwable);
        //处理过的就不让在处理
        if (!exception.isHaveShow()) {
            if (mErrorListenerList != null && mErrorListenerList.size() > 0) {
                for (int i = 0, size = mErrorListenerList.size(); i < size; i++) {
                    mErrorListenerList.get(i).handleError(exception);
                }
            }
        }
        return exception;
    }

    @Override
    @Keep
    public T apply(Throwable throwable) throws Exception {
        return getError(handleError(throwable));
    }

    abstract T getError(ApiException e);
}
