package com.kaolafm.opensdk.http.core;

import com.trello.rxlifecycle2.LifecycleProvider;
import com.trello.rxlifecycle2.LifecycleTransformer;

import io.reactivex.Flowable;
import io.reactivex.Observable;
import io.reactivex.ObservableTransformer;
import io.reactivex.Single;
import io.reactivex.SingleTransformer;
import io.reactivex.functions.Function;
import retrofit2.Call;
import retrofit2.Response;

/**
 * <AUTHOR>
 * @date 2018/7/24
 */

public interface IRepositoryManager {

    /**
     * 根据传入的class文件获取对应的Retrofit Service
     *
     * @param service 传入的Class
     * @param <T>     实例类型
     */
    <T> T obtainRetrofitService(Class<T> service);

    /**
     * 根据传入的Class文件获取对应的RxCache Service
     *
     * @param cache 传入的Class
     * @param <T>   实例类型
     */

    <T> T obtainCacheService(Class<T> cache);

    /**
     * 取消所有缓存
     */
    void clearAllCache();

//    Context getContext();

    /**
     * 处理网络请求。Observable，
     *
     * @param observable retrofit接口返回的Observable对象
     * @param <T>        原始数据
     */
    <T> void doHttpDeal(Observable<T> observable);

    /**
     * 处理网络请求。Observable，
     *
     * @param tag        tag标记，用于取消网络请求
     * @param observable retrofit接口返回的Observable对象
     * @param callback   请求回调
     * @param <T>        原始数据
     */
    <T> void doHttpDeal(Object tag, Observable<T> observable, HttpCallback<T> callback);

    /**
     * 处理网络请求。Observable，
     *
     * @param tag        tag标记，用于取消网络请求
     * @param observable retrofit接口返回的Observable对象
     * @param function   用于数据处理, 不可为空。如果不需要处理数据，请使用{@link #doHttpDeal(Object, Observable, HttpCallback)}
     * @param callback   请求回调
     * @param <T>        原始数据
     * @param <R>        处理后的数据
     */
    <T, E> void doHttpDeal(Object tag, Observable<T> observable,
            Function<T, E> function, HttpCallback<E> callback);

    /**
     * 处理网络请求。Observable，
     *
     * @param observable retrofit接口返回的Observable对象
     * @param function   用于数据处理, 不可为空。如果不需要处理数据，请使用{@link #doHttpDeal(Object, Observable, HttpCallback)}
     * @param <T>        原始数据
     * @param <R>        处理后的数据
     */
    <T, E> void doHttpDeal(Observable<T> observable, Function<T, E> function);

    /**
     * 处理网络请求。Observable，
     *
     * @param observableTransformer 实现{@link LifecycleProvider}接口的类，用于将rxjava绑定到其生命周期上。
     * @param observable            retrofit接口返回的Observable对象
     * @param function              用于数据处理, 不可为空。如果不需要处理数据，请使用{@link #doHttpDeal(ObservableTransformer, Observable,
     *                              HttpCallback)}
     * @param callback              请求回调
     * @param <T>                   原始数据
     * @param <R>                   处理后的数据
     */
    <T, R> void doHttpDeal(ObservableTransformer observableTransformer, Observable<T> observable,
            Function<T, R> function, HttpCallback<R> callback);

    /**
     * * 处理网络请求。Observable，
     *
     * @param observableTransformer 实现{@link LifecycleProvider}接口的类，用于将rxjava绑定到其生命周期上。
     * @param observable            retrofit接口返回的Observable对象
     * @param callback              请求回调
     * @param <T>                   原始数据
     */
    <T> void doHttpDeal(ObservableTransformer observableTransformer, Observable<T> observable,
            HttpCallback<T> callback);

    /**
     * 处理网络请求。使用Single.
     *
     * @param single   retrofit接口返回的Single对象
     * @param function 用于数据处理, 不可为空。如果不需要处理数据，请使用{@link #doHttpDeal(Single, HttpCallback)}
     * @param callback 请求回调
     * @param <T>      原始数据
     * @param <R>      处理后的数据
     */
    <T, E> void doHttpDeal(Single<T> single, Function<T, E> function, HttpCallback<E> callback);

    <T, E> Single<E> doHttpDeal(Single<T> single, Function<T, E> function);

    /**
     * 处理网络请求。使用Single.
     *
     * @param single   retrofit接口返回的Single对象
     * @param callback 请求回调
     * @param <T>      原始数据
     */
    <T> void doHttpDeal(Single<T> single, HttpCallback<T> callback);

    /**
     * 处理网络请求。使用Single.
     *
     * @param single retrofit接口返回对象
     */
    <T> void doHttpDeal(Single<T> single);

    /**
     * 处理网络请求绑定生命周期。使用Single.
     *
     * @param singleTransformer 实现{@link LifecycleProvider}接口的类，用于将rxjava绑定到其生命周期上。
     * @param single            retrofit接口返回对象
     * @param callback          请求回调
     */
    <T> void doHttpDeal(SingleTransformer singleTransformer, Single<T> single, HttpCallback<T> callback);

    /**
     * 处理网络请求，用tag标记，可取消请求
     *
     * @param tag      tag标记，用于取消网络请求
     * @param single   retrofit接口返回对象
     * @param callback 请求回调
     */
    <T> void doHttpDeal(Object tag, Single<T> single, HttpCallback<T> callback);

    /**
     * 处理网络请求绑定生命周期。使用Single.
     *
     * @param singleTransformer 实现{@link LifecycleProvider}接口的类，用于将rxjava绑定到其生命周期上。
     * @param single            retrofit接口返回对象
     * @param function          用于数据处理, 不可为空。
     * @param callback          请求回调
     */
    <T, R> void doHttpDeal(SingleTransformer singleTransformer, Single<T> single,
            Function<T, R> function, HttpCallback<R> callback);

    /**
     * 处理带有tag标记的网络请求，可用于取消。
     *
     * @param obj      tag标记，用于取消网络请求
     * @param single   retrofit接口返回对象
     * @param function 用于数据处理, 不可为空。如果不需要处理数据，请使用{@link #doHttpDeal(Single, HttpCallback)}
     */
    <T, R> void doHttpDeal(Object obj, Single<T> single,
            Function<T, R> function, HttpCallback<R> callback);

    /**
     * 处理同步网络请求, 需要子线程中
     */
    <T> T doHttpDealSync(Call<T> call);

    /**
     * 处理同步网络请求, 需要子线程中, 返回Response。
     */
    <T> Response<T> doHttpDealSyncResponse(Call<T> call);

    void cancel(Object tag);

    <T> void doHttpDeal(LifecycleTransformer transformer, Flowable<T> flowable, FlowableCallback<T> callback);

    <T> void doHttpDeal(Object tag, Flowable<T> flowable, FlowableCallback<T> callback);

    <T> void doHttpDeal(Flowable<T> flowable, FlowableCallback<T> callback);
}
