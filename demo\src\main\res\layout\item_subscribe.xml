<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="10dp">
    <ImageView
        android:id="@+id/iv_subscribe_item_cover"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:scaleType="centerInside"
        />
    <TextView
        android:id="@+id/tv_subscribe_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="标题"
        android:textColor="@color/colorBlack"
        android:textSize="20sp"
        app:layout_constraintLeft_toRightOf="@id/iv_subscribe_item_cover"
        android:paddingLeft="10dp"
        android:maxLines="1"
        />
    <TextView
        android:id="@+id/tv_subscribe_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="小卡"
        app:layout_constraintLeft_toRightOf="@id/iv_subscribe_item_cover"
        app:layout_constraintTop_toBottomOf="@id/tv_subscribe_title"
        android:paddingLeft="10dp"
        android:layout_marginTop="10dp"
        android:maxLines="2"
        />

    <TextView
        android:id="@+id/tv_subscribe_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="订阅类型"
        app:layout_constraintLeft_toRightOf="@id/iv_subscribe_item_cover"
        app:layout_constraintTop_toBottomOf="@id/tv_subscribe_name"
        android:paddingLeft="10dp"
        android:layout_marginTop="5dp"
        android:maxLines="1"
        />
    <TextView
        android:id="@+id/tv_subscribe_id"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/iv_subscribe_item_cover"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        android:background="@color/color_black_50_transparent"
        />

</android.support.constraint.ConstraintLayout>