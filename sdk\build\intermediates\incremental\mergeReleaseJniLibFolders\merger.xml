<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\libs"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\src\release\jniLibs"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\ad\unspecified\release\jni"><file name="arm64-v8a/libkaolafmse.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\ad\unspecified\release\jni\arm64-v8a\libkaolafmse.so"/><file name="armeabi/libkaolafmse.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\ad\unspecified\release\jni\armeabi\libkaolafmse.so"/><file name="armeabi-v7a/libkaolafmse.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\ad\unspecified\release\jni\armeabi-v7a\libkaolafmse.so"/><file name="x86/libkaolafmse.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\ad\unspecified\release\jni\x86\libkaolafmse.so"/><file name="x86_64/libkaolafmse.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\ad\unspecified\release\jni\x86_64\libkaolafmse.so"/></source><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni"><file name="arm64-v8a/libdeStream.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\arm64-v8a\libdeStream.so"/><file name="arm64-v8a/libkaolafmffmpeg.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\arm64-v8a\libkaolafmffmpeg.so"/><file name="arm64-v8a/libkaolafmplayer.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\arm64-v8a\libkaolafmplayer.so"/><file name="arm64-v8a/libkaolafmsdl.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\arm64-v8a\libkaolafmsdl.so"/><file name="arm64-v8a/libkaolafmsoundtouch.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\arm64-v8a\libkaolafmsoundtouch.so"/><file name="armeabi/libdeStream.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\armeabi\libdeStream.so"/><file name="armeabi/libkaolafmffmpeg.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\armeabi\libkaolafmffmpeg.so"/><file name="armeabi/libkaolafmplayer.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\armeabi\libkaolafmplayer.so"/><file name="armeabi/libkaolafmsdl.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\armeabi\libkaolafmsdl.so"/><file name="armeabi/libkaolafmsoundtouch.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\armeabi\libkaolafmsoundtouch.so"/><file name="armeabi-v7a/libdeStream.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\armeabi-v7a\libdeStream.so"/><file name="armeabi-v7a/libkaolafmffmpeg.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\armeabi-v7a\libkaolafmffmpeg.so"/><file name="armeabi-v7a/libkaolafmplayer.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\armeabi-v7a\libkaolafmplayer.so"/><file name="armeabi-v7a/libkaolafmsdl.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\armeabi-v7a\libkaolafmsdl.so"/><file name="armeabi-v7a/libkaolafmsoundtouch.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\armeabi-v7a\libkaolafmsoundtouch.so"/><file name="x86/libdeStream.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\x86\libdeStream.so"/><file name="x86/libkaolafmffmpeg.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\x86\libkaolafmffmpeg.so"/><file name="x86/libkaolafmplayer.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\x86\libkaolafmplayer.so"/><file name="x86/libkaolafmsdl.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\x86\libkaolafmsdl.so"/><file name="x86/libkaolafmsoundtouch.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\x86\libkaolafmsoundtouch.so"/><file name="x86_64/libdeStream.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\x86_64\libdeStream.so"/><file name="x86_64/libkaolafmffmpeg.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\x86_64\libkaolafmffmpeg.so"/><file name="x86_64/libkaolafmplayer.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\x86_64\libkaolafmplayer.so"/><file name="x86_64/libkaolafmsdl.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\x86_64\libkaolafmsdl.so"/><file name="x86_64/libkaolafmsoundtouch.so" path="C:\Users\<USER>\AndroidStudioProjects\sdk\1.6.0lantu\kaolaopensdk\sdk\build\intermediates\exploded-aar\kaolaopensdk\player\unspecified\release\jni\x86_64\libkaolafmsoundtouch.so"/></source></dataSet></merger>