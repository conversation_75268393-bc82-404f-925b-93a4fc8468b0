<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_kaola_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_kaola_qr_code"
        android:layout_width="170dp"
        android:layout_height="170dp"
        android:contentDescription="@null"
        app:layout_constraintStart_toStartOf="parent" />

    <Button
        android:id="@+id/btn_kaola_start_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="开始循环检查二维码状态"
        app:layout_constraintTop_toBottomOf="@id/iv_kaola_qr_code" />

    <Button
        android:id="@+id/btn_kaola_stop_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="停止"
        app:layout_constraintStart_toEndOf="@id/btn_kaola_start_check"
        app:layout_constraintTop_toBottomOf="@id/iv_kaola_qr_code" />

    <TextView
        android:id="@+id/tv_qr_status_history"
        android:layout_width="0dp"
        android:layout_height="170dp"
        android:lineSpacingExtra="5dp"
        android:paddingStart="10dp"
        android:scrollbars="vertical"
        android:textColor="@color/colorAccent"
        android:textSize="10sp"
        app:layout_constraintLeft_toRightOf="@id/iv_kaola_qr_code"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btn_kaola_fetch_code"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="获取Code"
        app:layout_constraintTop_toBottomOf="@id/btn_kaola_start_check" />
    <Button
        android:id="@+id/btn_kaola_bind_kradio_uuid"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="UUID绑定设备"
        app:layout_constraintStart_toEndOf="@id/btn_kaola_fetch_code"
        app:layout_constraintTop_toBottomOf="@id/btn_kaola_start_check"
        />
    <Button
        android:id="@+id/btn_kaola_bind_kradio"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="绑定设备"
        app:layout_constraintTop_toBottomOf="@id/btn_kaola_fetch_code"
        />

    <Button
        android:id="@+id/btn_kaola_unbind_kradio"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="解绑设备"
        app:layout_constraintStart_toEndOf="@id/btn_kaola_bind_kradio"
        app:layout_constraintTop_toBottomOf="@id/btn_kaola_fetch_code" />

    <Button
        android:id="@+id/btn_kaola_bind_when_auth"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="一键绑定"
        app:layout_constraintTop_toBottomOf="@id/btn_kaola_bind_kradio"
        />

    <Button
        android:id="@+id/btn_kaola_login_id"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@+id/btn_kaola_bind_when_auth"
        app:layout_constraintTop_toBottomOf="@+id/btn_kaola_unbind_kradio"
        android:text="UserId登录"
        />
    <Button
        android:id="@+id/btn_kaola_login_refresh"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@+id/btn_kaola_login_id"
        app:layout_constraintTop_toBottomOf="@+id/btn_kaola_unbind_kradio"
        android:text="刷新token"
        />

    <Button
        android:id="@+id/btn_kaola_login_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/btn_kaola_login_refresh"
        android:text="是否登录"
        />

    <ImageView
        android:id="@+id/iv_kaola_avatar"
        android:layout_width="100dp"
        android:layout_height="100dp"
        app:layout_constraintStart_toEndOf="@+id/btn_kaola_stop_check"
        app:layout_constraintTop_toBottomOf="@+id/iv_kaola_qr_code" />
    <TextView
        android:id="@+id/tv_kaola_nick_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/colorAccent"
        android:textSize="16sp"
        app:layout_constraintTop_toTopOf="@id/iv_kaola_avatar"
        app:layout_constraintStart_toStartOf="@id/iv_kaola_avatar"
        app:layout_constraintEnd_toEndOf="@id/iv_kaola_avatar"
        app:layout_constraintBottom_toBottomOf="@id/iv_kaola_avatar"
        />

</android.support.constraint.ConstraintLayout>
