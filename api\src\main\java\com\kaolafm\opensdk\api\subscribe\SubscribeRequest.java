package com.kaolafm.opensdk.api.subscribe;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import java.util.List;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: SubscribeRequest.java                                               
 *                                                                  *
 * Created in 2018/8/15 上午11:16                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class SubscribeRequest extends BaseRequest {

    public static final int TYPE_ALL = 0;//0表示全部
    public static final int TYPE_ALBUM = 1;//专辑
    public static final int TYPE_RADIO = 2;//电台
    public static final int TYPE_BROADCAST = 3;//广播
    public static final int TYPE_BROADCAST_NEW = 4;//广播 新
    public static final int TYPE_SONG = 5;//单曲
    public static final int TYPE_ALBUM_AI_RADIO = 6;//专辑/AI电台


    private SubscribeService mService;


    public SubscribeRequest() {
        mService = obtainRetrofitService(SubscribeService.class);
    }

    /**
     * 订阅列表(All)
     *
     * @param type  类型 收藏类型 0：全部，1：专辑， 2：电台，3. 广播 ，4.广播 ，5.单曲 ，6.专辑/AI电台
     * @param pageNum  第几页
     * @param pageSize 每页数据个数
     * @param callback 回调结果
     */
    public void getSubscribeList(int type, int pageNum, int pageSize,
                                 HttpCallback<BasePageResult<List<SubscribeInfo>>> callback) {
        doHttpDeal(mService.getSubscribeList(type, pageNum, pageSize), BaseResult::getResult, callback);
    }

    /**
     * 订阅列表(All)
     *
     * @param pageNum  第几页
     * @param pageSize 每页数据个数
     * @param callback 回调结果
     */
    public void getSubscribeList(int pageNum, int pageSize,
            HttpCallback<BasePageResult<List<SubscribeInfo>>> callback) {
        doHttpDeal(mService.getSubscribeList(TYPE_ALL, pageNum, pageSize), BaseResult::getResult, callback);
    }

    /**
     * 订阅列表(All)
     *
     * @param type  类型 收藏类型 0：全部，1：专辑， 2：电台，3. 广播 ，4.广播 ，5.单曲 ，6.专辑/AI电台
     * @param pageNum  第几页
     * @param pageSize 每页数据个数
     */
    private BasePageResult<List<SubscribeInfo>> getSubscribeListSync(int type, int pageNum, int pageSize) {
        BaseResult<BasePageResult<List<SubscribeInfo>>> baseResult = doHttpDealSync(mService.getSubscribeListSync(type, pageNum, pageSize));
        return baseResult != null? baseResult.getResult() : null;
    }

    /**
     * 订阅列表(All)
     *
     * @param pageNum  第几页
     * @param pageSize 每页数据个数
     */
    private BasePageResult<List<SubscribeInfo>> getSubscribeListSync(int pageNum, int pageSize) {
        BaseResult<BasePageResult<List<SubscribeInfo>>> baseResult = doHttpDealSync(mService.getSubscribeListSync(TYPE_ALL, pageNum, pageSize));
        return baseResult != null? baseResult.getResult() : null;
    }


    /**
     * 订阅
     *
     * @param id 专辑、PGC、广播的id
     */
    public void subscribe(long id, HttpCallback<SubscribeStatus> callback) {
        doHttpDeal(mService.subscribe(id), BaseResult::getResult, callback);
    }


    /**
     * 订阅(同步方法)
     *
     * @param radioId 专辑、PGC、广播的id
     */
    private SubscribeStatus subscribeSync(long radioId) {
        BaseResult<SubscribeStatus> baseResult = doHttpDealSync(mService.subscribeSync(radioId));
        return baseResult != null ? baseResult.getResult() : null;
    }

    /**
     * 取消订阅
     *
     * @param id 专辑、PGC、广播的id
     */
    public void unsubscribe(long id, HttpCallback<Boolean> callback) {
        doHttpDeal(mService.unsubscribe(id), baseResult -> {
            SubscribeStatus status = baseResult.getResult();
            return status != null && status.getStatus() != SubscribeStatus.STATE_FAILURE;
        }, callback);
    }

    /**
     * 取消订阅 (同步)
     *
     * @param radioId 订阅id
     */
    private SubscribeStatus unsubscribeSync(long radioId) {
        BaseResult<SubscribeStatus> baseResult = doHttpDealSync(mService.unsubscribeSync(radioId));
        return baseResult != null ? baseResult.getResult() : null;
    }

    /**
     * 是否订阅
     *
     * @param id       专辑、PGC、广播的id
     * @param callback true 已订阅；false 未订阅
     */
    public void isSubscribed(long id, HttpCallback<Boolean> callback) {
        doHttpDeal(mService.isSubscribed(id),
                baseResult -> {
                    SubscribeStatus status = baseResult.getResult();
                    return (status != null && status.getStatus() != SubscribeStatus.STATE_FAILURE);
                }, callback);
    }

    /**
     * 是否订阅(同步)
     *
     * @param radioId 订阅id
     */
    private SubscribeStatus isSubscribedSync(long radioId) {
        BaseResult<SubscribeStatus> baseResult = doHttpDealSync(mService.isSubscribedSync(radioId));
        return baseResult != null ? baseResult.getResult() : null;
    }

    public void getUserFollowRadio(String blockId, HttpCallback<List<AudioDetails>> callback) {
        doHttpDeal(mService.getUserFollowRadio(blockId), BaseResult::getResult, callback);
    }

}
