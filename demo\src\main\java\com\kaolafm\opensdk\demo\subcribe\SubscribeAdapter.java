package com.kaolafm.opensdk.demo.subcribe;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import com.bumptech.glide.Glide;
import com.kaolafm.opensdk.api.subscribe.SubscribeInfo;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.BaseHolder;
import com.kaolafm.opensdk.demo.R;

/**
 * <AUTHOR>
 * @date 2019-03-19
 */
public class SubscribeAdapter extends BaseAdapter<SubscribeInfo> {

    @Override
    protected BaseHolder<SubscribeInfo> getViewHolder(View view, int viewType) {
        return new SubscribeViewHolder(view);
    }

    @Override
    protected int getLayoutId(int viewType) {
        return R.layout.item_subscribe;
    }

    static class SubscribeViewHolder extends BaseHolder<SubscribeInfo> {

        @BindView(R.id.iv_subscribe_item_cover)
        ImageView mIvSubscribeItemCover;

        @BindView(R.id.tv_subscribe_id)
        TextView mTvSubscribeId;

        @BindView(R.id.tv_subscribe_name)
        TextView mTvSubscribeName;

        @BindView(R.id.tv_subscribe_title)
        TextView mTvSubscribeTitle;

        @BindView(R.id.tv_subscribe_type)
        TextView mTvSubscribeType;

        public SubscribeViewHolder(View itemView) {
            super(itemView);
        }

        @Override
        public void setupData(SubscribeInfo subscribeItem, int position) {
            mTvSubscribeId.setText(String.valueOf(subscribeItem.getId()));
            mTvSubscribeTitle.setText(subscribeItem.getName());
//            String name = subscribeItem.getName();
//            if (TextUtils.isEmpty(name)) {
//                name = subscribeItem.getDes();
//            }
            mTvSubscribeName.setText(subscribeItem.getDesc());
            // 收藏类型 0：全部，1：专辑， 2：电台，3. 广播 ，4.广播 ，5.单曲 ，6.专辑/AI电台
            int typeInt = subscribeItem.getType();
            String type = "";
            if(typeInt  == 0){
                type = "全部";
            }else if(typeInt  == 1){
                type = "专辑";
            }else if(typeInt  == 2){
                type = "电台";
            }else if(typeInt  == 3){
                type = "广播";
            }else if(typeInt  == 4){
                type = "广播(新)";
            }else if(typeInt  == 5){
                type = "单曲";
            }else if(typeInt  == 6){
                type = "专辑/AI电台";
            }
            mTvSubscribeType.setText(type);
            Glide.with(itemView).load(subscribeItem.getImg()).into(mIvSubscribeItemCover);
        }
    }
}
