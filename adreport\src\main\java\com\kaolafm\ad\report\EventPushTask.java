package com.kaolafm.ad.report;

import android.util.Log;

import com.kaolafm.ad.report.bean.BaseAdEvent;
import com.kaolafm.ad.report.db.bean.EventData;
import com.kaolafm.ad.report.net.AdReportNetHelper;
import com.kaolafm.ad.report.util.EventUtil;

public class EventPushTask {

    private BaseAdEvent baseAdEvent;

    private long id = -1;

    private static final String TAG = EventPushTask.class.getSimpleName();

    public EventPushTask(BaseAdEvent baseAdEvent){
        this.baseAdEvent = baseAdEvent;
    }

    public EventPushTask(long id,BaseAdEvent baseAdEvent){
        this.id = id;
        this.baseAdEvent = baseAdEvent;
    }

    public void report(){
        Log.i(TAG,"start ad report ,event:"+baseAdEvent +",db id:"+id);

        if (baseAdEvent == null) {
            return;
        }

        AdReportNetHelper.getInstance().sendEvent(baseAdEvent, aBoolean -> {

            Log.i(TAG,"ad report result:"+ aBoolean +" , event data:"+baseAdEvent.toString());
            EventData eventData = EventUtil.getEventBean(baseAdEvent);

            if (aBoolean) {
                AdReportDBManager.getInstance().delete(id);
            } else {
                eventData.setStatus(Constant.NOT_REPORTED);
                AdReportDBManager.getInstance().insert(eventData);
            }
        });
    }

}
