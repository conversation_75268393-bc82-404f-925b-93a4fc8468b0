apply plugin: 'groovy'

dependencies {
    implementation gradleApi()
    implementation localGroovy()
    implementation 'com.android.tools.build:gradle:3.5.3'
//    implementation 'com.github.dcendents:android-maven-gradle-plugin:2.1' //用于打包Maven所需文件
    implementation 'com.jfrog.bintray.gradle:gradle-bintray-plugin:1.8.5' //用于上传Maven生成的文件到Bintray
//    implementation "org.jfrog.buildinfo:build-info-extractor-gradle:3.1.1" //用于上传JFrog Artifactory本地
    implementation 'com.google.auto.service:auto-service:1.0-rc3'
    annotationProcessor 'com.google.auto.service:auto-service:1.0-rc3'
    implementation 'org.greenrobot:greendao-gradle-plugin:3.2.2'
}
repositories {
    mavenCentral()
    jcenter()
    google()
    maven { url 'https://maven.aliyun.com/repository/public' }
    maven { url 'https://maven.aliyun.com/repository/google' }
    maven { url 'https://maven.aliyun.com/repository/jcenter' }
    maven { url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/' }
}