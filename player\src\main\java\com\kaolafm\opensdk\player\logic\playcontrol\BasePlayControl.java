package com.kaolafm.opensdk.player.logic.playcontrol;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.media.AudioRequest;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.api.media.model.AudioPlayInfo;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.core.PlayerService;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.core.model.AAudioFocus;
import com.kaolafm.opensdk.player.core.model.AudioFadeConfig;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.OneKeyPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.PurchaseOneKeyPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayUrlData;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;
import com.kaolafm.opensdk.player.logic.util.ToneQualityHelper;

import java.util.List;


/**
 * <AUTHOR> on 2019/3/18.
 */

public class BasePlayControl implements IPlayControl {

    private PlayerService.PlayerServiceBinder mPlayerBinder;

    protected PlayItem mPlayItem;

    private BasePlayStateListener mBasePlayControlListener;


    public BasePlayControl() {
    }

    @Override
    public void setPlayStateListener(BasePlayStateListener iPlayerStateListener) {
        mBasePlayControlListener = iPlayerStateListener;
    }

    @Override
    public void setBind(PlayerService.PlayerServiceBinder mediaPlayerServiceBind) {
        mPlayerBinder = mediaPlayerServiceBind;
    }

    @Override
    public void play() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        if (isLiving(mPlayItem)) {
            mPlayerBinder.start(mPlayItem.getPlayUrl(), 0, 0, true);
        } else {
            mPlayerBinder.play();
        }
    }

    @Override
    public void pause() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        if (isLiving(mPlayItem)) {
            mPlayerBinder.reset();
        }
        mPlayerBinder.pause();
    }

    @Override
    public void stop() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.stop();
    }

    @Override
    public void reset() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.reset();

    }

    @Override
    public void release() {

    }

    @Override
    public void seek(int position) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }

        mPlayerBinder.seek(position);
    }

    @Override
    public void playTempTask(String url) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.start(url, 0, false, false);
    }

    @Override
    public void start(int type, PlayItem playItem) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            PlayerLogUtil.log(getClass().getSimpleName(), "start", "mPlayerBinder is null");
            return;
        }
        if (PlayerPreconditions.checkNull(playItem)) {
            PlayerLogUtil.log(getClass().getSimpleName(), "start", "playItem is null");
            return;
        }
        mPlayItem = playItem;

        if (isLiving(playItem)) {
            mPlayerBinder.start(playItem.getPlayUrl(), 0, 0, true);
        } else if (isAlbum(playItem)) {
            //加密音频播放测试
//            String testUrl = "https://ytmedia.radio.cn/CCYT%2F2021%2F08%2F02%2F1627892285de0d974ced6fefe3381f20be9d8cd70alc.mp3";
////            String testUrl = Environment.getExternalStorageDirectory().getAbsolutePath() +"/Download/decode_test.mp3";
////            String testUrl = Environment.getExternalStorageDirectory().getAbsolutePath() +"/Download/encode_test.mp3";
//            Log.d("start play", "testUrl:" + testUrl);
////            String testUrl = "/sdcard/Download/encode_test.mp3";
//            playItem.setPlayUrl(testUrl);
//            playWithPosition(playItem);
            // 由于添加了付费的内容播放，专辑内容需要单独请求播放的url地址
            getPlayUrlWithCallBack(playItem, new OnGetPlayUrlData() {
                @Override
                public void onDataGet(String url) {
                    Log.i("BasePlayControl", "getPlayurl:" + url);
                    if (TextUtils.isEmpty(url)) {
                        Log.e("BasePlayControl", "Final URL is empty, attempting emergency fallback");
                        // 尝试使用 playItem 中可能存在的原始URL
                        attemptEmergencyPlayback(playItem);
                        return;
                    }
                    playItem.setPlayUrl(url);
                    playWithPosition(playItem);
                }

                @Override
                public void onDataError(ApiException e) {
                    Log.e("BasePlayControl", "getPlayUrl error:" + e.toString());
                    // 尝试紧急播放方案
                    attemptEmergencyPlayback(playItem);
                }
            });
        } else {
            setPlayUrl(playItem);
            playWithPosition(playItem);
        }
    }

    @Override
    public void start(int type, PlayItem playItem, boolean isAutoPlay) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            PlayerLogUtil.log(getClass().getSimpleName(), "start", "mPlayerBinder is null");
            return;
        }
        if (PlayerPreconditions.checkNull(playItem)) {
            PlayerLogUtil.log(getClass().getSimpleName(), "start", "playItem is null");
            return;
        }
        mPlayItem = playItem;

        if (isLiving(playItem)) {
            mPlayerBinder.start(playItem.getPlayUrl(), 0, 0, true);
        } else if (isAlbum(playItem)) {
            //加密音频播放测试
//            String testUrl = "https://ytmedia.radio.cn/CCYT%2F2021%2F08%2F02%2F1627892285de0d974ced6fefe3381f20be9d8cd70alc.mp3";
////            String testUrl = Environment.getExternalStorageDirectory().getAbsolutePath() +"/Download/decode_test.mp3";
////            String testUrl = Environment.getExternalStorageDirectory().getAbsolutePath() +"/Download/encode_test.mp3";
//            Log.i("start play", "testUrl:" + testUrl);
////            String testUrl = "/sdcard/Download/encode_test.mp3";
//            playItem.setPlayUrl(testUrl);
//            playWithPosition(playItem);
            // 由于添加了付费的内容播放，专辑内容需要单独请求播放的url地址
            getPlayUrlWithCallBack(playItem, new OnGetPlayUrlData() {
                @Override
                public void onDataGet(String url) {
                    Log.i("BasePlayControl", "getPlayurl:" + url);
                    if (TextUtils.isEmpty(url)) {
                        Log.e("BasePlayControl", "Final URL is empty, attempting emergency fallback");
                        // 尝试使用 playItem 中可能存在的原始URL
                        attemptEmergencyPlaybackWithAutoPlay(playItem, isAutoPlay);
                        return;
                    }
                    playItem.setPlayUrl(url);
                    playWithPosition(playItem, isAutoPlay);
                }

                @Override
                public void onDataError(ApiException e) {
                    Log.e("BasePlayControl", "getPlayUrl error:" + e.toString());
                    // 尝试紧急播放方案
                    attemptEmergencyPlaybackWithAutoPlay(playItem, isAutoPlay);
                }
            });
        } else {
            setPlayUrl(playItem);
            playWithPosition(playItem, isAutoPlay);
        }
    }

    /**
     * 根据播放的记录进行seek播放
     *
     * @param playItem
     */
    private void playWithPosition(PlayItem playItem) {
        PlayerLogUtil.log("play url= " + playItem.getPlayUrl());
        if (playItem.getPosition() > 0) {
            mPlayerBinder.start(playItem.getPlayUrl(), mPlayItem.getDuration(), mPlayItem.getPosition(), false,true);
        } else {
            mPlayerBinder.start(playItem.getPlayUrl(), mPlayItem.getDuration(), false, false,true);
        }
    }

    /**
     * 根据播放的记录进行seek播放
     *
     * @param playItem
     */
    private void playWithPosition(PlayItem playItem, boolean isAutoPlay) {
        PlayerLogUtil.log("play url= " + playItem.getPlayUrl());
        if (playItem.getPosition() > 0) {
            mPlayerBinder.start(playItem.getPlayUrl(), mPlayItem.getDuration(), mPlayItem.getPosition(), false,isAutoPlay);
        } else {
            mPlayerBinder.start(playItem.getPlayUrl(), mPlayItem.getDuration(), false, false,isAutoPlay);
        }
    }

    /**
     * 获取播放地址，并通过回调返回
     *
     * @param playItem
     * @param callback
     */
    private void getPlayUrlWithCallBack(PlayItem playItem, OnGetPlayUrlData callback) {
        PlayUrlData playUrlData = null;
        if (playItem instanceof AlbumPlayItem) {
            playUrlData = ((AlbumPlayItem) playItem).getPlayUrlData();
        } else if (playItem instanceof PurchaseOneKeyPlayItem) {
            playUrlData = ((PurchaseOneKeyPlayItem) playItem).getPlayUrlData();
        } else if (playItem instanceof OneKeyPlayItem) {
            playUrlData = ((OneKeyPlayItem) playItem).getPlayUrlData();
        }
        requestPlayUrl(playItem, playUrlData, callback);
    }

    private boolean isAlbum(PlayItem playItem) {
        int type = playItem.getType();
        return type == PlayerConstants.RESOURCES_TYPE_ALBUM//专辑类型的
                || type == PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE//订阅的一键播放
                || type == PlayerConstants.RESOURCES_TYPE_PURCHASE_ONE_KEY_LISTENER_TYPE//已购的一键播放
                ;
    }


    /**
     * 根据音质选择不同的url
     */
    private void setPlayUrl(PlayItem playItem) {
        if (playItem == null) {
            PlayerLogUtil.log(getClass().getSimpleName(), "setPlayUrl", "playItem is null");
            return;
        }
        int type = playItem.getType();
        switch (type) {
//            case PlayerConstants.RESOURCES_TYPE_ALBUM: {
//                AlbumPlayItem albumPlayItem = (AlbumPlayItem) playItem;
//                setPlayUrl(playItem, albumPlayItem.getPlayUrlData());
//            }
//            break;
            case PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE: {
                OneKeyPlayItem oneKeyPlayItem = (OneKeyPlayItem) playItem;
                setPlayUrl(playItem, oneKeyPlayItem.getPlayUrlData());
            }
            break;
            case PlayerConstants.RESOURCES_TYPE_RADIO: {
                RadioPlayItem radioPlayItem = (RadioPlayItem) playItem;
                setPlayUrl(playItem, radioPlayItem.getPlayUrlData());
            }
            break;
            default:
                break;
        }
    }

    /**
     * 根据单曲id请求播放的url地址
     *
     * @param albumPlayItem
     * @param playUrlData
     */
    private void requestPlayUrl(PlayItem albumPlayItem, PlayUrlData playUrlData, OnGetPlayUrlData callback) {
        Log.i("BasePlayControl", "requestPlayUrl: playUrlId=" + albumPlayItem.getPlayUrlId());
        new AudioRequest().getAudioPlayInfo(albumPlayItem.getPlayUrlId(), new HttpCallback<AudioPlayInfo>() {
            @Override
            public void onSuccess(AudioPlayInfo audioPlayInfo) {
                Log.i("BasePlayControl", "getAudioPlayInfo success: " + audioPlayInfo.toString());
                List<AudioFileInfo> playList = audioPlayInfo.getPlayInfoList();
                if (playList == null) {
                    Log.e("BasePlayControl", "playList is null");
                    onError(new ApiException("playList is null"));
                    return;
                }

                if (playList.isEmpty()) {
                    Log.e("BasePlayControl", "playList is empty");
                    onError(new ApiException("playList is empty"));
                    return;
                }

                Log.i("BasePlayControl", "playList size: " + playList.size());
                boolean hasValidUrl = false;

                for (AudioFileInfo info : playList) {
                    Log.i("BasePlayControl", "AudioFileInfo: " + info.toString());
                    String fileType = info.getFileType();
                    String playUrl = info.getPlayUrl();

                    if (TextUtils.isEmpty(playUrl)) {
                        Log.w("BasePlayControl", "Empty playUrl for fileType: " + fileType + ", bitrate: " + info.getBitrate());
                        continue;
                    }

                    hasValidUrl = true;
                    Log.i("BasePlayControl", "Valid playUrl found: " + playUrl + " for fileType: " + fileType + ", bitrate: " + info.getBitrate());

                    if (fileType.startsWith("mp3")) {
                        switch (info.getBitrate()) {
                            case 32:
                                playUrlData.setMp3PlayUrl32(info.getPlayUrl());
                                break;
                            case 64:
                                playUrlData.setMp3PlayUrl64(info.getPlayUrl());
                                break;
                            case 128:
                                playUrlData.setMp3PlayUrl128(info.getPlayUrl());
                                break;
                            case 320:
                                playUrlData.setMp3PlayUrl320(info.getPlayUrl());
                                break;
                        }
                    } else if (fileType.startsWith("aac")) {
                        switch (info.getBitrate()) {
                            case 32:
                                playUrlData.setAacPlayUrl32(info.getPlayUrl());
                                break;
                            case 64:
                                playUrlData.setAacPlayUrl64(info.getPlayUrl());
                                break;
                            case 128:
                                playUrlData.setAacPlayUrl128(info.getPlayUrl());
                                break;
                            case 320:
                                playUrlData.setAacPlayUrl320(info.getPlayUrl());
                                break;
                        }
                    }
                }

                if (!hasValidUrl) {
                    Log.e("BasePlayControl", "No valid playUrl found in any AudioFileInfo");
                    // 尝试降级处理：检查是否有默认播放地址
                    if (!StringUtil.isEmpty(playUrlData.getDefaultPlayUrl())) {
                        Log.i("BasePlayControl", "Using fallback defaultPlayUrl: " + playUrlData.getDefaultPlayUrl());
                        albumPlayItem.setPlayUrl(playUrlData.getDefaultPlayUrl());
                        callback.onDataGet(playUrlData.getDefaultPlayUrl());
                        return;
                    }

                    // 如果没有任何可用URL，尝试重新请求一次
                    Log.w("BasePlayControl", "Retrying getAudioPlayInfo once more...");
                    retryGetPlayUrl(albumPlayItem, playUrlData, callback);
                    return;
                }

                //设置并回调播放地址
                setPlayUrl(albumPlayItem, playUrlData);
                String finalUrl = albumPlayItem.getPlayUrl();
                Log.i("BasePlayControl", "Final selected playUrl: " + finalUrl);

                // 最后检查：如果最终URL仍然为空，尝试降级
                if (TextUtils.isEmpty(finalUrl)) {
                    Log.e("BasePlayControl", "Final URL is still empty after setPlayUrl");
                    if (!StringUtil.isEmpty(playUrlData.getDefaultPlayUrl())) {
                        Log.i("BasePlayControl", "Using fallback defaultPlayUrl as final option: " + playUrlData.getDefaultPlayUrl());
                        albumPlayItem.setPlayUrl(playUrlData.getDefaultPlayUrl());
                        callback.onDataGet(playUrlData.getDefaultPlayUrl());
                    } else {
                        onError(new ApiException("No valid playUrl found after all attempts"));
                    }
                } else {
                    callback.onDataGet(finalUrl);
                }
            }

            @Override
            public void onError(ApiException exception) {
                Log.e("BasePlayControl", "getAudioPlayInfo error: " + exception.toString());
                callback.onDataError(exception);
            }
        });
    }

    private interface OnGetPlayUrlData {
        void onDataGet(String playUrl);

        void onDataError(ApiException e);
    }

    /**
     * 重试获取播放URL
     */
    private void retryGetPlayUrl(PlayItem albumPlayItem, PlayUrlData playUrlData, OnGetPlayUrlData callback) {
        Log.i("BasePlayControl", "retryGetPlayUrl: Attempting retry for playUrlId=" + albumPlayItem.getPlayUrlId());

        // 延迟500ms后重试
        android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                new AudioRequest().getAudioPlayInfo(albumPlayItem.getPlayUrlId(), new HttpCallback<AudioPlayInfo>() {
                    @Override
                    public void onSuccess(AudioPlayInfo audioPlayInfo) {
                        Log.i("BasePlayControl", "retryGetPlayUrl SUCCESS: " + audioPlayInfo.toString());
                        List<AudioFileInfo> playList = audioPlayInfo.getPlayInfoList();
                        if (playList == null || playList.isEmpty()) {
                            Log.e("BasePlayControl", "Retry failed: playList is still null or empty");
                            callback.onDataError(new ApiException("Retry failed: No valid playUrl found"));
                            return;
                        }

                        boolean hasValidUrl = false;
                        for (AudioFileInfo info : playList) {
                            if (!TextUtils.isEmpty(info.getPlayUrl())) {
                                hasValidUrl = true;
                                // 重新填充 playUrlData
                                String fileType = info.getFileType();
                                if (fileType.startsWith("mp3")) {
                                    switch (info.getBitrate()) {
                                        case 32:
                                            playUrlData.setMp3PlayUrl32(info.getPlayUrl());
                                            break;
                                        case 64:
                                            playUrlData.setMp3PlayUrl64(info.getPlayUrl());
                                            break;
                                        case 128:
                                            playUrlData.setMp3PlayUrl128(info.getPlayUrl());
                                            break;
                                        case 320:
                                            playUrlData.setMp3PlayUrl320(info.getPlayUrl());
                                            break;
                                    }
                                } else if (fileType.startsWith("aac")) {
                                    switch (info.getBitrate()) {
                                        case 32:
                                            playUrlData.setAacPlayUrl32(info.getPlayUrl());
                                            break;
                                        case 64:
                                            playUrlData.setAacPlayUrl64(info.getPlayUrl());
                                            break;
                                        case 128:
                                            playUrlData.setAacPlayUrl128(info.getPlayUrl());
                                            break;
                                        case 320:
                                            playUrlData.setAacPlayUrl320(info.getPlayUrl());
                                            break;
                                    }
                                }
                            }
                        }

                        if (hasValidUrl) {
                            setPlayUrl(albumPlayItem, playUrlData);
                            String finalUrl = albumPlayItem.getPlayUrl();
                            Log.i("BasePlayControl", "Retry SUCCESS: Final URL = " + finalUrl);
                            callback.onDataGet(finalUrl);
                        } else {
                            Log.e("BasePlayControl", "Retry failed: Still no valid URLs");
                            callback.onDataError(new ApiException("Retry failed: No valid playUrl found"));
                        }
                    }

                    @Override
                    public void onError(ApiException exception) {
                        Log.e("BasePlayControl", "retryGetPlayUrl ERROR: " + exception.toString());
                        callback.onDataError(exception);
                    }
                });
            }
        }, 500);
    }

    private void setPlayUrl(PlayItem playItem, PlayUrlData playUrlData) {
        Log.i("setPlayUrl", "=== setPlayUrl Debug Info ===");
        Log.i("setPlayUrl", "defaultPlayUrl: " + playUrlData.getDefaultPlayUrl());
        Log.i("setPlayUrl", "mp3PlayUrl32: " + playUrlData.getMp3PlayUrl32());
        Log.i("setPlayUrl", "mp3PlayUrl64: " + playUrlData.getMp3PlayUrl64());
        Log.i("setPlayUrl", "mp3PlayUrl128: " + playUrlData.getMp3PlayUrl128());
        Log.i("setPlayUrl", "mp3PlayUrl320: " + playUrlData.getMp3PlayUrl320());
        Log.i("setPlayUrl", "aacPlayUrl32: " + playUrlData.getAacPlayUrl32());
        Log.i("setPlayUrl", "aacPlayUrl64: " + playUrlData.getAacPlayUrl64());
        Log.i("setPlayUrl", "aacPlayUrl128: " + playUrlData.getAacPlayUrl128());
        Log.i("setPlayUrl", "aacPlayUrl320: " + playUrlData.getAacPlayUrl320());

        if (!StringUtil.isEmpty(playUrlData.getDefaultPlayUrl())) {
            playItem.setPlayUrl(playUrlData.getDefaultPlayUrl());
            Log.i("setPlayUrl", "Using defaultPlayUrl: " + playUrlData.getDefaultPlayUrl());
        }
        String tempUrl = null;
        if (!StringUtil.isEmpty(playUrlData.getAacPlayUrl64())) {
            tempUrl = playUrlData.getAacPlayUrl64();
        }
        int toneQuality = ToneQualityHelper.getInstance().getToneQuality();
        PlayerLogUtil.log(getClass().getSimpleName(), "toneQuality=  " + toneQuality);
        Log.i("setPlayUrl", "Current toneQuality: " + toneQuality);

        if (toneQuality == PlayerConstants.ToneQuality.LOW_TONE_QUALITY) {
            String mp3PlayUrl32 = playUrlData.getMp3PlayUrl32();
            if (!TextUtils.isEmpty(mp3PlayUrl32)) {
                tempUrl = playUrlData.getMp3PlayUrl32();
                Log.i("setPlayUrl", "Selected mp3PlayUrl32: " + tempUrl);
            } else {
                tempUrl = playUrlData.getAacPlayUrl32();
                Log.i("setPlayUrl", "Selected aacPlayUrl32: " + tempUrl);
            }
        } else if (toneQuality == PlayerConstants.ToneQuality.MIDDLE_TONE_QUALITY) {
            String mp3PlayUrl64 = playUrlData.getMp3PlayUrl64();
            Log.i("setPlayUrl", "mp3PlayUrl64:" + mp3PlayUrl64);
            if (!TextUtils.isEmpty(mp3PlayUrl64)) {
                tempUrl = playUrlData.getMp3PlayUrl64();
                Log.i("setPlayUrl", "Selected mp3PlayUrl64: " + tempUrl);
            } else {
                tempUrl = playUrlData.getAacPlayUrl64();
                Log.i("setPlayUrl", "Selected aacPlayUrl64: " + tempUrl);
            }
        } else if (toneQuality == PlayerConstants.ToneQuality.HIGH_TONE_QUALITY) {
            String mp3PlayUrl128 = playUrlData.getMp3PlayUrl128();
            if (!TextUtils.isEmpty(mp3PlayUrl128)) {
                tempUrl = playUrlData.getMp3PlayUrl128();
                Log.i("setPlayUrl", "Selected mp3PlayUrl128: " + tempUrl);
            } else {
                tempUrl = playUrlData.getAacPlayUrl128();
                Log.i("setPlayUrl", "Selected aacPlayUrl128: " + tempUrl);
            }
        } else if (toneQuality == PlayerConstants.ToneQuality.HIGHER_TONE_QUALITY) {
            String mp3PlayUrl320 = playUrlData.getMp3PlayUrl320();
            if (!TextUtils.isEmpty(mp3PlayUrl320)) {
                tempUrl = playUrlData.getMp3PlayUrl320();
                Log.i("setPlayUrl", "Selected mp3PlayUrl320: " + tempUrl);
            } else if (!TextUtils.isEmpty(playUrlData.getMp3PlayUrl128())) {
                tempUrl = playUrlData.getMp3PlayUrl128();
                Log.i("setPlayUrl", "Fallback to mp3PlayUrl128: " + tempUrl);
            } else {
                if (!StringUtil.isEmpty(playUrlData.getAacPlayUrl320())) {
                    tempUrl = playUrlData.getAacPlayUrl320();
                    Log.i("setPlayUrl", "Fallback to aacPlayUrl320: " + tempUrl);
                } else if (!StringUtil.isEmpty(playUrlData.getAacPlayUrl128())) {
                    tempUrl = playUrlData.getAacPlayUrl128();
                    Log.i("setPlayUrl", "Fallback to aacPlayUrl128: " + tempUrl);
                }
            }
        }

        if (!TextUtils.isEmpty(tempUrl)) {
            playItem.setPlayUrl(tempUrl);
            Log.i("setPlayUrl", "Final tempUrl set: " + tempUrl);
        } else {
            Log.e("setPlayUrl", "tempUrl is null - trying fallback options!");

            // 降级策略：按优先级尝试任何可用的URL
            String fallbackUrl = findAnyAvailableUrl(playUrlData);
            if (!TextUtils.isEmpty(fallbackUrl)) {
                playItem.setPlayUrl(fallbackUrl);
                Log.i("setPlayUrl", "Using fallback URL: " + fallbackUrl);
            } else {
                Log.e("setPlayUrl", "NO VALID URL FOUND AT ALL!");
                Log.e("setPlayUrl", "This will cause playback failure!");
            }
        }
        Log.i("setPlayUrl", "=== End setPlayUrl Debug Info ===");
    }

    /**
     * 查找任何可用的播放URL作为降级方案
     */
    private String findAnyAvailableUrl(PlayUrlData playUrlData) {
        // 按优先级顺序尝试所有可能的URL
        String[] urlCandidates = {
            playUrlData.getDefaultPlayUrl(),
            playUrlData.getMp3PlayUrl128(),
            playUrlData.getMp3PlayUrl64(),
            playUrlData.getMp3PlayUrl320(),
            playUrlData.getMp3PlayUrl32(),
            playUrlData.getAacPlayUrl128(),
            playUrlData.getAacPlayUrl64(),
            playUrlData.getAacPlayUrl320(),
            playUrlData.getAacPlayUrl32(),
            playUrlData.getMp3PlayUrl(),
            playUrlData.getM3u8PlayUrl()
        };

        for (String url : urlCandidates) {
            if (!TextUtils.isEmpty(url)) {
                Log.i("setPlayUrl", "Found fallback URL: " + url);
                return url;
            }
        }

        Log.e("setPlayUrl", "No fallback URL available");
        return null;
    }

    /**
     * 紧急播放方案：当所有正常获取URL的方式都失败时的最后尝试
     */
    private void attemptEmergencyPlayback(PlayItem playItem) {
        Log.w("BasePlayControl", "Attempting emergency playback for: " + playItem.getAudioName());

        // 检查 playItem 是否已经有播放URL
        String existingUrl = playItem.getPlayUrl();
        if (!TextUtils.isEmpty(existingUrl)) {
            Log.i("BasePlayControl", "Found existing URL in playItem: " + existingUrl);
            playWithPosition(playItem);
            return;
        }

        // 尝试从 playItem 的其他字段获取可能的播放地址
        if (playItem instanceof AlbumPlayItem) {
            AlbumPlayItem albumItem = (AlbumPlayItem) playItem;
            PlayUrlData urlData = albumItem.getPlayUrlData();
            if (urlData != null) {
                String emergencyUrl = findAnyAvailableUrl(urlData);
                if (!TextUtils.isEmpty(emergencyUrl)) {
                    Log.i("BasePlayControl", "Emergency URL found: " + emergencyUrl);
                    playItem.setPlayUrl(emergencyUrl);
                    playWithPosition(playItem);
                    return;
                }
            }
        }

        // 如果所有尝试都失败，记录详细错误信息并通知失败
        Log.e("BasePlayControl", "Emergency playback failed - no URL available");
        Log.e("BasePlayControl", "PlayItem details: audioId=" + playItem.getAudioId() +
              ", playUrlId=" + playItem.getPlayUrlId() +
              ", type=" + playItem.getType() +
              ", audioName=" + playItem.getAudioName());

        // 提供更具体的错误码
        int errorCode = 1001; // 自定义错误码：播放URL获取失败
        mBasePlayControlListener.onPlayerFailed(playItem, errorCode, errorCode);
    }


    private boolean isLiving(PlayItem playItem) {
        if (playItem == null) {
            return false;
        }
        int type = playItem.getType();
        switch (type) {
            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
            case PlayerConstants.RESOURCES_TYPE_LIVE_STREAM: {
                return mPlayItem.isLiving();
            }
            case PlayerConstants.RESOURCES_TYPE_LIVING: {
                return true;
            }
            default:
                return false;
        }
    }

    @Override
    public void setLoudnessNormalization(int isActive) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setLoudnessNormalization(isActive);
    }

    @Override
    public boolean isPlaying() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return false;
        }
        return mPlayerBinder.isPlaying();
    }

    @Override
    public boolean requestAudioFocus() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return false;
        }
        return mPlayerBinder.requestAudioFocus();
    }

    @Override
    public boolean abandonAudioFocus() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return false;
        }
        return mPlayerBinder.abandonAudioFocus();
    }

    @Override
    public void setCustomAudioFocus(AAudioFocus audioFocus) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setCustomAudioFocus(audioFocus);
    }


    @Override
    public void setAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setAudioFocusListener(iAudioFocusListener);
    }

    @Override
    public long getCurrentPosition() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return -1;
        }
        return mPlayerBinder.getCurrentPosition();
    }

    @Override
    public void setMediaVolume(float leftVolume, float rightVolume) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setMediaVolume(leftVolume, rightVolume);
    }

    @Override
    public int getPlayStatus() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return PlayerConstants.TYPE_PLAYER_IDLE;
        }
        return mPlayerBinder.getPlayStatus();
    }

    @Override
    public void destroy() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.release();
    }

    @Override
    public void setLogInValid() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setLogInValid();
    }

    @Override
    public void setPlayUrl(String url, long position, long duration) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setPlayUrl(url, position, duration);
    }

    @Override
    public void disableAudioFade() {
        mPlayerBinder.disableAudioFade();
    }

    @Override
    public void setHttpProxy(String httpProxy) {
        mPlayerBinder.setHttpProxy(httpProxy);
    }

    @Override
    public void clearHttpProxy() {
        mPlayerBinder.clearHttpProxy();
    }

    @Override
    public void clearDnsCache(boolean clearDnsCache) {
        mPlayerBinder.clearDnsCache(clearDnsCache);
    }

    @Override
    public void setAudioFadeConfig(AudioFadeConfig audioFadeConfig) {
        mPlayerBinder.setAudioFadeConfig(audioFadeConfig);
    }

    @Override
    public boolean isAsyncStartExecuting() {
        return mPlayerBinder.isAsyncStartExecuting();
    }


    public void setAttributesContentType(int content_type) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setAttributesContentType(content_type);
    }


    public void setAttributesUsage(int usage) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setAttributesUsage(usage);
    }

}
