package com.kaolafm.opensdk.player.logic.playcontrol;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.media.AudioRequest;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.api.media.model.AudioPlayInfo;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.core.PlayerService;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.core.model.AAudioFocus;
import com.kaolafm.opensdk.player.core.model.AudioFadeConfig;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.OneKeyPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.PurchaseOneKeyPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayUrlData;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;
import com.kaolafm.opensdk.player.logic.util.ToneQualityHelper;

import java.util.List;


/**
 * <AUTHOR> on 2019/3/18.
 */

public class BasePlayControl implements IPlayControl {

    private PlayerService.PlayerServiceBinder mPlayerBinder;

    protected PlayItem mPlayItem;

    private BasePlayStateListener mBasePlayControlListener;


    public BasePlayControl() {
    }

    @Override
    public void setPlayStateListener(BasePlayStateListener iPlayerStateListener) {
        mBasePlayControlListener = iPlayerStateListener;
    }

    @Override
    public void setBind(PlayerService.PlayerServiceBinder mediaPlayerServiceBind) {
        mPlayerBinder = mediaPlayerServiceBind;
    }

    @Override
    public void play() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        if (isLiving(mPlayItem)) {
            mPlayerBinder.start(mPlayItem.getPlayUrl(), 0, 0, true);
        } else {
            mPlayerBinder.play();
        }
    }

    @Override
    public void pause() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        if (isLiving(mPlayItem)) {
            mPlayerBinder.reset();
        }
        mPlayerBinder.pause();
    }

    @Override
    public void stop() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.stop();
    }

    @Override
    public void reset() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.reset();

    }

    @Override
    public void release() {

    }

    @Override
    public void seek(int position) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }

        mPlayerBinder.seek(position);
    }

    @Override
    public void playTempTask(String url) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.start(url, 0, false, false);
    }

    @Override
    public void start(int type, PlayItem playItem) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            PlayerLogUtil.log(getClass().getSimpleName(), "start", "mPlayerBinder is null");
            return;
        }
        if (PlayerPreconditions.checkNull(playItem)) {
            PlayerLogUtil.log(getClass().getSimpleName(), "start", "playItem is null");
            return;
        }
        mPlayItem = playItem;

        if (isLiving(playItem)) {
            mPlayerBinder.start(playItem.getPlayUrl(), 0, 0, true);
        } else if (isAlbum(playItem)) {
            //加密音频播放测试
//            String testUrl = "https://ytmedia.radio.cn/CCYT%2F2021%2F08%2F02%2F1627892285de0d974ced6fefe3381f20be9d8cd70alc.mp3";
////            String testUrl = Environment.getExternalStorageDirectory().getAbsolutePath() +"/Download/decode_test.mp3";
////            String testUrl = Environment.getExternalStorageDirectory().getAbsolutePath() +"/Download/encode_test.mp3";
//            Log.d("start play", "testUrl:" + testUrl);
////            String testUrl = "/sdcard/Download/encode_test.mp3";
//            playItem.setPlayUrl(testUrl);
//            playWithPosition(playItem);
            // 由于添加了付费的内容播放，专辑内容需要单独请求播放的url地址
            getPlayUrlWithCallBack(playItem, new OnGetPlayUrlData() {
                @Override
                public void onDataGet(String url) {
                    Log.i("BasePlayControl", "=== CALLBACK onDataGet ===");
                    Log.i("BasePlayControl", "getPlayurl result: " + url);
                    if (TextUtils.isEmpty(url)) {
                        Log.e("BasePlayControl", "CALLBACK ERROR: URL is EMPTY!");
                        mBasePlayControlListener.onPlayerFailed(playItem, 404, 404);
                        return;
                    }
                    Log.i("BasePlayControl", "SUCCESS: Setting playUrl and starting playback");
                    playItem.setPlayUrl(url);
                    playWithPosition(playItem);
                }

                @Override
                public void onDataError(ApiException e) {
                    Log.e("BasePlayControl", "=== CALLBACK onDataError ===");
                    Log.e("BasePlayControl", "getPlayUrl error: " + e.toString());
                    Log.e("BasePlayControl", "Error code: " + e.getCode());
                    Log.e("BasePlayControl", "Error message: " + e.getMessage());
                    mBasePlayControlListener.onPlayerFailed(playItem, e.getCode(), e.getCode());
                }
            });
        } else {
            setPlayUrl(playItem);
            playWithPosition(playItem);
        }
    }

    @Override
    public void start(int type, PlayItem playItem, boolean isAutoPlay) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            PlayerLogUtil.log(getClass().getSimpleName(), "start", "mPlayerBinder is null");
            return;
        }
        if (PlayerPreconditions.checkNull(playItem)) {
            PlayerLogUtil.log(getClass().getSimpleName(), "start", "playItem is null");
            return;
        }
        mPlayItem = playItem;

        if (isLiving(playItem)) {
            mPlayerBinder.start(playItem.getPlayUrl(), 0, 0, true);
        } else if (isAlbum(playItem)) {
            //加密音频播放测试
//            String testUrl = "https://ytmedia.radio.cn/CCYT%2F2021%2F08%2F02%2F1627892285de0d974ced6fefe3381f20be9d8cd70alc.mp3";
////            String testUrl = Environment.getExternalStorageDirectory().getAbsolutePath() +"/Download/decode_test.mp3";
////            String testUrl = Environment.getExternalStorageDirectory().getAbsolutePath() +"/Download/encode_test.mp3";
//            Log.i("start play", "testUrl:" + testUrl);
////            String testUrl = "/sdcard/Download/encode_test.mp3";
//            playItem.setPlayUrl(testUrl);
//            playWithPosition(playItem);
            // 由于添加了付费的内容播放，专辑内容需要单独请求播放的url地址
            getPlayUrlWithCallBack(playItem, new OnGetPlayUrlData() {
                @Override
                public void onDataGet(String url) {
                    Log.i("BasePlayControl", "getPlayurl:" + url);
                    if (TextUtils.isEmpty(url)) {
                        mBasePlayControlListener.onPlayerFailed(playItem, 404, 404);
                        return;
                    }
                    playItem.setPlayUrl(url);
                    playWithPosition(playItem, isAutoPlay);
                }

                @Override
                public void onDataError(ApiException e) {
                    Log.i("BasePlayControl", "getPlayUrl error:" + e.toString());
                    mBasePlayControlListener.onPlayerFailed(playItem, e.getCode(), e.getCode());
                }
            });
        } else {
            setPlayUrl(playItem);
            playWithPosition(playItem, isAutoPlay);
        }
    }

    /**
     * 根据播放的记录进行seek播放
     *
     * @param playItem
     */
    private void playWithPosition(PlayItem playItem) {
        PlayerLogUtil.log("play url= " + playItem.getPlayUrl());
        if (playItem.getPosition() > 0) {
            mPlayerBinder.start(playItem.getPlayUrl(), mPlayItem.getDuration(), mPlayItem.getPosition(), false,true);
        } else {
            mPlayerBinder.start(playItem.getPlayUrl(), mPlayItem.getDuration(), false, false,true);
        }
    }

    /**
     * 根据播放的记录进行seek播放
     *
     * @param playItem
     */
    private void playWithPosition(PlayItem playItem, boolean isAutoPlay) {
        PlayerLogUtil.log("play url= " + playItem.getPlayUrl());
        if (playItem.getPosition() > 0) {
            mPlayerBinder.start(playItem.getPlayUrl(), mPlayItem.getDuration(), mPlayItem.getPosition(), false,isAutoPlay);
        } else {
            mPlayerBinder.start(playItem.getPlayUrl(), mPlayItem.getDuration(), false, false,isAutoPlay);
        }
    }

    /**
     * 获取播放地址，并通过回调返回
     *
     * @param playItem
     * @param callback
     */
    private void getPlayUrlWithCallBack(PlayItem playItem, OnGetPlayUrlData callback) {
        Log.i("BasePlayControl", "=== getPlayUrlWithCallBack START ===");
        Log.i("BasePlayControl", "audioId=" + playItem.getAudioId());
        Log.i("BasePlayControl", "playUrlId=" + playItem.getPlayUrlId());
        Log.i("BasePlayControl", "existing playUrl=" + playItem.getPlayUrl());
        Log.i("BasePlayControl", "playItem type=" + playItem.getClass().getSimpleName());

        PlayUrlData playUrlData = null;
        if (playItem instanceof AlbumPlayItem) {
            playUrlData = ((AlbumPlayItem) playItem).getPlayUrlData();
            Log.i("BasePlayControl", "playItem is AlbumPlayItem");
        } else if (playItem instanceof PurchaseOneKeyPlayItem) {
            playUrlData = ((PurchaseOneKeyPlayItem) playItem).getPlayUrlData();
            Log.i("BasePlayControl", "playItem is PurchaseOneKeyPlayItem");
        } else if (playItem instanceof OneKeyPlayItem) {
            playUrlData = ((OneKeyPlayItem) playItem).getPlayUrlData();
            Log.i("BasePlayControl", "playItem is OneKeyPlayItem");
        }

        if (playUrlData != null) {
            Log.i("BasePlayControl", "playUrlData is not null");
        } else {
            Log.e("BasePlayControl", "playUrlData is NULL!");
        }

        Log.i("BasePlayControl", "About to call requestPlayUrl...");
        requestPlayUrl(playItem, playUrlData, callback);
    }

    private boolean isAlbum(PlayItem playItem) {
        int type = playItem.getType();
        return type == PlayerConstants.RESOURCES_TYPE_ALBUM//专辑类型的
                || type == PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE//订阅的一键播放
                || type == PlayerConstants.RESOURCES_TYPE_PURCHASE_ONE_KEY_LISTENER_TYPE//已购的一键播放
                ;
    }


    /**
     * 根据音质选择不同的url
     */
    private void setPlayUrl(PlayItem playItem) {
        if (playItem == null) {
            PlayerLogUtil.log(getClass().getSimpleName(), "setPlayUrl", "playItem is null");
            return;
        }
        int type = playItem.getType();
        switch (type) {
//            case PlayerConstants.RESOURCES_TYPE_ALBUM: {
//                AlbumPlayItem albumPlayItem = (AlbumPlayItem) playItem;
//                setPlayUrl(playItem, albumPlayItem.getPlayUrlData());
//            }
//            break;
            case PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE: {
                OneKeyPlayItem oneKeyPlayItem = (OneKeyPlayItem) playItem;
                setPlayUrl(playItem, oneKeyPlayItem.getPlayUrlData());
            }
            break;
            case PlayerConstants.RESOURCES_TYPE_RADIO: {
                RadioPlayItem radioPlayItem = (RadioPlayItem) playItem;
                setPlayUrl(playItem, radioPlayItem.getPlayUrlData());
            }
            break;
            default:
                break;
        }
    }

    /**
     * 根据单曲id请求播放的url地址
     *
     * @param albumPlayItem
     * @param playUrlData
     */
    private void requestPlayUrl(PlayItem albumPlayItem, PlayUrlData playUrlData, OnGetPlayUrlData callback) {
        Log.i("BasePlayControl", "requestPlayUrl: playUrlId=" + albumPlayItem.getPlayUrlId());
        new AudioRequest().getAudioPlayInfo(albumPlayItem.getPlayUrlId(), new HttpCallback<AudioPlayInfo>() {
            @Override
            public void onSuccess(AudioPlayInfo audioPlayInfo) {
                Log.i("BasePlayControl", "getAudioPlayInfo success: " + audioPlayInfo.toString());
                List<AudioFileInfo> playList = audioPlayInfo.getPlayInfoList();
                if (playList == null) {
                    Log.e("BasePlayControl", "CRITICAL: playList is NULL in response!");
                    onError(new ApiException("playList is null"));
                    return;
                }

                if (playList.isEmpty()) {
                    Log.e("BasePlayControl", "CRITICAL: playList is EMPTY in response!");
                    onError(new ApiException("playList is empty"));
                    return;
                }

                Log.i("BasePlayControl", "playList size: " + playList.size());
                boolean hasValidUrl = false;

                for (AudioFileInfo info : playList) {
                    Log.i("BasePlayControl", "AudioFileInfo: " + info.toString());
                    String fileType = info.getFileType();
                    String playUrl = info.getPlayUrl();

                    if (TextUtils.isEmpty(playUrl)) {
                        Log.w("BasePlayControl", "Empty playUrl for fileType: " + fileType + ", bitrate: " + info.getBitrate());
                        continue;
                    }

                    hasValidUrl = true;
                    Log.i("BasePlayControl", "Valid playUrl found: " + playUrl + " for fileType: " + fileType + ", bitrate: " + info.getBitrate());

                    if (fileType.startsWith("mp3")) {
                        switch (info.getBitrate()) {
                            case 32:
                                playUrlData.setMp3PlayUrl32(info.getPlayUrl());
                                break;
                            case 64:
                                playUrlData.setMp3PlayUrl64(info.getPlayUrl());
                                break;
                            case 128:
                                playUrlData.setMp3PlayUrl128(info.getPlayUrl());
                                break;
                            case 320:
                                playUrlData.setMp3PlayUrl320(info.getPlayUrl());
                                break;
                        }
                    } else if (fileType.startsWith("aac")) {
                        switch (info.getBitrate()) {
                            case 32:
                                playUrlData.setAacPlayUrl32(info.getPlayUrl());
                                break;
                            case 64:
                                playUrlData.setAacPlayUrl64(info.getPlayUrl());
                                break;
                            case 128:
                                playUrlData.setAacPlayUrl128(info.getPlayUrl());
                                break;
                            case 320:
                                playUrlData.setAacPlayUrl320(info.getPlayUrl());
                                break;
                        }
                    }
                }

                if (!hasValidUrl) {
                    Log.e("BasePlayControl", "CRITICAL: No valid playUrl found in any AudioFileInfo!");
                    Log.e("BasePlayControl", "All AudioFileInfo entries have empty playUrl fields");
                    onError(new ApiException("No valid playUrl found"));
                    return;
                }

                //设置并回调播放地址
                setPlayUrl(albumPlayItem, playUrlData);
                String finalUrl = albumPlayItem.getPlayUrl();
                Log.i("BasePlayControl", "Final selected playUrl: " + finalUrl);

                if (TextUtils.isEmpty(finalUrl)) {
                    Log.e("BasePlayControl", "CRITICAL: Final URL is EMPTY after setPlayUrl!");
                    onError(new ApiException("Final URL is empty after setPlayUrl"));
                } else {
                    Log.i("BasePlayControl", "SUCCESS: Returning final URL: " + finalUrl);
                    callback.onDataGet(finalUrl);
                }
            }

            @Override
            public void onError(ApiException exception) {
                Log.e("BasePlayControl", "getAudioPlayInfo error: " + exception.toString());
                callback.onDataError(exception);
            }
        });
    }

    private interface OnGetPlayUrlData {
        void onDataGet(String playUrl);

        void onDataError(ApiException e);
    }



    private void setPlayUrl(PlayItem playItem, PlayUrlData playUrlData) {
        Log.i("setPlayUrl", "=== setPlayUrl Debug Info ===");
        Log.i("setPlayUrl", "defaultPlayUrl: " + playUrlData.getDefaultPlayUrl());
        Log.i("setPlayUrl", "mp3PlayUrl32: " + playUrlData.getMp3PlayUrl32());
        Log.i("setPlayUrl", "mp3PlayUrl64: " + playUrlData.getMp3PlayUrl64());
        Log.i("setPlayUrl", "mp3PlayUrl128: " + playUrlData.getMp3PlayUrl128());
        Log.i("setPlayUrl", "mp3PlayUrl320: " + playUrlData.getMp3PlayUrl320());
        Log.i("setPlayUrl", "aacPlayUrl32: " + playUrlData.getAacPlayUrl32());
        Log.i("setPlayUrl", "aacPlayUrl64: " + playUrlData.getAacPlayUrl64());
        Log.i("setPlayUrl", "aacPlayUrl128: " + playUrlData.getAacPlayUrl128());
        Log.i("setPlayUrl", "aacPlayUrl320: " + playUrlData.getAacPlayUrl320());

        if (!StringUtil.isEmpty(playUrlData.getDefaultPlayUrl())) {
            playItem.setPlayUrl(playUrlData.getDefaultPlayUrl());
            Log.i("setPlayUrl", "Using defaultPlayUrl: " + playUrlData.getDefaultPlayUrl());
        }
        String tempUrl = null;
        if (!StringUtil.isEmpty(playUrlData.getAacPlayUrl64())) {
            tempUrl = playUrlData.getAacPlayUrl64();
        }
        int toneQuality = ToneQualityHelper.getInstance().getToneQuality();
        PlayerLogUtil.log(getClass().getSimpleName(), "toneQuality=  " + toneQuality);
        Log.i("setPlayUrl", "Current toneQuality: " + toneQuality);

        if (toneQuality == PlayerConstants.ToneQuality.LOW_TONE_QUALITY) {
            String mp3PlayUrl32 = playUrlData.getMp3PlayUrl32();
            if (!TextUtils.isEmpty(mp3PlayUrl32)) {
                tempUrl = playUrlData.getMp3PlayUrl32();
                Log.i("setPlayUrl", "Selected mp3PlayUrl32: " + tempUrl);
            } else {
                tempUrl = playUrlData.getAacPlayUrl32();
                Log.i("setPlayUrl", "Selected aacPlayUrl32: " + tempUrl);
            }
        } else if (toneQuality == PlayerConstants.ToneQuality.MIDDLE_TONE_QUALITY) {
            String mp3PlayUrl64 = playUrlData.getMp3PlayUrl64();
            Log.i("setPlayUrl", "mp3PlayUrl64:" + mp3PlayUrl64);
            if (!TextUtils.isEmpty(mp3PlayUrl64)) {
                tempUrl = playUrlData.getMp3PlayUrl64();
                Log.i("setPlayUrl", "Selected mp3PlayUrl64: " + tempUrl);
            } else {
                tempUrl = playUrlData.getAacPlayUrl64();
                Log.i("setPlayUrl", "Selected aacPlayUrl64: " + tempUrl);
            }
        } else if (toneQuality == PlayerConstants.ToneQuality.HIGH_TONE_QUALITY) {
            String mp3PlayUrl128 = playUrlData.getMp3PlayUrl128();
            if (!TextUtils.isEmpty(mp3PlayUrl128)) {
                tempUrl = playUrlData.getMp3PlayUrl128();
                Log.i("setPlayUrl", "Selected mp3PlayUrl128: " + tempUrl);
            } else {
                tempUrl = playUrlData.getAacPlayUrl128();
                Log.i("setPlayUrl", "Selected aacPlayUrl128: " + tempUrl);
            }
        } else if (toneQuality == PlayerConstants.ToneQuality.HIGHER_TONE_QUALITY) {
            String mp3PlayUrl320 = playUrlData.getMp3PlayUrl320();
            if (!TextUtils.isEmpty(mp3PlayUrl320)) {
                tempUrl = playUrlData.getMp3PlayUrl320();
                Log.i("setPlayUrl", "Selected mp3PlayUrl320: " + tempUrl);
            } else if (!TextUtils.isEmpty(playUrlData.getMp3PlayUrl128())) {
                tempUrl = playUrlData.getMp3PlayUrl128();
                Log.i("setPlayUrl", "Fallback to mp3PlayUrl128: " + tempUrl);
            } else {
                if (!StringUtil.isEmpty(playUrlData.getAacPlayUrl320())) {
                    tempUrl = playUrlData.getAacPlayUrl320();
                    Log.i("setPlayUrl", "Fallback to aacPlayUrl320: " + tempUrl);
                } else if (!StringUtil.isEmpty(playUrlData.getAacPlayUrl128())) {
                    tempUrl = playUrlData.getAacPlayUrl128();
                    Log.i("setPlayUrl", "Fallback to aacPlayUrl128: " + tempUrl);
                }
            }
        }

        if (!TextUtils.isEmpty(tempUrl)) {
            playItem.setPlayUrl(tempUrl);
            Log.i("setPlayUrl", "Final tempUrl set: " + tempUrl);
        } else {
            Log.e("setPlayUrl", "tempUrl is null - trying fallback options!");

            // 降级策略：按优先级尝试任何可用的URL
            String fallbackUrl = findAnyAvailableUrl(playUrlData);
            if (!TextUtils.isEmpty(fallbackUrl)) {
                playItem.setPlayUrl(fallbackUrl);
                Log.i("setPlayUrl", "Using fallback URL: " + fallbackUrl);
            } else {
                Log.e("setPlayUrl", "NO VALID URL FOUND AT ALL!");
                Log.e("setPlayUrl", "This will cause playback failure!");
            }
        }
        Log.i("setPlayUrl", "=== End setPlayUrl Debug Info ===");
    }

    /**
     * 查找任何可用的播放URL作为降级方案
     */
    private String findAnyAvailableUrl(PlayUrlData playUrlData) {
        // 按优先级顺序尝试所有可能的URL
        String[] urlCandidates = {
            playUrlData.getDefaultPlayUrl(),
            playUrlData.getMp3PlayUrl128(),
            playUrlData.getMp3PlayUrl64(),
            playUrlData.getMp3PlayUrl320(),
            playUrlData.getMp3PlayUrl32(),
            playUrlData.getAacPlayUrl128(),
            playUrlData.getAacPlayUrl64(),
            playUrlData.getAacPlayUrl320(),
            playUrlData.getAacPlayUrl32(),
            playUrlData.getMp3PlayUrl(),
            playUrlData.getM3u8PlayUrl()
        };

        for (String url : urlCandidates) {
            if (!TextUtils.isEmpty(url)) {
                Log.i("setPlayUrl", "Found fallback URL: " + url);
                return url;
            }
        }

        Log.e("setPlayUrl", "No fallback URL available");
        return null;
    }




    private boolean isLiving(PlayItem playItem) {
        if (playItem == null) {
            return false;
        }
        int type = playItem.getType();
        switch (type) {
            case PlayerConstants.RESOURCES_TYPE_BROADCAST:
            case PlayerConstants.RESOURCES_TYPE_LIVE_STREAM: {
                return mPlayItem.isLiving();
            }
            case PlayerConstants.RESOURCES_TYPE_LIVING: {
                return true;
            }
            default:
                return false;
        }
    }

    @Override
    public void setLoudnessNormalization(int isActive) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setLoudnessNormalization(isActive);
    }

    @Override
    public boolean isPlaying() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return false;
        }
        return mPlayerBinder.isPlaying();
    }

    @Override
    public boolean requestAudioFocus() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return false;
        }
        return mPlayerBinder.requestAudioFocus();
    }

    @Override
    public boolean abandonAudioFocus() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return false;
        }
        return mPlayerBinder.abandonAudioFocus();
    }

    @Override
    public void setCustomAudioFocus(AAudioFocus audioFocus) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setCustomAudioFocus(audioFocus);
    }


    @Override
    public void setAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setAudioFocusListener(iAudioFocusListener);
    }

    @Override
    public long getCurrentPosition() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return -1;
        }
        return mPlayerBinder.getCurrentPosition();
    }

    @Override
    public void setMediaVolume(float leftVolume, float rightVolume) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setMediaVolume(leftVolume, rightVolume);
    }

    @Override
    public int getPlayStatus() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return PlayerConstants.TYPE_PLAYER_IDLE;
        }
        return mPlayerBinder.getPlayStatus();
    }

    @Override
    public void destroy() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.release();
    }

    @Override
    public void setLogInValid() {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setLogInValid();
    }

    @Override
    public void setPlayUrl(String url, long position, long duration) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setPlayUrl(url, position, duration);
    }

    @Override
    public void disableAudioFade() {
        mPlayerBinder.disableAudioFade();
    }

    @Override
    public void setHttpProxy(String httpProxy) {
        mPlayerBinder.setHttpProxy(httpProxy);
    }

    @Override
    public void clearHttpProxy() {
        mPlayerBinder.clearHttpProxy();
    }

    @Override
    public void clearDnsCache(boolean clearDnsCache) {
        mPlayerBinder.clearDnsCache(clearDnsCache);
    }

    @Override
    public void setAudioFadeConfig(AudioFadeConfig audioFadeConfig) {
        mPlayerBinder.setAudioFadeConfig(audioFadeConfig);
    }

    @Override
    public boolean isAsyncStartExecuting() {
        return mPlayerBinder.isAsyncStartExecuting();
    }


    public void setAttributesContentType(int content_type) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setAttributesContentType(content_type);
    }


    public void setAttributesUsage(int usage) {
        if (PlayerPreconditions.checkNull(mPlayerBinder)) {
            return;
        }
        mPlayerBinder.setAttributesUsage(usage);
    }

}
