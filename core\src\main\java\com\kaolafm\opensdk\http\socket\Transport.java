package com.kaolafm.opensdk.http.socket;


import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.http.error.SocketEngineIOException;
import com.kaolafm.opensdk.http.error.UTF8Exception;
import com.kaolafm.opensdk.http.socket.parser.Parser;

import java.net.Socket;
import java.util.Map;

import okhttp3.Call;
import okhttp3.WebSocket;

/**
 * 传输协议
 *
 * <AUTHOR>
 */
public abstract class Transport extends Emitter {

    public static final String EVENT_REQUEST_HEADERS = "requestHeaders";
    public static final String EVENT_RESPONSE_HEADERS = "responseHeaders";


    public boolean writable;
    public String name;
    public Map<String, String> query;

    protected boolean secure;
    protected boolean timestampRequests;
    protected int port;
    protected String path;
    protected String hostname;
    protected String timestampParam;
    protected Socket socket;
    protected ReadyState readyState;
    protected WebSocket.Factory webSocketFactory;
    protected Call.Factory callFactory;
    protected Map<String, String> headers;

    public Transport(Options opts) {
        this.path = opts.path;
        this.hostname = opts.hostname;
        this.port = opts.port;
        this.secure = opts.secure;
        this.query = opts.query;
        this.timestampParam = opts.timestampParam;
        this.timestampRequests = opts.timestampRequests;
        this.socket = opts.socket;
        this.webSocketFactory = opts.webSocketFactory;
        this.callFactory = opts.callFactory;
        this.headers = opts.headers;
    }

    protected Transport onError(String msg, Exception desc) {
        Exception err = new SocketEngineIOException(msg, desc);
        this.emit(SocketEvent.EVENT_ERROR, err);
        return this;
    }

    public Transport open() {
        EventThread.exec(() -> {
            if (readyState == ReadyState.CLOSED || readyState == null) {
                readyState = ReadyState.OPENING;
                doOpen();
            }
        });
        return this;
    }

    /**
     * 关闭通道
     * @return
     */
    public Transport close() {
        EventThread.exec(() -> {
            if (readyState == ReadyState.OPENING || readyState == ReadyState.OPEN) {
                doClose();
                onClose();
            }
        });
        return this;
    }

    /**
     * 发送数据包
     * @param packets
     */
    public void send(final Packet[] packets) {
        EventThread.exec(() -> {
            if (readyState == ReadyState.OPEN) {
                try {
                    write(packets);
                } catch (UTF8Exception err) {
                    throw new RuntimeException(err);
                }
            } else {
                throw new RuntimeException("Transport not open");
            }
        });
    }

    /**
     * 切换到开启状态，并回调开启监听
     */
    protected void onOpen() {
        this.readyState = ReadyState.OPEN;
        this.writable = true;
        this.emit(SocketEvent.EVENT_OPEN);
    }

    protected void onData(String data) {
        this.onPacket(Parser.decodePacket(data));
    }

    protected void onData(byte[] data) {
        this.onPacket(Parser.decodePacket(data));
    }

    protected void onPacket(Packet packet) {
        Logging.d("packet what get from websocket is %s", packet);
        this.emit(SocketEvent.EVENT_PACKET, packet);
    }

    /**
     * 切换到关闭状态，并回调关闭监听
     */
    protected void onClose() {
        this.readyState = ReadyState.CLOSED;
        this.emit(SocketEvent.EVENT_CLOSE);
    }

    /**
     * 写入数据
     * @param packets 数据包
     * @throws UTF8Exception
     */
    abstract protected void write(Packet[] packets) throws UTF8Exception;

    /**
     * 开启长连接通道
     */
    abstract protected void doOpen();

    /**
     * 执行具体关闭长连接通道
     */
    abstract protected void doClose();


    public static class Options {

        public String hostname;
        public String path;
        public String timestampParam;
        public boolean secure;
        public boolean timestampRequests;
        public int port = -1;
        public int policyPort = -1;
        public Map<String, String> query;
        protected Socket socket;
        public WebSocket.Factory webSocketFactory;
        public Call.Factory callFactory;
        public Map<String, String> headers;
    }
}
