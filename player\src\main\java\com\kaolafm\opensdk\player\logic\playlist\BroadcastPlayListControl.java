package com.kaolafm.opensdk.player.logic.playlist;

import android.util.Log;

import com.google.gson.Gson;
import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.broadcast.BroadcastDetails;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.model.PlayItemConstants;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.innerinterface.IDataListCallback;
import com.kaolafm.opensdk.player.logic.playlist.innerinterface.ISonPlayList;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.net.MalformedURLException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> on 2019/3/19.
 */

public class BroadcastPlayListControl extends BasePlayListControl implements ISonPlayList {
    private static final String TAG = "BroadcastPlayListControl";

    private ArrayList<PlayItem> mSongPlayItemArrayList;
    private boolean isPlaySongList = false;
    private int mSongListPosition = -1;

    private BroadcastRequest mBroadcastRequest;

    public BroadcastPlayListControl() {
        mSongPlayItemArrayList = new ArrayList<>();
        mBroadcastRequest = new BroadcastRequest();
    }

    @Override
    public void initPlayList(PlayerBuilder playerBuilder, IPlayListGetListener iPlayListGetListener) {
        super.initPlayList(playerBuilder, iPlayListGetListener);
        initBroadcastInfo(iPlayListGetListener);
    }

    @Override
    public void loadPrePage(IPlayListGetListener iPlayListGetListener) {
    }


    @Override
    public void loadNextPage(IPlayListGetListener iPlayListGetListener) {
    }

    private void initBroadcastInfo(IPlayListGetListener iPlayListGetListener) {
        long albumId = string2Long(mPlayerBuilder.getId());
        PlayerLogUtil.log(TAG, "initBroadcastInfo","broadcast id =" + albumId);

        mBroadcastRequest.getBroadcastDetails(albumId, new HttpCallback<BroadcastDetails>() {
            @Override
            public void onSuccess(BroadcastDetails broadcastDetails) {
                PlayerLogUtil.log(TAG, "initBroadcastInfo","success");
                if (PlayerPreconditions.checkNull(broadcastDetails)) {
                    PlayerLogUtil.log(TAG, "initBroadcastInfo","success, list is empty");
                    notifyPlayListGetError(iPlayListGetListener, -1);
                    return;
                }
                mPlaylistInfo.setBroadcastChannel(broadcastDetails.getFreq());
                mPlaylistInfo.setProgramEnable(broadcastDetails.getProgramEnable());

                loadPlayList(albumId, null, new IDataListCallback<List<ProgramDetails>>() {
                    @Override
                    public void success(List<ProgramDetails> programDetails) {
                        PlayerLogUtil.log(TAG, "initBroadcastInfo","get play list success");
                        ArrayList<PlayItem> playItemArrayList = PlayListUtils.programDetailsToPlayItem(programDetails, mPlaylistInfo.getBroadcastChannel());
                        if (ListUtil.isEmpty(playItemArrayList)) {
                            PlayerLogUtil.log(TAG, "initBroadcastInfo","get play list success, list is empty");
                            notifyPlayListGetError(iPlayListGetListener, -1);
                            return;
                        }
                        release();
                        updatePlayListContent(playItemArrayList, iPlayListGetListener);
                        updatePlayListInfo((BroadcastPlayItem) playItemArrayList.get(mPosition));
                    }

                    @Override
                    public void error(int code) {
                        PlayerLogUtil.log(TAG, "initBroadcastInfo","get play list error");
                        notifyPlayListGetError(iPlayListGetListener, code);
                    }
                });
            }

            @Override
            public void onError(ApiException e) {
                PlayerLogUtil.log(TAG, "initBroadcastInfo","error");
                notifyPlayListGetError(iPlayListGetListener, e.getCode());
            }
        });
    }

    /**
     * 加载信息
     *
     * @param albumId
     * @param data
     * @param iDataListCallback
     */
    private void loadPlayList(long albumId, String data, IDataListCallback<List<ProgramDetails>> iDataListCallback) {
        mBroadcastRequest.getBroadcastProgramList(albumId, data, new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> programDetails) {
                if (ListUtil.isEmpty(programDetails)) {
                    if (iDataListCallback != null) {
                        iDataListCallback.error(-1);
                    }
                    return;
                }
                if (iDataListCallback != null) {
                    iDataListCallback.success(programDetails);
                }
            }

            @Override
            public void onError(ApiException e) {
                if (iDataListCallback != null) {
                    iDataListCallback.error(e.getCode());
                }
            }
        });
    }

    @Override
    public void getPrePlayItem(IPlayListGetListener iPlayListGetListener) {
        if (isPlaySongList) {
            if (mSongListPosition > 0) {
                PlayerLogUtil.log(TAG, "getPrePlayItem","is son play list");
                notifyPlayListGet(iPlayListGetListener, mSongPlayItemArrayList.get(--mSongListPosition), null);
            }
        } else {
            if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
                notifyPlayListGetError(iPlayListGetListener, -1);
                return;
            }
            if (mPosition - 1 >= mPlayItemArrayList.size()) {
                notifyPlayListGetError(iPlayListGetListener, -1);
                return;
            }
            if (mPosition - 1 < 0) {
                PlayerLogUtil.log(TAG, "getPrePlayItem","current page is start");
                if (hasPrePage()) {
                    PlayerLogUtil.log(TAG, "getPrePlayItem","has pre page");
                    loadPrePage(iPlayListGetListener);
                } else {
                    notifyPlayListGetError(iPlayListGetListener, -1);
                }
                return;
            }
            PlayerLogUtil.log(TAG, "getPrePlayItem","position = " + mPosition);
            BroadcastPlayItem playItem = (BroadcastPlayItem) (mPlayItemArrayList.get(mPosition - 1));
            if (PlayerPreconditions.checkNull(playItem)) {
                notifyPlayListGetError(iPlayListGetListener, -1);
                return;
            }
            if (playItem.getStatus() == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                PlayerLogUtil.log(TAG, "getPrePlayItem","is play back");
                // 检查回放节目是否有有效的播放URL，如果没有则需要重新获取
                if (StringUtil.isEmpty(playItem.getPlayUrl())) {
                    PlayerLogUtil.log(TAG, "getPrePlayItem", "播放项需要重新获取 url");
                    setAutoPlay(mPlayItemArrayList.get(mPosition), playItem);
                    initLiving(playItem, iPlayListGetListener);
                } else {
                    notifyPlayListGet(iPlayListGetListener, mPlayItemArrayList.get(mPosition - 1), null);
                }
            } else {
                long serverTime = DateUtil.getServerTime();
                // 统一时间检查逻辑：允许1分钟的时间误差
                if(playItem.getTimeInfoData().getStartTime() - serverTime > 60 * 1000){
                    PlayerLogUtil.log(TAG, "getPrePlayItem", "start time error ,playItem startTime=" + playItem.getTimeInfoData().getStartTime() + " ,serverTime=" + serverTime);
                    notifyPlayListGetError(iPlayListGetListener, 404);
                    return;
                }
                setAutoPlay(mPlayItemArrayList.get(mPosition), playItem);
                initLiving(playItem, iPlayListGetListener);
            }
        }
    }

    @Override
    public void getNextPlayItem(IPlayListGetListener iPlayListGetListener) {
        if (isPlaySongList) {
            if (mSongListPosition < mSongPlayItemArrayList.size() - 1) {
                PlayerLogUtil.log(TAG, "getNextPlayItem","is son play list");
                notifyPlayListGet(iPlayListGetListener, mSongPlayItemArrayList.get(++mSongListPosition), null);
            }
        } else {
            if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
                notifyPlayListGetError(iPlayListGetListener, -1);
                return;
            }
            if (mPosition + 1 < 0) {
                notifyPlayListGetError(iPlayListGetListener, -1);
                return;
            }
            if (mPosition + 1 >= mPlayItemArrayList.size()) {
                PlayerLogUtil.log(TAG, "getNextPlayItem","current page is end");
                if (hasNextPage()) {
                    PlayerLogUtil.log(TAG, "getNextPlayItem","has next page");
                    loadNextPage(iPlayListGetListener);
                } else {
                    notifyPlayListGetError(iPlayListGetListener, -1);
                }
                return;
            }
            PlayerLogUtil.log(TAG, "getNextPlayItem","position = " + mPosition);
            Gson gson = new Gson();
            PlayerLogUtil.log(TAG, "getNextPlayItem", "当前播放playItem = " + gson.toJson(mPlayItemArrayList.get(mPosition)));
            BroadcastPlayItem playItem = (BroadcastPlayItem) (mPlayItemArrayList.get(mPosition + 1));
            PlayerLogUtil.log(TAG, "getNextPlayItem", "下一个播放playItem = " + gson.toJson(playItem));
            if (PlayerPreconditions.checkNull(playItem)) {
                notifyPlayListGetError(iPlayListGetListener, -1);
                return;
            }
            if (playItem.getStatus() == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                PlayerLogUtil.log(TAG, "getNextPlayItem","is play back");
                notifyPlayListGet(iPlayListGetListener, mPlayItemArrayList.get(mPosition + 1), null);
            } else {
                long serverTime = DateUtil.getServerTime();
                //因广播直播自动切换过程中可能会出现startTime大于serverTime，导致无法正常切换到下一节目的问题，
                // 现将原先 playItem.getTimeInfoData().getStartTime() > serverTime 的判断
                // 修改为按照分钟进行对比，如果相差不足1分钟，则认为可以播放下一节目
                if (playItem.getTimeInfoData().getStartTime() - serverTime > 60 * 1000) {
                    PlayerLogUtil.log(TAG, "getNextPlayItem", "start time error ,playItem startTime=" + playItem.getTimeInfoData().getStartTime() + " ,serverTime=" + serverTime);
                    String playUrl = playItem.getPlayUrl();
                    Long expirationTime = checkUrlExpiration(playUrl);
                    PlayerLogUtil.log(TAG, "checkUrlExpiration", "expirationTime" + expirationTime);
                    PlayerLogUtil.log(TAG, "checkUrlExpiration", "currentTime" + System.currentTimeMillis() / 1000);
                    if (expirationTime != null) {
                        long currentTime = System.currentTimeMillis() / 1000;
                        if (currentTime >= expirationTime) {
                            PlayerLogUtil.log(TAG, "getNextPlayItem", "URL已过期，重新获取播放地址");
                            setAutoPlay(mPlayItemArrayList.get(mPosition), playItem);
                            initLiving(playItem, iPlayListGetListener);
                            return;
                        }
                    }
                    // URL没有time参数或解析失败，这不是错误，继续正常播放流程
                    PlayerLogUtil.log(TAG, "getNextPlayItem", "URL没有time参数或解析失败，但时间差超过1分钟，报时间错误");
                    notifyPlayListGetError(iPlayListGetListener, 404);
                    PlayerLogUtil.log(TAG, "getNextPlayItem", "校验时间出错：playitem begintime = " + playItem.getTimeInfoData().getStartTime() + " ,servertime = " + serverTime);
                    return;
                }
                setAutoPlay(mPlayItemArrayList.get(mPosition), playItem);
                initLiving(playItem, iPlayListGetListener);
            }
        }
    }

    private void setAutoPlay(PlayItem prePlayItem, PlayItem playItem) {
        if (prePlayItem == null || playItem == null) {
            return;
        }
        String autoPlay = prePlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
        if (!StringUtil.isEmpty(autoPlay)) {
            prePlayItem.removeMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
            prePlayItem.setPosition(0);
            playItem.addMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY, autoPlay);
        }
    }

    private void initLiving(PlayItem playItem, IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(TAG, "initLiving","get living info id: " + playItem.getAudioId());
        mBroadcastRequest.getBroadcastProgramDetails(playItem.getAudioId(), new HttpCallback<ProgramDetails>() {
            @Override
            public void onSuccess(ProgramDetails programDetails) {
                PlayerLogUtil.log(TAG, "initLiving","get detail success");
                if (PlayerPreconditions.checkNull(programDetails)) {
                    PlayerLogUtil.log(TAG, "initLiving","programDetails is null");
                    notifyPlayListGetError(iPlayListGetListener, -1);
                    return;
                }
                PlayerLogUtil.log(TAG, "initLiving","get detal success status: " + programDetails.getStatus());
                BroadcastPlayItem broadcastPlayItem = (BroadcastPlayItem) playItem;

                // 根据服务器返回的节目状态设置正确的播放状态和URL
                int programStatus = programDetails.getStatus();
                if (programStatus == PlayerConstants.BROADCAST_STATUS_LIVING ||
                    programStatus == PlayerConstants.BROADCAST_STATUS_DEFAULT) {
                    // 直播节目
                    broadcastPlayItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
                    String liveUrl = programDetails.getPlayUrl();
                    PlayerLogUtil.log(TAG, "initLiving", "success, old url = " + playItem.getPlayUrl() + " , new live url= " + liveUrl);
                    if (StringUtil.isEmpty(liveUrl)) {
                        PlayerLogUtil.log(TAG, "initLiving", "warning: liveUrl is empty for living program");
                    }
                    broadcastPlayItem.setPlayUrl(liveUrl);
                } else if (programStatus == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                    // 回放节目
                    broadcastPlayItem.setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
                    String backUrl = programDetails.getBackLiveUrl();
                    PlayerLogUtil.log(TAG, "initLiving", "success, old url = " + playItem.getPlayUrl() + " , new back url= " + backUrl);
                    if (StringUtil.isEmpty(backUrl)) {
                        PlayerLogUtil.log(TAG, "initLiving", "warning: backLiveUrl is empty for playback program");
                    }
                    broadcastPlayItem.setPlayUrl(backUrl);
                } else {
                    // 其他状态（如未开播等）
                    broadcastPlayItem.setStatus(programStatus);
                    broadcastPlayItem.setPlayUrl(null);
                    PlayerLogUtil.log(TAG, "initLiving", "success, program status = " + programStatus + ", set url to null");
                }

                broadcastPlayItem.getTimeInfoData().setStartTime(programDetails.getStartTime());
                broadcastPlayItem.getTimeInfoData().setFinishTime(programDetails.getFinishTime());
                broadcastPlayItem.getTimeInfoData().setBeginTime(programDetails.getBeginTime());
                broadcastPlayItem.getTimeInfoData().setEndTime(programDetails.getEndTime());
                broadcastPlayItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
                notifyPlayListGet(iPlayListGetListener, playItem, null);
            }

            @Override
            public void onError(ApiException exception) {
                PlayerLogUtil.log(TAG, "initLiving","error: " + exception.getMessage());
                notifyPlayListGetError(iPlayListGetListener, -1);
            }
        });
    }

    /**
     * 检查URL是否过期
     * @param url 播放URL
     * @return 过期时间戳，如果无法解析则返回null
     */
    private Long checkUrlExpiration(String url) {
        String hexTime = getTimeParameter(url);
        if (hexTime == null) {
            Log.i("BroadcastPlayListContrl", "No time parameter found");
            return null;
        }
        Long timestamp = safeHexToTimestamp(hexTime);
        if (timestamp == null) return null;
        Log.i("BroadcastPlayListContrl", "Expiration UTC Time: " + formatUnixTime(timestamp));
        return timestamp;
    }

    /**
     * 获取URL中的time参数值（十六进制字符串）
     */
    private String getTimeParameter(String url) {
        try {
            // 先处理Unicode转义字符（如\u003d转=，\u0026转&）
            String decodedUrl = unescapeUnicode(url);
            URL parsedUrl = new URL(decodedUrl);

            // 获取查询参数部分
            String query = parsedUrl.getQuery();
            if (query == null) {
                Log.i("BroadcastPlayListContrl", "No query parameters in URL");
                return null;
            }

            // 分割参数对
            String[] params = query.split("&");
            for (String param : params) {
                // 使用split限制分割次数，防止值中含=
                String[] pair = param.split("=", 2);
                if (pair.length == 2 && "time".equals(pair[0])) {
                    return pair[1]; // 返回time参数值
                }
            }
            Log.i("BroadcastPlayListContrl", "Time parameter not found in URL");
            return null;
        } catch (MalformedURLException e) {
            Log.e("BroadcastPlayListContrl", "Invalid URL format: " + url, e);
            return null;
        } catch (Exception e) {
            Log.e("BroadcastPlayListContrl", "Error parsing URL parameters", e);
            return null;
        }
    }

    /**
     * 安全地将十六进制字符串转为时间戳（返回Long便于处理null）
     */
    private Long safeHexToTimestamp(String hex) {
        if (hex == null || hex.isEmpty()) {
            Log.e("BroadcastPlayListContrl", "Hex string is empty");
            return null;
        }
        try {
            // 使用Long解析16进制（注意：结果可能是秒级或毫秒级，需根据业务确认）
            return Long.parseLong(hex, 16);
        } catch (NumberFormatException e) {
            Log.e("BroadcastPlayListContrl", "Invalid hex format: " + hex, e);
            return null;
        }
    }

    /**
     * 格式化Unix时间戳为可读字符串（UTC时区）
     */
    private String formatUnixTime(long timestamp) {
        try {
            // 使用秒级时间戳转毫秒（如果timestamp是毫秒级则不需要*1000）
            java.util.Date date = new java.util.Date(timestamp * 1000L);

            // 设置UTC时区格式
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss 'UTC'");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));

            return sdf.format(date);
        } catch (Exception e) {
            Log.e("BroadcastPlayListContrl", "Error formatting time: " + timestamp, e);
            return "Invalid Time";
        }
    }

    /**
     * 解码Unicode转义字符
     */
    private String unescapeUnicode(String input) {
        // 正则匹配所有形如 的 Unicode 转义字符
        Pattern pattern = Pattern.compile("\\\\u([0-9a-fA-F]{4})");
        Matcher matcher = pattern.matcher(input);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            // 提取十六进制编码部分（例如 "003d"）
            String hexGroup = matcher.group(1);
            // 将十六进制转为对应的 Unicode 字符
            char decodedChar = (char) Integer.parseInt(hexGroup, 16);
            // 替换匹配到的转义字符
            matcher.appendReplacement(sb, Matcher.quoteReplacement(String.valueOf(decodedChar)));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    private long string2Long(String strValue) {
        long longValue = 0;
        if (!StringUtil.isEmpty(strValue)) {
            try {
                longValue = Long.parseLong(strValue);
            } catch (Exception e) {
                PlayerLogUtil.log(TAG, "string2Long:", e.getMessage());
            }
        }
        return longValue;
    }

    private void updatePlayListContent(ArrayList<PlayItem> playItemArrayList, IPlayListGetListener iPlayListGetListener) {
        mPlayItemArrayList.addAll(playItemArrayList);
        int index = PlayListUtils.getLivingBroadcastPlayItem(playItemArrayList);
        if (index >= playItemArrayList.size()) {
            index = 0;
        }
        mPosition = index;
        PlayerLogUtil.log(TAG, "updatePlayListContent", "修改mPosition = " + mPosition);
        notifyPlayListGet(iPlayListGetListener, playItemArrayList.get(index), playItemArrayList);
        notifyPlayListChange(playItemArrayList);
    }

    private void updatePlayListInfo(BroadcastPlayItem broadcastPlayItem) {
        if (broadcastPlayItem != null) {
            mPlaylistInfo.setAlbumName(broadcastPlayItem.getInfoData().getAlbumName());
            mPlaylistInfo.setAlbumPic(broadcastPlayItem.getInfoData().getAlbumPic());
        }
    }

    @Override
    public PlayItem getPlayItem(PlayerBuilder playerBuilder) {
        long tempId = string2Long(playerBuilder.getId());
        if (isPlaySongList) {
            for (int i = 0; i < mSongPlayItemArrayList.size(); i++) {
                PlayItem playItem = mSongPlayItemArrayList.get(i);
                if (PlayerPreconditions.checkNull(playItem)) {
                    continue;
                }
                if (playItem.getAudioId() == tempId) {
                    PlayerLogUtil.log(TAG, "getPlayItem","son play list has id");
                    mSongListPosition = i;
                    isPlaySongList = true;
                    PlayItem playItem1 = mSongPlayItemArrayList.get(mSongListPosition);
                    if (playItem1 != null) {
                        playItem1.setPosition(0);
                    }
                    return playItem1;
                }
            }
        }
        isPlaySongList = false;
        mSongPlayItemArrayList.clear();

        BroadcastPlayItem playItem = (BroadcastPlayItem) super.getPlayItem(playerBuilder);
        if (PlayerPreconditions.checkNull(playItem)) {
            return null;
        }
        if (playItem.getTimeInfoData().getFinishTime() < DateUtil.getServerTime()) {
            playItem.setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
            // 节目已结束，设置为回听状态.检查是否有有效的回听地址
            if (StringUtil.isEmpty(playItem.getPlayUrl())) {
                PlayerLogUtil.log(TAG, "getPlayItem", "broadcast ended but no valid playback url, will need to refresh when playing. audioId=" + playItem.getAudioId());
                // 注意：这里不设置为null，保持原有URL，让播放时通过initLiving重新获取
            }
        }
        playItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
        playItem.setPosition(0);
        return playItem;
    }


    @Override
    public ArrayList getSongPlayList() {
        return mSongPlayItemArrayList;
    }

    @Override
    public void addSongPlayItem(Object o) {
        mSongPlayItemArrayList.addAll((ArrayList<PlayItem>) o);
    }

    @Override
    public void removeSongPlayItem(Object o) {
        mSongPlayItemArrayList.clear();
    }

    @Override
    public boolean isPlayingSonList() {
        return isPlaySongList;
    }

    @Override
    public boolean isExistPlayItem(long id) {
        boolean isExist = super.isExistPlayItem(id);
        if (isExist) {
            return true;
        }
        PlayerLogUtil.log(TAG, "isExistPlayItem","father play list not has");
        if (PlayerPreconditions.checkNull(mSongPlayItemArrayList)) {
            return false;
        }
        for (int i = 0; i < mSongPlayItemArrayList.size(); i++) {
            PlayItem playItem = mSongPlayItemArrayList.get(i);
            if (PlayerPreconditions.checkNull(playItem)) {
                continue;
            }
            if (playItem.getAudioId() == id) {
                PlayerLogUtil.log(TAG, "isExistPlayItem","son play list, position = " + i);
                mSongListPosition = i;
                isPlaySongList = true;
                return true;
            }
        }
        PlayerLogUtil.log(TAG, "isExistPlayItem","son play list not has");
        return false;
    }

    @Override
    public boolean hasNext() {
        if (isPlaySongList) {
            if (mSongListPosition + 1 < 0) {
                return false;
            }
            if (mSongListPosition + 1 < mPlayItemArrayList.size()) {
                return true;
            }
            return false;
        }
        return super.hasNext();
    }

    @Override
    public void release() {
        super.release();
        if (mSongPlayItemArrayList != null) {
            mSongPlayItemArrayList.clear();
            isPlaySongList = false;
            mSongListPosition = -1;
        }
    }

    @Override
    public void setCurPosition(PlayItem playItem) {
        if (isPlaySongList) {
            for (int i = 0; i < mPlayItemArrayList.size(); i++) {
                PlayItem playItemTemp = mPlayItemArrayList.get(i);
                if (PlayerPreconditions.checkNull(playItemTemp)) {
                    continue;
                }
                if (playItemTemp.getAudioId() == playItem.getAudioId()) {
                    playItem.setPosition(0);
                    mSongListPosition = i;
                    PlayerLogUtil.log(TAG, "setCurPosition","position: " + mSongListPosition);
                    return;
                }
            }
        } else {
            if(((BroadcastPlayItem)playItem).getTimeInfoData().getStartTime() > DateUtil.getServerTime()){
                notifyPlayListChangeError(404);
                return;
            }
            super.setCurPosition(playItem);
        }
    }

    @Override
    public int getCurPosition() {
        if(isPlaySongList){
            return mSongListPosition;
        }
        return super.getCurPosition();
    }
}
