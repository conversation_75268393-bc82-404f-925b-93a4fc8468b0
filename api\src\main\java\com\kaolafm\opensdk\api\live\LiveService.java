package com.kaolafm.opensdk.api.live;

import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.HostConstant;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.live.model.ChatRoomTokenDetail;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import io.reactivex.Single;
import java.util.HashMap;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;


/**
 * <AUTHOR>
 */
public interface LiveService {

    /**
     * 直播info
     *
     * @param id
     * @return
     */
    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.REQUEST_KAOLA_LIVE_INFO)
    Single<BaseResult<LiveInfoDetail>> getLiveInfo(@Query(LiveRequest.KEY_PROGRAM_ID) String id);


    /**
     * 聊天室token
     *
     * @param tempMap
     * @return
     */
    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.REQUEST_KAOLA_CHAT_ROOM_TOKEN)
    Single<BaseResult<ChatRoomTokenDetail>> getChatRoomToken(@QueryMap HashMap<String, String> tempMap);

    /**
     * 根据开发者的唯一标识、头像、昵称获取进入直播的token。
     * @return
     */
    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @POST(KaolaApiConstant.GET_CHAT_ROOM_TOKEN_BY_ID)
    Single<BaseResult<ChatRoomTokenDetail>> getChatRoomTokenByUnique(@Body RequestBody requestBody);

}
