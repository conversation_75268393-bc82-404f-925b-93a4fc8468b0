package com.kaolafm.opensdk.api.subscribe;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.HostConstant;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.media.model.AudioDetails;

import java.util.List;

import io.reactivex.Single;
import retrofit2.Call;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: SubscribeService.java                                               
 *                                                                  *
 * Created in 2018/8/15 上午11:17                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
/*package*/  interface SubscribeService {

    @Headers({HostConstant.DOMAIN_HEADER_OPEN_KAOLA})
    @POST(KaolaApiConstant.KRADIO_GET_SUBSCRIBE_LIST_STRONG)
    Single<BaseResult<BasePageResult<List<SubscribeInfo>>>> getSubscribeList(@Query("type") int type, @Query("pagenum") int pageNum, @Query("pagesize") int pageSize);

    @Headers({HostConstant.DOMAIN_HEADER_OPEN_KAOLA})
    @POST(KaolaApiConstant.KRADIO_GET_SUBSCRIBE_LIST)
    Call<BaseResult<BasePageResult<List<SubscribeInfo>>>> getSubscribeListSync(@Query("type") int type, @Query("pagenum") int pageNum, @Query("pagesize") int pageSize);

    @Headers({HostConstant.DOMAIN_HEADER_OPEN_KAOLA})
    @POST(KaolaApiConstant.KRADIO_SUBSCRIBE_STRONG)
    Single<BaseResult<SubscribeStatus>> subscribe(@Query("id") long id);

    @Headers({HostConstant.DOMAIN_HEADER_OPEN_KAOLA})
    @POST(KaolaApiConstant.KRADIO_SUBSCRIBE)
    Call<BaseResult<SubscribeStatus>> subscribeSync(@Query("id") long id);

    @Headers({HostConstant.DOMAIN_HEADER_OPEN_KAOLA})
    @POST(KaolaApiConstant.KRADIO_UNSUBSCRIBE_STRONG)
    Single<BaseResult<SubscribeStatus>> unsubscribe(@Query("id") long id);

    @Headers({HostConstant.DOMAIN_HEADER_OPEN_KAOLA})
    @POST(KaolaApiConstant.KRADIO_UNSUBSCRIBE)
    Call<BaseResult<SubscribeStatus>> unsubscribeSync(@Query("id") long id);

    @Headers({HostConstant.DOMAIN_HEADER_OPEN_KAOLA})
    @POST(KaolaApiConstant.KRADIO_CHECK_IS_SUBSCRIBE_STRONG)
    Single<BaseResult<SubscribeStatus>> isSubscribed(@Query("id") long id);

    @Headers({HostConstant.DOMAIN_HEADER_OPEN_KAOLA})
    @POST(KaolaApiConstant.KRADIO_CHECK_IS_SUBSCRIBE)
    Call<BaseResult<SubscribeStatus>> isSubscribedSync(@Query("id") long id);

    /**
     * 获取用户的订阅，返回的是类似PGC的流，用于一键播放。
     * @param id 第一获取可以为空，获取更多时需要传入，该值是上一次请求返回的。
     * @return
     */
    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @POST(KaolaApiConstant.KRADIO_GET_USER_FOLLOW_RADIO_STRONG)
    Single<BaseResult<List<AudioDetails>>> getUserFollowRadio(@Query("clockid") String id);

    Single<BaseResult<BasePageResult<List<SubscribeInfo>>>> getSubscribeListOnce();
}
