package com.kaolafm.opensdk.player.logic.model.item;

import android.os.Parcel;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.player.logic.model.item.model.InfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayUrlData;
import com.kaolafm.opensdk.player.logic.model.item.model.RadioInfoData;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * 电台-播放对象
 */
public class RadioPlayItem extends PlayItem {

    /**
     * 播放url相关数据
     */
    private PlayUrlData mPlayUrlData;

    /**
     * 专辑信息相关数据
     */
    private InfoData mInfoData;

    /**
     * 电台信息
     */
    private RadioInfoData mRadioInfoData;

    public RadioPlayItem() {
        mPlayUrlData = new PlayUrlData();
        mInfoData = new InfoData();
        mRadioInfoData = new RadioInfoData();
    }

    @Override
    public String getRadioId() {
        return String.valueOf(mRadioInfoData.getRadioId());
    }

    @Override
    public String getTitle() {
        return mInfoData.getTitle();
    }


    @Override
    public String getAlbumId() {
        return String.valueOf(mInfoData.getAlbumId());
    }


    @Override
    public String getPicUrl() {
        String picUrl = mInfoData.getAudioPic();
        if (StringUtil.isEmpty(picUrl)) {
            picUrl = mInfoData.getAlbumPic();
        }
        if (StringUtil.isEmpty(picUrl)) {
            picUrl = mRadioInfoData.getRadioPic();
        }
        return picUrl;
    }

    @Override
    public String getHost() {
        return mInfoData.getHosts();
    }

    @Override
    public String getAlbumTitle() {
        return mInfoData.getAlbumName();
    }

    @Override
    public String getCallback() {
        return mRadioInfoData.getCallBack();
    }

    @Override
    public String getSource() {
        return mRadioInfoData.getSource();
    }

    @Override
    public String getSourceName() {
        return mInfoData.getSourceName();
    }

    @Override
    public String getSourceLogo() {
        return mInfoData.getSourceLogo();
    }

    public PlayUrlData getPlayUrlData() {
        return mPlayUrlData;
    }

    public void setPlayUrlData(PlayUrlData playUrlData) {
        this.mPlayUrlData = playUrlData;
    }

    public InfoData getInfoData() {
        return mInfoData;
    }

    public void setInfoData(InfoData infoData) {
        this.mInfoData = infoData;
    }

    public RadioInfoData getRadioInfoData() {
        return mRadioInfoData;
    }

    public void setRadioInfoData(RadioInfoData radioInfoData) {
        this.mRadioInfoData = radioInfoData;
    }

    @Override
    public String getRadioName() {
        return mRadioInfoData.getRadioName();
    }

    @Override
    public int getIsThirdParty() {
        return mRadioInfoData.getIsThirdParty();
    }

    @Override
    public int getType() {
        return PlayerConstants.RESOURCES_TYPE_RADIO;
    }

    @Override
    public String getUpdateTime() {
        return mInfoData.getUpdateTime();
    }

    @Override
    public int getRadioSubTagType() {
        return mRadioInfoData.getRadioSubTagType();
    }

    private RadioPlayItem(Parcel parcel) {

    }

    public static final Creator<RadioPlayItem> CREATOR = new Creator<RadioPlayItem>() {

        @Override
        public RadioPlayItem createFromParcel(Parcel source) {
            return new RadioPlayItem(source);
        }

        @Override
        public RadioPlayItem[] newArray(int size) {
            return new RadioPlayItem[size];
        }
    };
}
