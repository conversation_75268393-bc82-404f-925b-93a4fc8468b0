package com.kaolafm.opensdk.account.token;

import android.app.Application;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.kaolafm.base.utils.SpUtil;
import com.kaolafm.opensdk.di.scope.AppScope;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.Lazy;

/**
 * <AUTHOR>
 * @date 2018/7/30
 */
public class KaolaAccessTokenCache implements TokenCache<KaolaAccessToken>, TokenMonitor<KaolaAccessToken, TingbanTokenObserver> {

    private static final String SP_KAOLA_ACCESS_TOKEN_NAME
            = "sdk.KaolaAccessToken.SharedPreferences";

    private static final String CACHED_KAOLA_ACCESS_TOKEN
            = "com.kaolafm.open.sdk.KaolaAccessTokenV2";

    private static final String CACHED_KAOLA_ACCESS_TOKEN_OPEN_ID_KEY
            = "com.kaolafm.open.sdk.KaolaAccessToken.OpenId";

//    private final SharedPreferences mSharedPreferences;

    @Inject
    KaolaAccessToken mAccessToken;

    private List<TingbanTokenObserver> mObservers;

    @Inject
    @AppScope
    Lazy<Gson> mGsonLazy;
    private final String mSpName;

    @Inject
    public KaolaAccessTokenCache(Application application) {
        mSpName = application.getPackageName() + SP_KAOLA_ACCESS_TOKEN_NAME;
        SpUtil.init(application, mSpName);
    }

    @Override
    public boolean accept(AccessToken token) {
        return token instanceof KaolaAccessToken;
    }

    @Override
    public KaolaAccessToken getToken() {
        return mAccessToken;
    }

    @Override
    public void save(KaolaAccessToken kaolaAccessToken) {
        if (kaolaAccessToken != null) {
            mAccessToken = kaolaAccessToken;
            String accessTokenStr = mGsonLazy.get().toJson(kaolaAccessToken);
            if (!TextUtils.isEmpty(accessTokenStr)) {
                put(CACHED_KAOLA_ACCESS_TOKEN, accessTokenStr);
                String openId = kaolaAccessToken.getOpenId();
                if (!TextUtils.isEmpty(openId)) {
                    put(CACHED_KAOLA_ACCESS_TOKEN_OPEN_ID_KEY, openId);
                }
            }
            notifyObserver(kaolaAccessToken);
        } else {
            logout();
        }
    }

    private void put(String key, String value) {
        SpUtil.putEncryptedString(mSpName, key, value);
    }

    private String get(String key) {
        return SpUtil.getDecryptedString(mSpName, key, null).first;
    }

    void saveOpenId(String openId) {
        mAccessToken.setOpenId(openId);
        put(CACHED_KAOLA_ACCESS_TOKEN_OPEN_ID_KEY, openId);
    }

    @Override
    public KaolaAccessToken load() {
        String accessTokenStr = get(CACHED_KAOLA_ACCESS_TOKEN);
        if (!TextUtils.isEmpty(accessTokenStr)) {
            KaolaAccessToken accessToken = mGsonLazy.get().fromJson(accessTokenStr, KaolaAccessToken.class);
            if (accessToken != null) {
                mAccessToken = accessToken;
            }
            String openId = get(CACHED_KAOLA_ACCESS_TOKEN_OPEN_ID_KEY);
            mAccessToken.setOpenId(openId);
        }
        return mAccessToken;
    }

    @Override
    public void clear() {
        mAccessToken.clear();
        SpUtil.removeAnyEncryptedByName(mSpName, CACHED_KAOLA_ACCESS_TOKEN, CACHED_KAOLA_ACCESS_TOKEN_OPEN_ID_KEY);
        notifyObserver(null);
    }

    @Override
    public void logout() {
        mAccessToken.logout();
        SpUtil.removeEncrypted(mSpName, CACHED_KAOLA_ACCESS_TOKEN);
        notifyObserver(mAccessToken);
    }

    @Override
    public void notifyObserver(KaolaAccessToken token) {
        if (mObservers != null && !mObservers.isEmpty()) {
            for (TingbanTokenObserver observer : mObservers) {
                observer.onChange(token);
            }
        }
    }

    @Override
    public void registerObserver(TingbanTokenObserver observer) {
        if (mObservers == null) {
            mObservers = new ArrayList<>();
        }
        if (observer != null) {
            mObservers.add(observer);
        }
    }
    @Override
    public void unregisterObserver(TingbanTokenObserver observer) {
        if (mObservers != null && observer != null) {
            mObservers.remove(observer);
        }
    }
}
