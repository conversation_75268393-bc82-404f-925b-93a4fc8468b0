package com.kaolafm.opensdk.demo.scene;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.OnClick;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.scene.AccScene;
import com.kaolafm.opensdk.api.scene.Scene;
import com.kaolafm.opensdk.api.scene.SceneInfo;
import com.kaolafm.opensdk.api.scene.SceneRequest;
import com.kaolafm.opensdk.api.scene.SpeedScene;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.demo.player.AlbumPlayerActivity;
import com.kaolafm.opensdk.demo.player.AudioPlayerActivity;
import com.kaolafm.opensdk.demo.player.BasePlayerActivity;
import com.kaolafm.opensdk.demo.player.BroadcastPlayerActivity;
import com.kaolafm.opensdk.demo.player.RadioPlayerActivity;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.LivePlayerManager;
import java.util.ArrayList;

public class SceneActivity extends BaseActivity {

    @BindView(R.id.btn_scene_send)
    Button mBtnSceneSend;

    @BindView(R.id.cb_scene_acc)
    CheckBox mCbSceneAcc;

    @BindView(R.id.cb_scene_high_speed)
    CheckBox mCbSceneHighSpeed;

    @BindView(R.id.cb_scene_low_speed)
    CheckBox mCbSceneLowSpeed;

    @BindView(R.id.cb_scene_medium_speed)
    CheckBox mCbSceneMediumSpeed;

    @BindView(R.id.tv_scene_info)
    TextView mTvSceneInfo;

    private SceneInfo mSceneInfo;

    @Override
    public int getLayoutId() {
        return R.layout.activity_scene;
    }

    @Override
    public void initView(Bundle savedInstanceState) {


    }

    @Override
    public void initData() {

    }

    @OnClick({R.id.btn_scene_send, R.id.tv_scene_info})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.btn_scene_send:
                ArrayList<Scene> scenes = new ArrayList<>();
                if (mCbSceneAcc.isChecked()) {
                    scenes.add(new AccScene());
                }

                if (mCbSceneLowSpeed.isChecked()) {
                    scenes.add(new SpeedScene(SpeedScene.TYPE_LOW_SPEED));
                }
                if (mCbSceneMediumSpeed.isChecked()) {
                    scenes.add(new SpeedScene(SpeedScene.TYPE_MEDIUM_SPEED));
                }

                if (mCbSceneHighSpeed.isChecked()) {
                    scenes.add(new SpeedScene(SpeedScene.TYPE_HIGH_SPEED));
                }
                send(scenes);
                break;
            case R.id.tv_scene_info:
                if (mSceneInfo == null) {
                    return;
                }
                int contentType = mSceneInfo.getContentResType();
                Log.e("SceneActivity", "onViewClicked: contentType="+contentType);
                Intent intent = new Intent();
                Class clazz = null;
                switch (contentType) {
                    case ResType.TYPE_ALBUM:
                        clazz = AlbumPlayerActivity.class;
                        break;
                    case ResType.TYPE_AUDIO:
                        clazz = AudioPlayerActivity.class;
                        break;
                    case ResType.TYPE_BROADCAST:
                        clazz = BroadcastPlayerActivity.class;
                        break;
                    case ResType.TYPE_RADIO:
                        clazz = RadioPlayerActivity.class;
                        break;
                    case ResType.TYPE_LIVE:
                        clazz = LivePlayerManager.class;
                        break;
                    default:
                }
                if (clazz != null) {
                    intent.putExtra(BasePlayerActivity.KEY_ID, mSceneInfo.getContentId());
                    intent.setClass(SceneActivity.this, clazz);
                    startActivity(intent);
                }
                break;
            default:
        }

    }

    private void send(ArrayList<Scene> scenes) {
        new SceneRequest().getSceneInfo(new HttpCallback<SceneInfo>() {
            @Override
            public void onSuccess(SceneInfo sceneInfo) {
                mSceneInfo = sceneInfo;
                if (mTvSceneInfo != null) {
                    mTvSceneInfo.setText(sceneInfo.toString());
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取场景信息失败", exception);

            }
        }, scenes.toArray(new Scene[]{}));
    }
}
