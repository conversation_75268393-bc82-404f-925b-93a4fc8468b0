package com.kaolafm.opensdk.demo;

import android.content.Context;
import android.support.multidex.MultiDexApplication;

import com.kaolafm.opensdk.OpenSDK;
import com.netease.nimlib.sdk.NIMClient;
import com.netease.nimlib.sdk.SDKOptions;

/**
 * <AUTHOR>
 * @date 2018/7/19
 */

public class DemoApplication extends MultiDexApplication {

    public static Context appContext;

    @Override
    public void onCreate() {
        super.onCreate();
//        OpenSDK.getInstance().init(this);
        appContext = this;
        NIMClient.config(this, null, new SDKOptions());

    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        OpenSDK.getInstance().release();
    }

    public static Context getContext() {
        return appContext;
    }
}
