package com.kaolafm.opensdk.api.recommend.model;

import java.util.List;

public class SceneDataList extends BaseSceneListData<List<SceneDataList>>{


    /**
     * columnCode : 1
     * outputMode : 2
     * contentType : 4
     * pageNum : 1
     * pageSize : 7
     * dataList : [{"id":1200000000501,"name":"新闻早知道\t","img":"http://img.kaolafm.net/mz/images/201806/57d7c546-2fa3-46dd-98c3-0d00b2e9b7f3/default.jpg","algo":"17","callback":"0_03_1200000000501_17_109_24_15747524326140043","debugInfos":null,"bizLine":"2","labels":["科技","财经","头条"],"contentType":4},{"id":1200000000548,"name":"流行音乐台","img":"http://img.kaolafm.net/mz/images/201906/20044826-7605-4583-b784-bbeb34476b5c/default.jpg","algo":"17","callback":"1_03_1200000000548_17_109_24_15747524326140043","debugInfos":null,"bizLine":"","labels":[""],"contentType":4},{"id":1200000000238,"name":"儿童电台","img":"http://img.kaolafm.net/mz/images/201906/dfa47aa9-3065-4bd7-ae0e-c4f145b49bb7/default.jpg","algo":"17","callback":"2_03_1200000000238_17_109_24_15747524326140043","debugInfos":null,"bizLine":"1,2","labels":["儿歌","故事","诗词"],"contentType":4},{"id":1200000000443,"name":"环球时政","img":"http://img.kaolafm.net/mz/images/201806/372c06f3-b497-412d-a5f8-2f66690c9ead/default.jpg","algo":"17","callback":"3_03_1200000000443_17_109_24_15747524326140043","debugInfos":null,"bizLine":"2","labels":["时政","头条","焦点"],"contentType":4},{"id":1200000000468,"name":"护送女王","img":"http://img.kaolafm.net/mz/images/201806/7da5c681-8b27-401e-a6a2-4baae62cc87e/default.jpg","algo":"17","callback":"4_03_1200000000468_17_109_24_15747524326140043","debugInfos":null,"bizLine":"2","labels":["电影","娱乐","时尚"],"contentType":4},{"id":1200000000522,"name":"萌娃故事汇","img":"http://img.kaolafm.net/mz/images/201904/7cfd4e1e-13a0-40c2-97d6-1efc35ad053e/default.jpg","algo":"17","callback":"5_03_1200000000522_17_109_24_15747524326140043","debugInfos":null,"bizLine":"","labels":["儿歌","绘本","故事"],"contentType":4},{"id":1200000000439,"name":"股票","img":"http://img.kaolafm.net/mz/images/201806/26342745-fdf7-48b9-a05c-9f53877fc967/default.jpg","algo":"17","callback":"6_03_1200000000439_17_109_24_15747524326140043","debugInfos":null,"bizLine":"2","labels":["财经","金融","股市"],"contentType":4}]
     */

    private String columnCode;
    private String outputMode;
    private String contentType;
    private int pageNum;
    private int pageSize;
    private List<SceneData> dataList;

    public String getColumnCode() {
        return columnCode;
    }

    public void setColumnCode(String columnCode) {
        this.columnCode = columnCode;
    }

    public String getOutputMode() {
        return outputMode;
    }

    public void setOutputMode(String outputMode) {
        this.outputMode = outputMode;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<SceneData> getDataList() {
        return dataList;
    }

    public void setDataList(List<SceneData> dataList) {
        this.dataList = dataList;
    }

    public static class SceneData {
        /**
         * id : 1200000000501
         * name : 新闻早知道
         * img : http://img.kaolafm.net/mz/images/201806/57d7c546-2fa3-46dd-98c3-0d00b2e9b7f3/default.jpg
         * algo : 17
         * callback : 0_03_1200000000501_17_109_24_15747524326140043
         * debugInfos : null
         * bizLine : 2
         * labels : ["科技","财经","头条"]
         * contentType : 4
         */

        private long id;
        private String name;
        private String img;
        private String algo;
        private String callback;
        private Object debugInfos;
        private String bizLine;
        private int contentType;
        private List<String> labels;

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getAlgo() {
            return algo;
        }

        public void setAlgo(String algo) {
            this.algo = algo;
        }

        public String getCallback() {
            return callback;
        }

        public void setCallback(String callback) {
            this.callback = callback;
        }

        public Object getDebugInfos() {
            return debugInfos;
        }

        public void setDebugInfos(Object debugInfos) {
            this.debugInfos = debugInfos;
        }

        public String getBizLine() {
            return bizLine;
        }

        public void setBizLine(String bizLine) {
            this.bizLine = bizLine;
        }

        public int getContentType() {
            return contentType;
        }

        public void setContentType(int contentType) {
            this.contentType = contentType;
        }

        public List<String> getLabels() {
            return labels;
        }

        public void setLabels(List<String> labels) {
            this.labels = labels;
        }
    }
}
