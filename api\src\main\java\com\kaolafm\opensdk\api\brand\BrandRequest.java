package com.kaolafm.opensdk.api.brand;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.HostConstant;
import com.kaolafm.opensdk.api.brand.model.BrandDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;

/**
 * 品牌相关
 * <AUTHOR>
 * @date 2019-03-28
 */
public class BrandRequest extends BaseRequest {

    private final BrandService mBrandService;

    public BrandRequest() {
        mUrlManager.putDomain(HostConstant.BRAND_DOMAIN_NAME, HostConstant.BRAND_HOST);
        mBrandService = obtainRetrofitService(BrandService.class);
    }

    /**
     * 获取品牌信息，包括名称，logo，用户须知
     */
    public void getBrandInfo(HttpCallback<BrandDetails> callback) {
        doHttpDeal(mBrandService.getBrandInfo(), BaseResult::getResult, callback);
    }
}
