# 播放器SSL错误修复方案

## 问题描述

在自动播放下一首广播节目时，偶现SSL连接错误导致播放失败的问题。主要表现为：

1. 自动播放下一首时获取到指向有SSL问题的服务器URL（如 `play.a.l.kaolafm.net`）
2. 出现 `error:14094438:SSL routines:ssl3_read_bytes:tlsv1 alert internal error` 错误
3. 用户手动切换广播后播放正常

## 根本原因分析

### 问题的真正原因：不同API接口返回不同格式的播放URL

通过深入分析发现，问题的根本原因是**服务器端存在新旧两套播放系统，不同的API接口返回不同格式的播放URL**：

#### 1. 自动播放下一首使用的API
- **接口**：`/v2/program/detail?programid={programId}`
- **调用方法**：`getBroadcastProgramDetails(programId)`
- **返回URL格式**：`https://play.a.l.kaolafm.net/fm/{broadcastId}h.m3u8`（旧格式，有SSL问题）
- **数据来源**：节目详情数据库（可能包含过期的播放URL）

#### 2. 手动切换时使用的API
- **接口**：`/v2/broadcast/currentprogram?bid={broadcastId}`
- **调用方法**：`getBroadcastCurrentProgramDetails(broadcastId)`
- **返回URL格式**：`https://ytcast.radio.cn/64/radios/{radioId}/index_{radioId}.m3u8?type=1&key={key}&time={time}`（新格式，SSL正常）
- **数据来源**：实时播放系统（返回最新的播放URL）

#### 3. 服务器端系统迁移状态
- **旧系统**：`play.a.l.kaolafm.net` - 存在SSL配置问题
- **新系统**：`ytcast.radio.cn` - SSL配置正常，包含认证参数
- **迁移状态**：节目详情数据库中的URL还未完全更新到新格式

## 解决方案

### 核心解决策略：API接口智能切换

当检测到旧格式URL时，自动调用当前节目接口获取正确的播放地址：

```java
// 在BroadcastPlayListControl.initLiving()中
if (ServerConfig.isProblematicServer(newUrl) && retryCount < ServerConfig.MAX_URL_RETRY_COUNT) {
    // 尝试使用getCurrentProgramDetails接口获取当前节目的正确播放URL
    tryGetCurrentProgramUrl(playItem, iPlayListGetListener, retryCount);
    return;
}
```

### 1. 增强的错误处理机制

#### 播放控制层面 (PlayControl.java)
- 在 `onPlayerFailed` 方法中增加SSL和网络错误的检测
- 当检测到SSL错误时，自动清空当前URL并重新获取
- 避免使用有问题的缓存URL

#### 播放列表控制层面 (BroadcastPlayListControl.java)
- 在 `initLiving` 方法中增加重试机制
- 检测到有问题的服务器URL时自动重试
- 网络错误时的智能重试

#### IJK播放器层面 (IJKMediaPlayerAdapter.java)
- 增强错误码识别，区分SSL错误和其他错误
- SSL错误交给上层处理，避免播放器级别的无效重试

### 2. 配置管理 (ServerConfig.java)

新增配置类统一管理：
- 有问题的服务器域名黑名单
- 重试次数和延迟时间配置
- 错误码判断逻辑

### 3. 关键修改点

#### PlayControl.java
```java
// 检查是否是SSL错误或网络连接错误，如果是则尝试重新获取播放URL
if (shouldRetryWithNewUrl(what, extra)) {
    PlayerLogUtil.log(getClass().getSimpleName(), "onPlayerFailed", "SSL or network error, retry with new URL");
    retryWithNewUrl();
    return;
}
```

#### BroadcastPlayListControl.java
```java
// 检查获取到的URL是否指向有问题的服务器
String newUrl = programDetails.getPlayUrl();
if (ServerConfig.isProblematicServer(newUrl) && retryCount < ServerConfig.MAX_URL_RETRY_COUNT) {
    // 尝试使用getCurrentProgramDetails接口获取当前节目的正确播放URL
    tryGetCurrentProgramUrl(playItem, iPlayListGetListener, retryCount);
    return;
}

// 新增方法：API接口智能切换
private void tryGetCurrentProgramUrl(PlayItem playItem, IPlayListGetListener iPlayListGetListener, int retryCount) {
    BroadcastPlayItem broadcastPlayItem = (BroadcastPlayItem) playItem;
    long broadcastId = broadcastPlayItem.getBroadcastId();

    // 调用当前节目接口获取正确的播放URL
    mBroadcastRequest.getBroadcastCurrentProgramDetails(broadcastId, new HttpCallback<ProgramDetails>() {
        @Override
        public void onSuccess(ProgramDetails currentProgramDetails) {
            String currentUrl = currentProgramDetails.getPlayUrl();
            if (!ServerConfig.isProblematicServer(currentUrl)) {
                // 使用当前节目的正确播放URL
                broadcastPlayItem.setPlayUrl(currentUrl);
                notifyPlayListGet(iPlayListGetListener, playItem, null);
            } else {
                // 如果还是有问题，回退到重试
                // 延迟重试逻辑...
            }
        }
        // 错误处理...
    });
}
```

## 配置参数

### ServerConfig.java 中的可配置参数：

- `MAX_URL_RETRY_COUNT = 2`: URL重试的最大次数
- `RETRY_DELAY_MS = 1000`: 重试延迟时间（毫秒）
- `NETWORK_ERROR_RETRY_DELAY_MS = 2000`: 网络错误重试延迟时间（毫秒）
- `PROBLEMATIC_SERVERS`: 有问题的服务器域名列表

## 使用方法

### 添加有问题的服务器到黑名单
```java
ServerConfig.addProblematicServer("problematic.server.com");
```

### 从黑名单中移除服务器
```java
ServerConfig.removeProblematicServer("fixed.server.com");
```

### 检查URL是否指向有问题的服务器
```java
boolean isProblematic = ServerConfig.isProblematicServer(url);
```

## 预期效果

1. **自动故障转移**: 当检测到SSL错误时，自动重新获取播放URL
2. **智能重试**: 避免连接到已知有问题的服务器
3. **减少播放失败**: 通过多层重试机制提高播放成功率
4. **用户体验改善**: 减少用户需要手动干预的情况

## 监控建议

1. 增加对SSL错误的监控和统计
2. 记录重试成功率和失败率
3. 定期检查和更新有问题的服务器列表
4. 监控不同服务器的可用性和响应时间

## 注意事项

1. 重试机制有次数限制，避免无限重试
2. 延迟重试给服务器负载均衡时间调整
3. 保持与服务器端的协调，及时修复SSL配置问题
4. 定期清理已修复的服务器黑名单

## 测试建议

1. 模拟SSL错误场景测试重试机制
2. 测试不同网络环境下的播放稳定性
3. 验证自动播放下一首的成功率
4. 测试用户手动切换的兼容性
