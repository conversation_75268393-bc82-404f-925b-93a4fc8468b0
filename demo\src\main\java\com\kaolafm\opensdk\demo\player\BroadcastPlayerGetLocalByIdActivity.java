package com.kaolafm.opensdk.demo.player;

import android.os.Bundle;
import android.util.Log;
import android.view.View;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.broadcast.BroadcastDetails;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.subscribe.SubscribeRequest;
import com.kaolafm.opensdk.demo.detail.StringAdapter.Item;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayListControl;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlaylistInfo;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.utils.BeanUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 广播播放器页面
 * <AUTHOR> Yan
 * @date 2018/12/7
 */

public class BroadcastPlayerGetLocalByIdActivity extends BasePlayerActivity {

    private int curPosition;
    private int mNextPage = 1;
    private boolean isHaveNext = true;

    @Override
    public void initView(Bundle savedInstanceState) {
        super.initView(savedInstanceState);
        setTitle("广播播放器页面");
        //添加播放状态监听
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().addPlayListControlStateCallback(iPlayListStateListener);

        btnSubscribe.setVisibility(View.VISIBLE);
    }

    @Override
    public void initData() {
        //根据ID播放广播
        PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(mId)).setType(PlayerConstants.RESOURCES_TYPE_BROADCAST));
        getBroadcastPlaylist(false);
        getSubscribeState();
    }

    /**
     * 获取该广播订阅状态
     */
    private void getSubscribeState() {
        new SubscribeRequest().isSubscribed(mId, new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                isSubscribed = aBoolean;
                btnSubscribe.setText(aBoolean ? "取消订阅" : "订阅");
                btnSubscribe.setEnabled(true);
            }

            @Override
            public void onError(ApiException exception) {
                btnSubscribe.setText(exception.getMessage());
            }
        });
    }

    private void getBroadcastPlaylist(boolean isLoadMore) {
        if (isLoadMore && !isHaveNext) {
            //如果加载更多没有下一页了就不在请求
            if (mTrfDetailPlaylist != null) {
                mTrfDetailPlaylist.finishLoadmore();
            }
            showToast("没有更多");
            return;
        }
        new BroadcastRequest().getBroadcastNeighborList(mId, mNextPage, 10, new HttpCallback<BasePageResult<List<BroadcastDetails>>>() {
            @Override
            public void onSuccess(BasePageResult<List<BroadcastDetails>> listBasePageResult) {
                mNextPage = listBasePageResult.getNextPage();
                if(listBasePageResult.getHaveNext()==0){
                    isHaveNext =false;
                }
                if (!ListUtil.isEmpty(listBasePageResult.getDataList())) {
                    Log.i("ttt","listBasePageResult.getDataList() "+listBasePageResult.getDataList().toString());
                    List<Item> datas = new ArrayList<>();
                    ArrayList<PlayItem> playItemList = new ArrayList<>();
                    for (int i = 0, size = listBasePageResult.getDataList().size(); i < size; i++) {
                        BroadcastDetails item = listBasePageResult.getDataList().get(i);
                        Item sai = new Item();
                        sai.id = item.getBroadcastId();
                        sai.type = ResType.TYPE_BROADCAST;
                        sai.title = item.getName();
                        sai.details = item.getFreq();
                        sai.item = item;
                        sai.playItem =  BeanUtil.translateToPlayItem(item);
                        datas.add(sai);
                        playItemList.add(sai.playItem);
                    }
                    if(isLoadMore){
                        mAdapter.addDataList(datas);
                        if (mTrfDetailPlaylist != null) {
                            mTrfDetailPlaylist.finishLoadmore();
                        }
                    }else{
                        mAdapter.setDataList(datas);
                        PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(playItemList.get(0).getAudioId())).setType(PlayerConstants.RESOURCES_TYPE_BROADCAST));
                    }
                }
            }

            @Override
            public void onError(ApiException exception) {

            }
        });
    }

    /**
     * 获取广播详情
     */
    private void getBroadcastDetails(long broadcastId) {
        new BroadcastRequest().getBroadcastDetails(broadcastId, new HttpCallback<BroadcastDetails>() {
            @Override
            public void onSuccess(BroadcastDetails broadcastDetails) {
                showDetail(broadcastDetails, broadcastDetails.getImg());
                PlayerManager.getInstance().getPlayListInfo().setBroadcastChannel(broadcastDetails.getFreq());
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取广播详情错误", exception);
            }
        });
    }

    @Override
    protected void playPre() {
//        PlayerManager.getInstance().playPre();
        if(curPosition>0){
            long id = mAdapter.getDataList().get(--curPosition).id;
            PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(id)).setType(PlayerConstants.RESOURCES_TYPE_BROADCAST));
        }
    }

    @Override
    protected void switchPlayPause() {
        PlayerManager.getInstance().switchPlayerStatus();
    }

    @Override
    protected void playNext() {
        if(curPosition<mAdapter.getDataList().size()-1){
            long id = mAdapter.getDataList().get(++curPosition).id;
            PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(id)).setType(PlayerConstants.RESOURCES_TYPE_BROADCAST));
        }
    }

    @Override
    protected void refresh() {

    }

    @Override
    protected void loadMore() {
        getBroadcastPlaylist(true);
    }

    @Override
    protected void playItem(Item item) {
        curPosition = mAdapter.getDataList().indexOf(item);
        Log.i("ttt","item.id "+item.id);
        PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(item.id)).setType(PlayerConstants.RESOURCES_TYPE_BROADCAST));
        getBroadcastDetails(item.id);
    }

    public int getCurrentPosition() {
        return curPosition;
    }

    @Override
    protected void seek(int progress) {
        PlayerManager.getInstance().seek(progress);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().removePlayListControlStateCallback(iPlayListStateListener);
    }

    private IPlayListStateListener iPlayListStateListener = new IPlayListStateListener(){

        @Override
        public void onPlayListChange(List<PlayItem> playItemList) {
            showToast("广播播单发生变化");
        }

        @Override
        public void onPlayListChangeError(int errorCode) {

        }
    };
}
