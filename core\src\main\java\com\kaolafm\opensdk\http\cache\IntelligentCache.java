package com.kaolafm.opensdk.http.cache;

import android.support.annotation.Nullable;
import java.util.HashMap;
import java.util.Set;

/**
 * {@link IntelligentCache} 含有可将数据永久存储至内存中的存储容器 {@link #mMap}, 和当达到最大容量时可根据 LRU
 * 算法抛弃不合规数据的存储容器 {@link #mCache}
 * <p>
 * {@link IntelligentCache} 可根据您传入的 {@code key} 智能的判断您需要将数据存储至哪个存储容器, 从而针对数据
 * 的不同特性进行不同的存储优化
 * <p>
 * 调用 {@link IntelligentCache#put(Object, Object)} 方法, 使用 {@link #KEY_KEEP} + {@code key} 作为 key 传入的
 * {@code value} 可存储至 {@link #mMap} (数据永久存储至内存中, 适合比较重要的数据) 中, 否则储存至 {@link #mCache}
 * <p>
 * <AUTHOR>
 * @date 2018/4/18
 */

public class IntelligentCache<V> implements Cache<String, V> {

    public static final String KEY_KEEP = "Keep=";

    private final LruCache<String, V> mCache;

    private final HashMap<String, V> mMap;

    public IntelligentCache(int cacheSize) {
        mMap = new HashMap<>();
        mCache = new LruCache<>(cacheSize);
    }

    @Override
    public int size() {
        return mMap.size() + mCache.size();
    }

    @Override
    public int getMaxSize() {
        return mMap.size() + mCache.getMaxSize();
    }

    /**
     * 如果在 {@code key} 中使用 {@link #KEY_KEEP} 作为其前缀, 则操作 {@link #mMap}, 否则操作 {@link #mCache}
     * @param key {@code key}
     * @return
     */
    @Nullable
    @Override
    public V get(String key) {
        if (key.startsWith(KEY_KEEP)){
            return mMap.get(key);
        }
        return mCache.get(key);
    }

    @Nullable
    @Override
    public V put(String key, V value) {
        if (key.startsWith(KEY_KEEP)){
            return mMap.put(key, value);
        }
        return mCache.put(key, value);
    }

    @Nullable
    @Override
    public V remove(String key) {
        if (key.startsWith(KEY_KEEP)){
            return mMap.remove(key);
        }
        return mCache.remove(key);
    }

    @Override
    public boolean containsKey(String key) {
        if (key.startsWith(KEY_KEEP)){
            return mMap.containsKey(key);
        }
        return mCache.containsKey(key);
    }

    /**
     * 将 {@link #mMap} 和 {@link #mCache} 的 {@code keySet} 合并返回
     * @return
     */
    @Override
    public Set<String> keySet() {
        Set<String> set = mCache.keySet();
        set.addAll(mMap.keySet());
        return set;
    }

    @Override
    public void clear() {
        mCache.clear();
        mMap.clear();
    }
}
