package com.kaolafm.opensdk.player.core.utils;

import android.content.Context;

import com.kaolafm.opensdk.player.core.ijk.IJKMediaPlayerAdapter;
import com.kaolafm.opensdk.player.core.model.AMediaPlayer;

/**
 * <AUTHOR> on 2019-05-24.
 */

public class ContextMediaPlayer {
    public static final int TYPE_IJK_MEDIA_PLAYER = 1;
    public static final int TYPE_EXO_MEDIA_PLAYER = 2;
    private AMediaPlayer mediaPlayer;


    public void initPlayer(int type, Context context) {
        if (TYPE_IJK_MEDIA_PLAYER == type) {
            mediaPlayer = new IJKMediaPlayerAdapter();
        } else {
           // mediaPlayer = new ExoMediaPlayerAdapter(context);
        }
    }

    public AMediaPlayer getMediaPlayer() {
        return mediaPlayer;
    }
}
