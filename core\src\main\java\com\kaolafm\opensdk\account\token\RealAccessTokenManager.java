package com.kaolafm.opensdk.account.token;

import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.qualifier.AccessTokenQualifier;
import com.kaolafm.opensdk.di.scope.AppScope;

import java.util.Map;
import java.util.Map.Entry;

import javax.inject.Inject;

/**
 * token 真正的管理类。
 * <AUTHOR>
 * @date 2020-02-24
 */
@AppScope
public class RealAccessTokenManager {

    public static final String TOKEN_KAOLA = "token_kaola";

    @Inject
    @AccessTokenQualifier
    Map<String, TokenCache> mTokenCaches;

    @Inject
    public RealAccessTokenManager() {
        ComponentKit.getInstance().inject(this);
    }

    /**
     * 加载所有token缓存。内部使用，开发者不需要调用该方法。
     */
    public void loadCurrentAccessToken() {
        if (mTokenCaches != null && !mTokenCaches.isEmpty()) {
            for (Entry<String, TokenCache> entry : mTokenCaches.entrySet()){
                TokenCache<AccessToken> tokenCache = entry.getValue();
                if (tokenCache != null) {
                    tokenCache.load();
                }
            }
        }
    }

    /**
     * 退出指定账号。
     * @param type 要退出的账号类型
     */
    public void logout(String type) {
        TokenCache tokenCache = mTokenCaches.get(type);
        if (tokenCache != null) {
            tokenCache.logout();
        }
    }

    /**
     * 退出所有账户
     */
    public void logoutAll() {
        if (mTokenCaches != null && !mTokenCaches.isEmpty()) {
            for (Entry<String, TokenCache> entry : mTokenCaches.entrySet()){
                TokenCache tokenCache = entry.getValue();
                if (tokenCache != null) {
                    tokenCache.logout();
                }
            }
        }
    }

    /**
     * 清除所有数据
     */
    public void clearAll() {
        if (mTokenCaches != null && !mTokenCaches.isEmpty()) {
            for (Entry<String, TokenCache> entry : mTokenCaches.entrySet()){
                TokenCache tokenCache = entry.getValue();
                if (tokenCache != null) {
                    tokenCache.clear();
                }
            }
        }
    }

    /**
     * 清除指定账户数据
     * @param type
     */
    public void clear(String type) {
        TokenCache tokenCache = mTokenCaches.get(type);
        if (tokenCache != null) {
            tokenCache.clear();
        }
    }


    /**
     * 保存token
     * @param token
     */
    public void setCurrentAccessToken(AccessToken token) {
        if (mTokenCaches != null && !mTokenCaches.isEmpty()) {
            for (Entry<String, TokenCache> entry : mTokenCaches.entrySet()){
                TokenCache<AccessToken> tokenCache = entry.getValue();
                if (tokenCache.accept(token)) {
                    tokenCache.save(token);
                }
            }
        }
    }

    /**
     * 获取指定账号类型的token
     * @param type 账号类型
     * @return
     */
    public <T extends AccessToken> T getCurrentAccessToken(String type) {
        if (mTokenCaches != null) {
            TokenCache<AccessToken> tokenCache = mTokenCaches.get(type);
            if (tokenCache != null) {
                return (T) tokenCache.getToken();
            }
        }
        return null;
    }

    /**
     * 获取考拉账号的Token。其实是K-radio账号的token。
     *
     * @return
     */
    public KaolaAccessToken getKaolaAccessToken() {
        KaolaAccessToken accessToken = getCurrentAccessToken(RealAccessTokenManager.TOKEN_KAOLA);
        return accessToken == null ? new KaolaAccessToken() : accessToken;
    }

    // TODO: 2020-03-02 可以优化兼容其他token
    /**
     * 注册监听token的变化
     * @param observer
     */
    public void registerObserver(TingbanTokenObserver observer) {
        if (observer != null) {
            TokenMonitor tokenCache = (KaolaAccessTokenCache) mTokenCaches.get(TOKEN_KAOLA);
            tokenCache.registerObserver(observer);
        }
    }

    /**
     * 注销token变化监听
     * @param observer
     */
    public void unregisterObserver(TingbanTokenObserver observer) {
        if (observer != null) {
            TokenMonitor tokenCache = (KaolaAccessTokenCache) mTokenCaches.get(TOKEN_KAOLA);
            tokenCache.unregisterObserver(observer);
        }
    }
}
