package com.kaolafm.opensdk.api.activity;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.HostConstant;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.activity.model.Activity;

import java.util.List;

import io.reactivex.Single;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

public interface ActivityService {

    /**
     * 活动列表
     * @return 活动列表
     */
    @Headers({HostConstant.DOMAIN_HEADER_OPEN_KAOLA})
    @GET(KaolaApiConstant.GET_ACTIVITY_INFO_LIST)
    Single<BaseResult<BasePageResult<List<Activity>>>> getInfoList(@Query("appid")String appid);

    @Headers({HostConstant.DOMAIN_HEADER_OPEN_KAOLA})
    @GET(KaolaApiConstant.GET_ACTIVITY_INFO_LIST)
    Call<BaseResult<BasePageResult<List<Activity>>>> getInfoListSync(@Query("appid")String appid);
}
