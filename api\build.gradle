apply plugin: 'com.android.library'
apply plugin: 'build-jar'
apply plugin: 'org.greenrobot.greendao'
def and = rootProject.ext.android
def dependent = rootProject.ext.dependencies
def maven = rootProject.ext.maven

def VERSION_CODE = and.versionCode
def VERSION_NAME = and.versionName

android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion
    defaultConfig {
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode VERSION_CODE
        versionName VERSION_NAME
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"

    }

    buildTypes {
        release {
            minifyEnabled false
            //Zipalign优化
            zipAlignEnabled false
        }
        debug {
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }

    compileOptions {
        sourceCompatibility and.javaSourceVersion
        targetCompatibility and.javaTargetVersion
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    packagingOptions {
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
    }
    //配置数据库相关信息
    greendao {
        schemaVersion 1 //数据库版本号
        daoPackage 'com.kaolafm.opensdk.db.greendao'
        targetGenDir 'src/main/java'//设置DaoMaster、DaoSession、Dao目录
    }

}

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'
    annotationProcessor dependent["dagger2-compiler"]
    implementation project(":core")
    embed project(path:":report", configuration:"default")
    compileOnly(dependent["rxlifecycle2-components"]) {
        exclude module: 'rxjava'
        exclude module: 'appcompat-v7'
        exclude module: 'rxandroid'
        exclude module: 'support-annotations'
    }
}




