package com.kaolafm.opensdk.api.operation.model.column;

import com.kaolafm.opensdk.api.operation.model.ImageFile;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 栏目组.类目组只有子栏目
 */
public class ColumnGrp implements Serializable {

    /** 栏目组/栏目的code，用于请求栏目组/栏目下的子分类/分类成员。该值是可变的。*/
    private String code;

    /** 标题*/
    private String title;

    /** 副标题*/
    private String subtitle;

    /** 描述*/
    private String description;

    /** 图片信息集合*/
    private Map<String, ImageFile> imageFiles;

    /** 额外信息，用于一些定制需求*/
    private Map<String, String> extInfo;

    /** SDK内部使用，开发者不需要关心，会一直为空*/
    private String type;

    /** 子栏目列表*/
    private List<? extends ColumnGrp> childColumns;

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Map<String, String> getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(Map<String, String> extInfo) {
        this.extInfo = extInfo;
    }

    public Map<String, ImageFile> getImageFiles() {
        return imageFiles;
    }

    public void setImageFiles(Map<String, ImageFile> imageFiles) {
        this.imageFiles = imageFiles;
    }

    public List<? extends ColumnGrp> getChildColumns() {
        return childColumns;
    }

    public void setChildColumns(List<? extends ColumnGrp> childColumns) {
        this.childColumns = childColumns;
    }

    @Override
    public String toString() {
        return "ColumnGrp{" +
                "code='" + code + '\'' +
                ", title='" + title + '\'' +
                ", subtitle='" + subtitle + '\'' +
                ", description='" + description + '\'' +
                ", imageFiles=" + imageFiles +
                ", extInfo=" + extInfo +
                ", type='" + type + '\'' +
                ", childColumns=" + childColumns +
                '}';
    }
}
