<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <android.support.constraint.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_search_semantics_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="语义搜索" />


        <TextView
            android:id="@+id/tv_search_field_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="7dp"
            android:text="场景*："
            app:layout_constraintTop_toBottomOf="@id/tv_search_semantics_title" />

        <RadioGroup
            android:id="@+id/rg_search_field"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintStart_toEndOf="@id/tv_search_field_title"
            app:layout_constraintTop_toTopOf="@id/tv_search_field_title">

            <RadioButton
                android:id="@+id/rb_search_field_music"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="音乐" />

            <RadioButton
                android:id="@+id/rb_search_field_integrate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                android:text="综合" />

            <RadioButton
                android:id="@+id/rb_search_field_broadcast"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                android:text="在线广播" />

        </RadioGroup>

        <EditText
            android:id="@+id/et_search_artist"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:hint="请输入艺术家"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@+id/et_search_audio_name"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/et_search_voice_origin_text" />

        <EditText
            android:id="@+id/et_search_audio_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:hint="请输入音频名(歌曲名)"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/et_search_artist"
            app:layout_constraintTop_toBottomOf="@id/et_search_voice_origin_text" />

        <EditText
            android:id="@+id/et_search_album_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:hint="请输入专辑名称"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@+id/et_search_category"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/et_search_artist" />

        <EditText
            android:id="@+id/et_search_category"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:hint="请输入分类"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/et_search_album_name"
            app:layout_constraintTop_toTopOf="@id/et_search_album_name" />

        <EditText
            android:id="@+id/et_search_voice_source"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="请输入音源公司缩写"
            android:textSize="12sp"
            android:text="0"
            app:layout_constraintTop_toBottomOf="@id/rg_search_field" />

        <EditText
            android:id="@+id/et_search_semantics_keyword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="请输入关键词*,多个关键词以英文逗号“,”分"
            android:textSize="12sp"
            app:layout_constraintTop_toBottomOf="@id/et_search_voice_source" />

        <EditText
            android:id="@+id/et_search_voice_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="请输入用户声控的原始串*"
            android:textSize="12sp"
            app:layout_constraintTop_toBottomOf="@id/et_search_semantics_keyword" />

        <EditText
            android:id="@+id/et_search_voice_origin_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="请输入语音商返回的原始json字符串"
            android:textSize="12sp"
            app:layout_constraintTop_toBottomOf="@id/et_search_voice_text" />

        <TextView
            android:id="@+id/tv_search_semantics_commit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/colorAccent"
            android:paddingBottom="5dp"
            android:paddingEnd="20dp"
            android:paddingStart="20dp"
            android:paddingTop="5dp"
            android:text="搜索"
            android:textColor="@android:color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/et_search_album_name" />

    </android.support.constraint.ConstraintLayout>
</ScrollView>
