package com.kaolafm.opensdk.api.media;

import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.HostConstant;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.media.model.AudioPlayInfo;

import java.util.List;

import io.reactivex.Single;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Query;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: AudioService.java                                               
 *                                                                  *
 * Created in 2018/8/13 上午10:16                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
/*package*/ interface AudioService {

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.GET_AUDIO_DETAILS_SINGLE)
    Single<BaseResult<AudioDetails>> getAudioDetails(@Query("id") long id);

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.GET_AUDIO_PLAYINFO_SINGLE)
    Single<BaseResult<AudioPlayInfo>> getAudioPlayInfo(@Query("playUrlId") String playUrlId);

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.GET_AUDIO_DETAILS_MULTIPLE)
    Single<BaseResult<List<AudioDetails>>> getAudioDetails(@Query("ids") String id);

    @Headers(HostConstant.DOMAIN_HEADER_OPEN_KAOLA)
    @GET(KaolaApiConstant.GET_CURRENT_CLOCK_AUDIO)
    Single<BaseResult<AudioDetails>> getCurrentClockAudio();
}
