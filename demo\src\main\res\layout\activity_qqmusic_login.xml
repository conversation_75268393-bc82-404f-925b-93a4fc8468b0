<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <WebView
        android:id="@+id/web_qqmusic_qr_code"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@null"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/iv_qqmusic_user_image"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginTop="10dp"
        android:scaleType="centerCrop"
        app:layout_constraintTop_toBottomOf="@id/web_qqmusic_qr_code" />

    <TextView
        android:id="@+id/tv_qqmusic_user_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:lineSpacingExtra="5dp"
        android:paddingStart="10dp"
        android:scrollbars="vertical"
        android:textColor="@color/colorAccent"
        android:textSize="10sp"
        app:layout_constraintStart_toEndOf="@id/iv_qqmusic_user_image"
        app:layout_constraintTop_toBottomOf="@id/web_qqmusic_qr_code" />
    <Button
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="退出登录"
        android:onClick="logout"
        app:layout_constraintTop_toBottomOf="@id/iv_qqmusic_user_image"
        />

</android.support.constraint.ConstraintLayout>
