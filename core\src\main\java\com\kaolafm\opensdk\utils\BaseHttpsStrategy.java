package com.kaolafm.opensdk.utils;

import java.util.HashMap;
import java.util.Map;

public abstract class BaseHttpsStrategy {
    public static final String GET_HOST = "getHost";
    public static final String REPLACE_URL = "replaceUrl";
    public static final String AD_PARAM = "adParam";
    public static final String MEDIA = "media";
    protected Map<String,Boolean> mHttpsMap = new HashMap<>();

    public BaseHttpsStrategy(){
        initDefaultMap();
        updateChannelHttpsStrategy();
    }

    private void initDefaultMap() {
        mHttpsMap.put(GET_HOST,true);
        mHttpsMap.put(REPLACE_URL,true);
        mHttpsMap.put(AD_PARAM,true);
        mHttpsMap.put(MEDIA,false);
    }

    public boolean isHttps(String key){
        if(mHttpsMap!=null)
            return mHttpsMap.get(key);
        return true;
    }

    public abstract void updateChannelHttpsStrategy();

    public String printMap(){
        return mHttpsMap.toString();
    }
}
