package com.kaolafm.opensdk.demo.purchase;

import android.content.Intent;
import android.os.Bundle;
import android.support.v7.widget.DividerItemDecoration;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.view.View;

import com.kaolafm.opensdk.api.purchase.PurchaseRequest;
import com.kaolafm.opensdk.api.purchase.model.VipMeals;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.BaseAdapter;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

/**
 * vip套餐
 *
 */
public class VipMealsActivity extends CommonActivity {

    @Override
    public void initView(Bundle savedInstanceState) {
        super.initView(savedInstanceState);
        setTitle("Vip套餐");
    }

    @Override
    public void initData() {
        new PurchaseRequest().getVipMeats(new HttpCallback<List<VipMeals>>() {
            @Override
            public void onSuccess(List<VipMeals> vipMeats) {
                if (adapter != null) {
                    List<Object> list = new ArrayList<>();
                    for (VipMeals i : vipMeats) {
                        list.add(i);
                    }
                    adapter.setDataList(list);
                    adapter.setOnItemClickListener((view, viewType, o, position) -> {
                        Intent intent = new Intent(VipMealsActivity.this, VipQRCodeActivity.class);
                        VipMeals meals = (VipMeals)o;
                        intent.putExtra(VipQRCodeActivity.KEY_MEAL_ID, String.valueOf(meals.getMealId()));
                        intent.putExtra(VipQRCodeActivity.KEY_PRICE, String.valueOf(meals.getDiscountPrice()));
                        startActivity(intent);
                    });
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取vip套餐列表失败", exception);
            }
        });
    }
}
