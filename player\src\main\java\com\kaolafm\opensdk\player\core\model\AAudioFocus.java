package com.kaolafm.opensdk.player.core.model;


import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInner;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

/**
 * <AUTHOR> on 2019-06-12.
 */

public abstract class AAudioFocus {
    private OnAudioFocusChangeInner mIAudioFocusListener;
    private int mFocusState;

    /**
     * 请求焦点
     */
    public abstract boolean requestAudioFocus();

    /**
     * 主动放弃音频焦点
     */
    public abstract boolean abandonAudioFocus();

    /**
     * 获取音频焦点状态
     *
     * @return
     */
    public final int getAudioFocusState() {
        return mFocusState;
    }

    /**
     * 设置监听器
     *
     * @param iAudioFocusListener
     */
    public final void setAudioFocusListener(OnAudioFocusChangeInner iAudioFocusListener) {
        mIAudioFocusListener = iAudioFocusListener;
    }

    /**
     * 通知焦点改变
     *
     * @param change
     */
    protected final void notifyAudioFocusChange(boolean isUseBySystem, int change) {
        PlayerLogUtil.log(getClass().getSimpleName(), "notifyAudioFocusChange", "isUseBySystem = " + isUseBySystem + ", change = " + change);
        mFocusState = change;
        if (mIAudioFocusListener != null) {
            mIAudioFocusListener.onAudioFocusChange(isUseBySystem, change);
        }
    }

}
