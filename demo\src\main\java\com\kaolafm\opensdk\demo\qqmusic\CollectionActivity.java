package com.kaolafm.opensdk.demo.qqmusic;

import android.content.Intent;
import android.os.Bundle;
import android.support.v7.app.AlertDialog.Builder;
import android.support.v7.widget.DividerItemDecoration;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.util.Log;

import com.kaolafm.opensdk.api.music.qq.QQMusicRequest;
import com.kaolafm.opensdk.api.music.qq.model.SongMenu;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.List;

import butterknife.BindView;

/**
 * QQ音乐收藏页面
 *
 * <AUTHOR>
 * @date 2018/10/26
 */

public class CollectionActivity extends BaseActivity {

    @BindView(R.id.rv_collection_content)
    RecyclerView mRvCollectionContent;

    private CollectionAdapter mCollectionAdapter;

    @Override
    public int getLayoutId() {
        return R.layout.activity_collection;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        mRvCollectionContent.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
        mRvCollectionContent.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
        mCollectionAdapter = new CollectionAdapter();
        mCollectionAdapter.setOnItemClickListener((view, viewType, songMenu, position) -> showOrPlay(songMenu));
        mRvCollectionContent.setAdapter(mCollectionAdapter);
        setTitle("QQ音乐收藏歌单");
    }

    private void showOrPlay(SongMenu songMenu) {
        new Builder(this)
                .setSingleChoiceItems(R.array.dialog_play_or_show, -1, (dialog, which) -> {
                    switch (which) {
                        case 0:
                            Intent intent = new Intent(CollectionActivity.this, CollectionListActivity.class);
                            intent.putExtra(CollectionListActivity.SONGMENU_ID, songMenu.getDissId());
                            intent.putExtra(CollectionListActivity.SONGMENU_NAME, songMenu.getDissName());
                            CollectionActivity.this.startActivity(intent);
                            break;
                        case 1:
//                            MusicPlayerManager.getInstance().playMyLike(songMenu.getDissId(), "");
                            break;
                        default:
                            break;
                    }
                    dialog.dismiss();
                })
                .create().show();
    }

    @Override
    public void initData() {
        new QQMusicRequest().getSelfSongMenuList(new HttpCallback<List<SongMenu>>() {
            @Override
            public void onSuccess(List<SongMenu> songMenus) {
                mCollectionAdapter.setDataList(songMenus);
            }

            @Override
            public void onError(ApiException exception) {
                Log.e("CollectionActivity", "onError: " + exception);
            }
        });
    }

}
