package com.kaolafm.opensdk.api.purchase.model;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;

/**
 * 已购内容
 */
@Entity
public class PurchasedItem {

    private long id;

    /* 专辑名称 */
    private String name;

    /* 专辑封面 */
    private String img;

    /* 是否付费 */
    private int fine;

    /* 是否vip */
    private int vip;

    /**
     * 是否已经上线 （1是，0否）
     */
    private int online = 1;

    /* 购买时间 注意页面展示使用 DateUtil.getDisTimeStr */
    private Long createTime;

    @Generated(hash = 2128829952)
    public PurchasedItem(long id, String name, String img, int fine, int vip,
                         int online, Long createTime) {
        this.id = id;
        this.name = name;
        this.img = img;
        this.fine = fine;
        this.vip = vip;
        this.online = online;
        this.createTime = createTime;
    }

    @Generated(hash = 279191257)
    public PurchasedItem() {
    }


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getFine() {
        return fine;
    }

    public void setFine(int fine) {
        this.fine = fine;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }


    public int getOnline() {
        return this.online;
    }

    public void setOnline(int online) {
        this.online = online;
    }

    @Override
    public String toString() {
        return "PurchasedItem{" +
                ", id=" + id +
                ", name='" + name + '\'' +
                ", fine=" + fine +
                ", vip=" + vip +
                ", img='" + img + '\'' +
                ", online='" + online + '\'' +
                ", createTime='" + createTime + '\'' +
                '}';
    }


}
