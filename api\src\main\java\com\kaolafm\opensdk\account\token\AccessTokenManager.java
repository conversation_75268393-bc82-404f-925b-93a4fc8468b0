package com.kaolafm.opensdk.account.token;

import android.util.Log;

import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.component.SessionComponent;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.ReportHelper;

import javax.inject.Inject;

/**
 * token 管理类
 *
 * <AUTHOR>
 * @date 2018/7/27
 */
public class AccessTokenManager {

    private static final String TAG = "AccessTokenManager";

    public static final String TOKEN_QQMUSIC = "token_qq_music";

    private static volatile AccessTokenManager sInstance;

    @Inject
    @AppScope
    RealAccessTokenManager mRealManager;

    private AccessTokenManager() {
        Log.d(TAG, "AccessTokenManager constructor called");
        SessionComponent subcomponent = ComponentKit.getInstance().getSubcomponent();
        Log.d(TAG, "SessionComponent: " + (subcomponent != null ? "not null" : "null"));
        if (subcomponent != null) {
            subcomponent.inject(this);
            Log.d(TAG, "After injection, mRealManager: " + (mRealManager != null ? "not null" : "null"));
        } else {
            Log.w(TAG, "SessionComponent为NULL，MrealManager不会被注入");
        }
    }

    public static AccessTokenManager getInstance() {
        if (sInstance == null) {
            synchronized (AccessTokenManager.class) {
                if (sInstance == null) {
                    sInstance = new AccessTokenManager();
                }
            }
        }
        return sInstance;
    }

    /**
     * 退出指定账号。
     *
     * @param type 要退出的账号类型
     */
    public void logout(String type) {
        mRealManager.logout(type);
    }

    /**
     * 退出所有账户
     */
    public void logoutAll() {
        mRealManager.logoutAll();
    }

    /**
     * 清除所有数据
     */
    public void clearAll() {
        mRealManager.clearAll();
    }

    /**
     * 清除指定账户数据
     *
     * @param type
     */
    public void clear(String type) {
        mRealManager.clear(type);
    }


    /**
     * 保存token
     *
     * @param token
     */
    public void setCurrentAccessToken(AccessToken token) {
        mRealManager.setCurrentAccessToken(token);
    }

    /**
     * 获取指定账号类型的token
     *
     * @param type 账号类型
     * @return
     */
    public <T extends AccessToken> T getCurrentAccessToken(String type) {
        return mRealManager.getCurrentAccessToken(type);
    }

    /**
     * 注册监听token的变化
     *
     * @param observer
     */
    public void registerObserver(TingbanTokenObserver observer) {
        Log.d(TAG, "registerObserver called, mRealManager: " + (mRealManager != null ? "not null" : "null"));
        if (mRealManager == null) {
            Log.w(TAG, "MrealManager无效的，尝试重新注入");
            SessionComponent subcomponent = ComponentKit.getInstance().getSubcomponent();
            Log.d(TAG, "Re-injection attempt - SessionComponent: " + (subcomponent != null ? "not null" : "null"));
            if (subcomponent != null) {
                subcomponent.inject(this);
                Log.d(TAG, "After re-injection, mRealManager: " + (mRealManager != null ? "not null" : "null"));
            }
        }

        if (mRealManager == null) {
            Log.w(TAG, "MrealManager仍然无效的。");
            return;
        }

        mRealManager.registerObserver(observer);
        Log.d(TAG, "观察者成功注册");
    }

    /**
     * 注销token变化监听
     *
     * @param observer
     */
    public void unregisterObserver(TingbanTokenObserver observer) {
        mRealManager.unregisterObserver(observer);
    }

    /**
     * 退出考拉账号，这个是退出本地缓存。
     * <br>
     * 如果要取消考拉账号的授权，使用{@link com.kaolafm.opensdk.api.login.LoginRequest#logoutYunting(HttpCallback)}
     */
    public void logoutKaola() {
        Logging.d("退出考拉账号");
        mRealManager.logout(RealAccessTokenManager.TOKEN_KAOLA);
        ReportHelper.getInstance().initUid(null);
    }

    /**
     * 退出QQ音乐账号。
     */
    public void logoutQQMusic() {
        mRealManager.logout(TOKEN_QQMUSIC);
    }

    /**
     * 获取考拉账号的Token。其实是K-radio账号的token。
     *
     * @return
     */
    public KaolaAccessToken getKaolaAccessToken() {
        Log.i(TAG, "getKaolaAccessToken() --- (mRealManager != null) is " + (mRealManager != null));
        KaolaAccessToken result;
        if (mRealManager == null) {
            result = null;
        } else {
            result = mRealManager.getKaolaAccessToken();
        }
        if (result == null){
            // fix monkey中的Crash
            //  java.lang.NullPointerException: Attempt to invoke virtual method 'java.lang.String com.kaolafm.opensdk.account.token.KaolaAccessToken.getOpenId()' on a null object reference
            //      at com.kaolafm.opensdk.api.init.VerifyActivationImpl.isActivate(SourceFile:165)
            result = new KaolaAccessToken();
        }
        Log.i(TAG, "getKaolaAccessToken() --- (result != null) is " + (result != null));
        return result;
    }

    /**
     * 获取QQ音乐账号的Token
     *
     * @return
     */
    public QQMusicAccessToken getQQMusicAccessToken() {
        QQMusicAccessToken accessToken = mRealManager.getCurrentAccessToken(TOKEN_QQMUSIC);
        return accessToken != null ? accessToken : new QQMusicAccessToken();
    }
}
