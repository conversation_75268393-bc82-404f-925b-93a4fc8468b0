package com.kaolafm.opensdk.demo.live.chat;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.opensdk.demo.live.ui.LivePresenter;
import com.netease.nimlib.sdk.NIMClient;
import com.netease.nimlib.sdk.Observer;
import com.netease.nimlib.sdk.RequestCallbackWrapper;
import com.netease.nimlib.sdk.ResponseCode;
import com.netease.nimlib.sdk.chatroom.ChatRoomService;
import com.netease.nimlib.sdk.chatroom.ChatRoomServiceObserver;
import com.netease.nimlib.sdk.chatroom.constant.MemberQueryType;
import com.netease.nimlib.sdk.chatroom.constant.MemberType;
import com.netease.nimlib.sdk.chatroom.model.ChatRoomMember;
import com.netease.nimlib.sdk.chatroom.model.ChatRoomMessage;
import com.netease.nimlib.sdk.chatroom.model.ChatRoomNotificationAttachment;
import com.netease.nimlib.sdk.msg.constant.MsgTypeEnum;
import com.netease.nimlib.sdk.msg.constant.NotificationType;
import com.netease.nimlib.sdk.msg.model.IMMessage;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class ChatRoomMemberCache {

    private ChatRoomMemberCache() {
    }

    public static ChatRoomMemberCache getInstance() {
        return InstanceHolder.instance;
    }

    private Map<String, Map<String, ChatRoomMember>> cache = new HashMap<>();

    private Map<String, List<FetchChatRoomMemberCallback<ChatRoomMember>>> frequencyLimitCache = new HashMap<>(); // 重复请求处理

    private ArrayList<RoomMemberChangedObserver> roomMemberChangedObservers = new ArrayList<>();

    public void clear() {
        cache.clear();
        frequencyLimitCache.clear();
        roomMemberChangedObservers.clear();
    }

    /**
     * 从缓存中清除某个用户
     *
     * @param roomId
     * @param account
     */
    public void deleteRoomMemberCache(String roomId, String account) {
        if (cache.containsKey(roomId)) {
            Map<String, ChatRoomMember> chatRoomMemberMap = cache.get(roomId);
            chatRoomMemberMap.remove(account);
        }
    }

    public ChatRoomMember getChatRoomMember(String roomId, String account) {
        if (cache.containsKey(roomId)) {
            return cache.get(roomId).get(account);
        }

        return null;
    }

    /**
     * 根据roomId获取当前缓存中的成员信息
     *
     * @param roomId
     * @return
     */
    public List<ChatRoomMember> getChatRoomMemberByRoomId(String roomId) { // 这里其实想做MemberQueryType区分，但是云信的写法真的很蛋疼。。。参考MemberType与MemberQueryType定义即可明白
        List<ChatRoomMember> chatRoomMembers = null;
        if (cache.containsKey(roomId)) {
            chatRoomMembers = new ArrayList<>();
            HashMap<String, ChatRoomMember> chatRoomMemberHashMap = (HashMap<String, ChatRoomMember>) cache.get(roomId);
            Iterator it = chatRoomMemberHashMap.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<String, ChatRoomMember> entry = (Map.Entry<String, ChatRoomMember>) it.next();
                ChatRoomMember chatRoomMember = entry.getValue();
                if (chatRoomMember == null) {
                    continue;
                }
                chatRoomMembers.add(chatRoomMember);
            }
        }
        return chatRoomMembers;
    }

    /**
     * 获取当前聊天室已经被禁言的用户列表
     *
     * @param roomId
     * @return
     */
    public ArrayList<ChatRoomMember> getChatRoomMuteMember(String roomId) {
        if (cache.containsKey(roomId)) {
            Map<String, ChatRoomMember> chatRoomMemberMap = cache.get(roomId);
            if (chatRoomMemberMap == null || chatRoomMemberMap.size() == 0) {
                return null;
            }
            Iterator iterator = chatRoomMemberMap.entrySet().iterator();
            ArrayList<ChatRoomMember> chatRoomMembers = new ArrayList<>();
            while (iterator.hasNext()) {
                Map.Entry entry = (Map.Entry) iterator.next();
                ChatRoomMember val = (ChatRoomMember) entry.getValue();
                if (val == null || !val.isMuted()) {
                    continue;
                }
                chatRoomMembers.add(val);
            }
            return chatRoomMembers;
        }
        return null;
    }

    public void saveMyMember(ChatRoomMember chatRoomMember) {
        saveMember(chatRoomMember);
    }

    /**
     * 从服务器获取聊天室成员资料（去重处理）（异步）
     */
    public void fetchMember(final String roomId, final String account, final FetchChatRoomMemberCallback<ChatRoomMember> callback) {
        if (TextUtils.isEmpty(roomId) || TextUtils.isEmpty(account)) {
            if (callback != null) {
                callback.onResult(false, null);
            }
            return;
        }

        // 频率控制
        if (frequencyLimitCache.containsKey(account)) {
            if (callback != null) {
                frequencyLimitCache.get(account).add(callback);
            }
            return; // 已经在请求中，不要重复请求
        } else {
            List<FetchChatRoomMemberCallback<ChatRoomMember>> cbs = new ArrayList<>();
            if (callback != null) {
                cbs.add(callback);
            }
            frequencyLimitCache.put(account, cbs);
        }

        // fetch
        List<String> accounts = new ArrayList<>(1);
        accounts.add(account);
        NIMClient.getService(ChatRoomService.class).fetchRoomMembersByIds(roomId, accounts).setCallback(new RequestCallbackWrapper<List<ChatRoomMember>>() {
            @Override
            public void onResult(int code, List<ChatRoomMember> members, Throwable exception) {
                ChatRoomMember member = null;
                boolean hasCallback = false;
                if (frequencyLimitCache.get(account) != null) {
                    hasCallback = !frequencyLimitCache.get(account).isEmpty();
                }

                boolean success = code == ResponseCode.RES_SUCCESS && members != null && !members.isEmpty();
                // cache
                if (success) {
                    saveMembers(members);
                    member = members.get(0);
                } else {
                }
                // callback
                if (hasCallback) {
                    List<FetchChatRoomMemberCallback<ChatRoomMember>> cbs = frequencyLimitCache.get(account);
                    for (int i = 0, size = cbs.size(); i < size; i++) {
                        FetchChatRoomMemberCallback<ChatRoomMember> cb = cbs.get(i);
                        cb.onResult(success, member);
                    }
                }
                frequencyLimitCache.remove(account);
            }
        });
    }

    public void fetchRoomMembers(String roomId, MemberQueryType memberQueryType, long time, int limit,
                                 final FetchChatRoomMemberCallback<List<ChatRoomMember>> callback) {
        if (TextUtils.isEmpty(roomId)) {
            if (callback != null) {
                callback.onResult(false, null);
            }
            return;
        }
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(NimManager.TAG, "testNim----------------->fetchRoomMembers roomId = {}" + roomId);
        }
        NIMClient.getService(ChatRoomService.class).
                fetchRoomMembers(roomId, memberQueryType, time, limit).
                setCallback(new RequestCallbackWrapper<List<ChatRoomMember>>() {
                    @Override
                    public void onResult(int code, List<ChatRoomMember> result, Throwable exception) {
                        boolean success = code == ResponseCode.RES_SUCCESS;

                        if (success) {
                            saveMembers(result);
                        } else {
                        }

                        if (callback != null) {
                            callback.onResult(success, result);
                        }
                    }
                });
    }

    public void saveMember(ChatRoomMember member) {
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(NimManager.TAG, "testNim----------------->saveMember member = {}" + member);
        }
        if (member != null && !TextUtils.isEmpty(member.getRoomId()) && !TextUtils.isEmpty(member.getAccount())) {
            Map<String, ChatRoomMember> members = cache.get(member.getRoomId());

            if (members == null) {
                members = new HashMap<>();
                cache.put(member.getRoomId(), members);
            }

            members.put(member.getAccount(), member);
            HashMap<String, ChatRoomMember> chatRoomMemberHashMap = (HashMap<String, ChatRoomMember>) cache.get(member.getRoomId());
            if (LivePresenter.DEBUG_LIVE) {
                if (chatRoomMemberHashMap != null) {
                    Log.i(NimManager.TAG, "testNim----------------->saveMember member middle = {}" + chatRoomMemberHashMap.size());
                }
            }
        }
        if (LivePresenter.DEBUG_LIVE) {
            Log.i(NimManager.TAG, "testNim----------------->saveMember member end = {}" + cache.size());
        }
    }

    public void saveMembers(List<ChatRoomMember> members) {
        if (members == null || members.isEmpty()) {
            return;
        }

        for (int i = 0, size = members.size(); i < size; i++) {
            ChatRoomMember m = members.get(i);
            saveMember(m);
        }
    }

    /**
     * ************************************ 单例 ***************************************
     */
    private static class InstanceHolder {
        private final static ChatRoomMemberCache instance = new ChatRoomMemberCache();
    }

    /**
     * ********************************** 监听 ********************************
     */

    public void registerObservers() {
        NIMClient.getService(ChatRoomServiceObserver.class).observeReceiveMessage(incomingChatRoomMsg, true);
    }

    public void unRegisterObservers() {
        NIMClient.getService(ChatRoomServiceObserver.class).observeReceiveMessage(incomingChatRoomMsg, true);
    }

    private Observer<List<ChatRoomMessage>> incomingChatRoomMsg = new Observer<List<ChatRoomMessage>>() {
        @Override
        public void onEvent(List<ChatRoomMessage> messages) {
            if (messages == null || messages.isEmpty()) {
                return;
            }

            for (IMMessage msg : messages) {
                if (msg == null) {
                    continue;
                }

                if (msg.getMsgType() == MsgTypeEnum.notification) {
                    handleNotification(msg);
                }
            }
        }
    };

    private void handleNotification(IMMessage message) {
        if (message.getAttachment() == null) {
            return;
        }

        String roomId = message.getSessionId();
        ChatRoomNotificationAttachment attachment = (ChatRoomNotificationAttachment) message.getAttachment();
        List<String> targets = attachment.getTargets();
        if (targets != null) {
            for (String target : targets) {
                ChatRoomMember member = getChatRoomMember(roomId, target);
                handleMemberChanged(attachment.getType(), member);
            }
        }
    }

    private void handleMemberChanged(NotificationType type, ChatRoomMember member) {
        if (member == null) {
            return;
        }

        switch (type) {
            case ChatRoomMemberIn:
                for (RoomMemberChangedObserver o : roomMemberChangedObservers) {
                    o.onRoomMemberIn(member);
                }
                break;
            case ChatRoomMemberExit:
                for (RoomMemberChangedObserver o : roomMemberChangedObservers) {
                    o.onRoomMemberExit(member);
                }
                break;
            case ChatRoomManagerAdd:
                member.setMemberType(MemberType.ADMIN);
                break;
            case ChatRoomManagerRemove:
                member.setMemberType(MemberType.NORMAL);
                break;
            case ChatRoomMemberBlackAdd:
                member.setInBlackList(true);
                break;
            case ChatRoomMemberBlackRemove:
                member.setInBlackList(false);
                break;
            case ChatRoomMemberMuteAdd:
                member.setMuted(true);
                break;
            case ChatRoomMemberMuteRemove:
                member.setMuted(false);
                member.setMemberType(MemberType.GUEST);
                break;
            case ChatRoomCommonAdd:
                member.setMemberType(MemberType.NORMAL);
                break;
            case ChatRoomCommonRemove:
                member.setMemberType(MemberType.GUEST);
                break;
            default:
                break;
        }

        saveMember(member);
    }

    /**
     * ************************** 在线用户变化通知 ****************************
     */

    public interface RoomMemberChangedObserver {
        void onRoomMemberIn(ChatRoomMember member);

        void onRoomMemberExit(ChatRoomMember member);
    }

    public void registerRoomMemberChangedObserver(RoomMemberChangedObserver o, boolean register) {
        if (o == null) {
            return;
        }

        if (register) {
            if (!roomMemberChangedObservers.contains(o)) {
                roomMemberChangedObservers.add(o);
            }
        } else {
            roomMemberChangedObservers.remove(o);
        }
    }
}