package com.kaolafm.ad.expose;

import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.AdvertisingDetails;
import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.di.component.AdvertSubcomponent;
import com.kaolafm.ad.di.qualifier.AdvertAdapterQualifier;
import com.kaolafm.ad.util.AdBeanUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

/**
 * 广告管理类
 *
 * <AUTHOR>
 * @date 2020-01-09
 */
public class AdvertisingManager {

    private static final String IMAGER = "Imager";

    private static final String PLAYER = "Player";

    private static final String COMPOSITE = "Composite";

    private static volatile AdvertisingManager mInstance;

    private AdvertisingReporter mReporter;

    private List<AdvertisingLifecycleCallback> mLifecycleCallbacks;

    private List<AdvertInterceptor> mInterceptors = new ArrayList<>();

    @Inject
    @AdvertAdapterQualifier
    Map<String, Adapter> mAdapters;

    @Inject
    @AppScope
    AdvertRepository mAdvertRepository;

    private AdvertisingManager() {
        AdvertSubcomponent subcomponent = ComponentKit.getInstance().getSubcomponent();
        if (subcomponent != null) {
            subcomponent.inject(this);
        }
    }

    public static AdvertisingManager getInstance() {
        if (mInstance == null) {
            synchronized (AdvertisingManager.class) {
                if (mInstance == null) {
                    mInstance = new AdvertisingManager();
                }
            }
        }
        return mInstance;
    }

    public void addInterceptor(AdvertInterceptor interceptor) {
        if (interceptor != null && !mInterceptors.contains(interceptor)) {
            mInterceptors.add(interceptor);
        }
    }

    public void removeInterceptor(AdvertInterceptor interceptor){
        if(interceptor != null && mInterceptors != null && mInterceptors.contains(interceptor)){
            mInterceptors.remove(interceptor);
        }
    }

    /**
     * 曝光广告
     *
     * @param advert 广告类。通过广告接口获取。
     */
    public void expose(Advert advert) {
        AdvertExposeChain chain = new AdvertExposeChain(this);
        chain.addInterceptors(mInterceptors);
        chain.addInterceptor(new RealExposeInterceptor());
        chain.process(advert);
    }


    /**
     * 曝光广告
     *
     * @param details 广告详情。通过广告接口的。
     */
    public void expose(AdvertisingDetails details) {
        Advert advert = AdBeanUtil.transform(details);
        expose(advert);
    }

    /**
     * 请求广告并曝光广告
     *
     * @param adZoneId        必填 广告位ID
     * @param subtype         选填 广告次级分类。对应于{@link Advert#subtype}
     * @param picWidth        必填 图片广告位的宽 可传多个 以逗号分隔 在请求包含图片信息的广告位时为必填。
     * @param picHeight       必填 图片广告位的高 可传多个 以逗号分隔 在请求包含图片信息的广告位时为必填。
     *                        对于图片分辨率参数的说明:
     *                        如需要1280×960和1920×1080的图片时 c_picWidth应传"1280,1920" c_picHeight应传"960,1080"
     * @param acceptedAdTypes 必填 多种类型逗号隔开。可接受广告类型，客户端（非SDK）必传。3音频广告（新）；4图片广告（新）；5音图广告（新）
     * @param advancedAttrs   选填 json格式，需要编码。用于精准投放广告或广告服务判断投放广告时机等。
     *                        通过
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getAdvanceAttrs(long, long, long, long, int)}、
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getAlbumAttrs(long, long, int)}、
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getRadioAttrs(long, long, int)}
     *                        获取就是已经处理好的，直接填入就可以了。
     */
    public void expose(String adZoneId,
                       int subtype,
                       String picWidth,
                       String picHeight,
                       String acceptedAdTypes,
                       String advancedAttrs) {
        dispatchAdvertCreate(adZoneId, subtype);
        mAdvertRepository.getAdvertisingList(adZoneId, picWidth, picHeight, acceptedAdTypes, advancedAttrs, new HttpCallback<List<Advert>>() {
            @Override
            public void onSuccess(List<Advert> detailsList) {
                if (!ListUtil.isEmpty(detailsList)) {
                    //曝光最后一个
                    Advert advert = detailsList.get(detailsList.size() - 1);
                    advert.setSubtype(subtype);
                    expose(advert);
                }
            }

            @Override
            public void onError(ApiException exception) {
                error(adZoneId, subtype, exception);
            }
        });
    }

    /**
     * 请求广告并曝光广告
     *
     * @param adZoneId        必填 广告位ID
     * @param picWidth        必填 图片广告位的宽 可传多个 以逗号分隔 在请求包含图片信息的广告位时为必填。
     * @param picHeight       必填 图片广告位的高 可传多个 以逗号分隔 在请求包含图片信息的广告位时为必填。
     *                        对于图片分辨率参数的说明:
     *                        如需要1280×960和1920×1080的图片时 c_picWidth应传"1280,1920" c_picHeight应传"960,1080"
     * @param acceptedAdTypes 必填 多种类型逗号隔开。可接受广告类型，客户端（非SDK）必传。3音频广告（新）；4图片广告（新）；5音图广告（新）
     * @param advancedAttrs   选填 json格式，需要编码。用于精准投放广告或广告服务判断投放广告时机等。
     *                        通过
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getAdvanceAttrs(long, long, long, long, int)}、
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getAlbumAttrs(long, long, int)}、
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getRadioAttrs(long, long, int)}
     *                        获取就是已经处理好的，直接填入就可以了。
     */
    public void expose(String adZoneId,
                       String picWidth,
                       String picHeight,
                       String acceptedAdTypes,
                       String advancedAttrs) {
        expose(adZoneId, -1, picWidth, picHeight, acceptedAdTypes, advancedAttrs);
    }

    /**
     * 曝光预加载广告
     *
     * @param adZoneId        必填 广告位ID
     * @param subtype         选填 广告次级类型。对应于{@link Advert#subtype}
     * @param picWidth        必填 图片广告位的宽 可传多个 以逗号分隔 在请求包含图片信息的广告位时为必填。
     * @param picHeight       必填 图片广告位的高 可传多个 以逗号分隔 在请求包含图片信息的广告位时为必填。
     *                        对于图片分辨率参数的说明:
     *                        如需要1280×960和1920×1080的图片时 c_picWidth应传"1280,1920" c_picHeight应传"960,1080"
     * @param acceptedAdTypes 必填 多种类型逗号隔开。可接受广告类型，客户端（非SDK）必传。3音频广告（新）；4图片广告（新）；5音图广告（新）
     * @param advancedAttrs   选填 json格式，需要编码。用于精准投放广告或广告服务判断投放广告时机等。
     *                        通过
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getAdvanceAttrs(long, long, long, long, int)}、
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getAlbumAttrs(long, long, int)}、
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getRadioAttrs(long, long, int)}
     *                        获取就是已经处理好的，直接填入就可以了。
     */
    public void exposePreloading(String adZoneId,
                                 int subtype,
                                 String picWidth,
                                 String picHeight,
                                 String acceptedAdTypes,
                                 String advancedAttrs) {
        dispatchAdvertCreate(adZoneId, subtype);
        mAdvertRepository.getPreloadingAdvert(adZoneId, subtype, picWidth, picHeight, acceptedAdTypes, advancedAttrs, new HttpCallback<Advert>() {
            @Override
            public void onSuccess(Advert advert) {
                expose(advert);
            }

            @Override
            public void onError(ApiException exception) {
                error(adZoneId, subtype, exception);
            }
        });
    }

    /**
     * 曝光预加载广告
     *
     * @param adZoneId        必填 广告位ID
     * @param picWidth        必填 图片广告位的宽 可传多个 以逗号分隔 在请求包含图片信息的广告位时为必填。
     * @param picHeight       必填 图片广告位的高 可传多个 以逗号分隔 在请求包含图片信息的广告位时为必填。
     *                        对于图片分辨率参数的说明:
     *                        如需要1280×960和1920×1080的图片时 c_picWidth应传"1280,1920" c_picHeight应传"960,1080"
     * @param acceptedAdTypes 必填 多种类型逗号隔开。可接受广告类型，客户端（非SDK）必传。3音频广告（新）；4图片广告（新）；5音图广告（新）
     * @param advancedAttrs   选填 json格式，需要编码。用于精准投放广告或广告服务判断投放广告时机等。
     *                        通过
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getAdvanceAttrs(long, long, long, long, int)}、
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getAlbumAttrs(long, long, int)}、
     *                        <p>
     *                        {@link com.kaolafm.ad.util.AdParamsUtil#getRadioAttrs(long, long, int)}
     *                        获取就是已经处理好的，直接填入就可以了。
     */
    public void exposePreloading(String adZoneId,
                                 String picWidth,
                                 String picHeight,
                                 String acceptedAdTypes,
                                 String advancedAttrs) {
        exposePreloading(adZoneId, -1, picWidth, picHeight, acceptedAdTypes, advancedAttrs);
    }

    /**
     * 关闭广告，并删除缓存(如果有缓存的话)
     *
     * @param advert 广告
     */
    public void close(Advert advert) {
        onlyClose(advert);
        clearCache(advert);
    }

    /**
     * 仅关闭，不删除缓存。
     *
     * @param advert
     */
    public void onlyClose(Advert advert) {
        if (advert != null) {
            for (Adapter adapter : mAdapters.values()) {
                if (adapter.accept(advert)) {
                    adapter.close(advert);
                }
            }
        } else {
            //这里需要注意组合广告，如果遍历需要去除组合广告，不然会调用多次。或者直接调用组合广告。
            //后面如果还有其他组合广告的话，或者组合广告不包括所有类型，就需要遍历非组合广告。
            mAdapters.get(COMPOSITE).close(null);
        }
        dispatchAdvertClose(advert);
    }

    /**
     * @param advert
     */
    public void clearCache(Advert advert) {
        mAdvertRepository.delete(advert);
    }

    /**
     * 关闭广告
     *
     * @param details 广告详情
     */
    public void close(AdvertisingDetails details) {
        close(AdBeanUtil.transform(details));
    }

    /**
     * 关闭所有广告
     */
    public void close() {
        close((Advert) null);
    }

    /**
     * 错误
     */
    public void error(String adZoneId, int subtype, ApiException e) {
        for (Adapter adapter : mAdapters.values()) {
            adapter.error(adZoneId, subtype, e);
        }
        dispatchAdvertError(adZoneId, subtype, e);
    }

    public AdvertisingImager getImager() {
        if (mAdapters == null || mAdapters.get(IMAGER) == null) {
            return null;
        }
        return (AdvertisingImager) mAdapters.get(IMAGER).getExecutor();
    }

    public void setImager(AdvertisingImager imager) {
        mAdapters.get(IMAGER).setExecutor(imager);
    }

    public AdvertisingPlayer getPlayer() {
        return (AdvertisingPlayer) mAdapters.get(PLAYER).getExecutor();
    }

    public void setPlayer(AdvertisingPlayer player) {
        mAdapters.get(PLAYER).setExecutor(player);
    }

    public AdvertisingReporter getReporter() {
        return mReporter;
    }

    public void setReporter(AdvertisingReporter reporter) {
        mReporter = reporter;
    }

    public void registerAdvertLifecycleCallback(AdvertisingLifecycleCallback callback) {
        if (mLifecycleCallbacks == null) {
            mLifecycleCallbacks = new ArrayList<>();
        }
        mLifecycleCallbacks.add(callback);
    }

    public void unregisterAdvertLifecycleCallback(AdvertisingLifecycleCallback callback) {
        if (mLifecycleCallbacks != null) {
            mLifecycleCallbacks.remove(callback);
        }
    }

    private void dispatchAdvertCreate(String adZoneId, int subtype) {
        if (mLifecycleCallbacks != null) {
            for (AdvertisingLifecycleCallback lifecycleCallback : mLifecycleCallbacks) {
                lifecycleCallback.onCreate(adZoneId, subtype);
            }
        }
    }

    private void dispatchAdvertStart(Advert advert) {
        if (mLifecycleCallbacks != null) {
            for (AdvertisingLifecycleCallback lifecycleCallback : mLifecycleCallbacks) {
                lifecycleCallback.onStart(advert);
            }
        }
    }

    private void dispatchAdvertClose(Advert advert) {
        if (mLifecycleCallbacks != null) {
            if (advert instanceof ImageAdvert && advert.getType() == 5) {
                //如果是音图广告中的图片广告就不走生命周期，等音频调用再走。
                return;
            }
            for (AdvertisingLifecycleCallback lifecycleCallback : mLifecycleCallbacks) {
                lifecycleCallback.onClose(advert);
            }
        }
    }

    void dispatchAdvertError(String adZoneId, int subtype, Exception e) {
        if (mLifecycleCallbacks != null) {
            for (AdvertisingLifecycleCallback lifecycleCallback : mLifecycleCallbacks) {
                lifecycleCallback.onError(adZoneId, subtype, e);
            }
        }
    }

    private class RealExposeInterceptor implements AdvertInterceptor {

        @Override
        public void intercept(Chain chain) {
            Advert advert = chain.advert();
            dispatchAdvertStart(advert);
            for (Adapter adapter : mAdapters.values()) {
                if (adapter.accept(advert)) {
                    adapter.expose(advert);
                }
            }
        }
    }
}
