apply plugin: 'com.android.application'
apply plugin: 'android-aspectjx'

def configAndroid = [
        compileSdkVersion            : 28,
        buildToolsVersion            : "28.0.3",
        minSdkVersion                : 17,
        targetSdkVersion             : 28,
        versionCode                  : 10600,
        versionName                  : "1.6.0",
        adVersionCode                : 10200,
        adVersionName                : "1.2.0",
        javaSourceVersion            : JavaVersion.VERSION_1_8,
        javaTargetVersion            : JavaVersion.VERSION_1_8
]
def version = [
        androidSupportSdkVersion     : "28.0.0",
        retrofitSdkVersion           : "2.4.0",
        butterknifeSdkVersion        : "9.0.0-SNAPSHOT",
        rxlifecycle2SdkVersion       : "2.2.1",
        canarySdkVersion             : "1.5.4",
        glideVersion                 : "4.6.1",
        dagger2Version               : "2.16"
]
def dependent = [
        "junit"                      : "junit:junit:4.12",
        "appcompat-v4"               : "com.android.support:support-v4:${version["androidSupportSdkVersion"]}",
        "appcompat-v7"               : "com.android.support:appcompat-v7:${version["androidSupportSdkVersion"]}",
        "constraint"                 : "com.android.support.constraint:constraint-layout:1.1.1",
        "design"                     : "com.android.support:design:${version["androidSupportSdkVersion"]}",
        "cardview"                   : "com.android.support:cardview-v7:${version["androidSupportSdkVersion"]}",
        "recyclerview-v7"            : "com.android.support:recyclerview-v7:${version["androidSupportSdkVersion"]}",
        "annotations"                : "com.android.support:support-annotations:${version["androidSupportSdkVersion"]}",
        "annotations-java5"          : "org.jetbrains:annotations-java5:15.0",
        "eventbus"                   : "org.greenrobot:eventbus:3.1.1",
        "butterknife"                : "com.jakewharton:butterknife:${version["butterknifeSdkVersion"]}",
        "butterknife-compiler"       : "com.jakewharton:butterknife-compiler:${version["butterknifeSdkVersion"]}",
        "butterknife-plugin"         : "com.jakewharton:butterknife-gradle-plugin:${version["butterknifeSdkVersion"]}",
        "retrofit2"                  : "com.squareup.retrofit2:retrofit:${version["retrofitSdkVersion"]}",
        "retrofit2-gson"             : "com.squareup.retrofit2:converter-gson:${version["retrofitSdkVersion"]}",
        "retrofit2-rxjava2"          : "com.squareup.retrofit2:adapter-rxjava2:${version["retrofitSdkVersion"]}",
        "disklrucache"               : "com.jakewharton:disklrucache:2.0.2",
        "okhttp3"                    : "com.squareup.okhttp3:okhttp:3.10.0",
        "okhttp3-log"                : "com.squareup.okhttp3:logging-interceptor:3.3.1",
        "rxandroid2"                 : "io.reactivex.rxjava2:rxandroid:2.0.2",
        "rxjava2"                    : "io.reactivex.rxjava2:rxjava:2.1.16",
        "rxlifecycle2"               : "com.trello.rxlifecycle2:rxlifecycle:${version["rxlifecycle2SdkVersion"]}",
        "rxlifecycle2-components"    : "com.trello.rxlifecycle2:rxlifecycle-components:${version["rxlifecycle2SdkVersion"]}",
        "rxcache2"                   : "com.github.VictorAlbertos.RxCache:runtime:1.8.3-2.x",
        "rxcache2-gson"              : "com.github.VictorAlbertos.Jolyglot:gson:0.0.4",
        "canary-debug"               : "com.squareup.leakcanary:leakcanary-android:${version["canarySdkVersion"]}",
        "canary-release"             : "com.squareup.leakcanary:leakcanary-android-no-op:${version["canarySdkVersion"]}",
        "multidex"                   : "com.android.support:multidex:1.0.2",
        "lottie"                     : "com.airbnb.android:lottie:2.5.0",
        "logger"                     : "com.orhanobut:logger:2.2.0",
        "glide"                      : "com.github.bumptech.glide:glide:${version["glideVersion"]}",
        "glide-compiler"             : "com.github.bumptech.glide:compiler:${version["glideVersion"]}",
        "gson"                       : "com.google.code.gson:gson:2.8.2",
        "greenDao"                   : "org.greenrobot:greendao:3.3.0",
        "arouter"                    : "com.alibaba:arouter-api:1.3.1",
        "arouter-compiler"           : "com.alibaba:arouter-compiler:1.1.4",
        "zxing"                      : "com.google.zxing:core:3.3.2",
        "bugly_crashreport"          : "com.tencent.bugly:crashreport:latest.release",
        "bugly_nativecrashreport"    : "com.tencent.bugly:nativecrashreport:latest.release",
        "dagger2"                    : "com.google.dagger:dagger:${version["dagger2Version"]}",
        "dagger2-compiler"           : "com.google.dagger:dagger-compiler:${version["dagger2Version"]}",
        "dagger2-android"            : "com.google.dagger:dagger-android:${version["dagger2Version"]}",
        "dagger2-android-support"    : "com.google.dagger:dagger-android-support:${version["dagger2Version"]}",
        "dagger2-android-processor"  : "com.google.dagger:dagger-android-processor:${version["dagger2Version"]}",
        "openSDK"                    : "com.kaolafm:open-sdk:1.6.0.32",
        "ad"                         : "com.kaolafm:ad:1.2.0.1"

]
android {
    compileSdkVersion configAndroid.compileSdkVersion
    buildToolsVersion configAndroid.buildToolsVersion
    defaultConfig {
        applicationId "com.kaolafm.sdk.demo"
        minSdkVersion configAndroid.minSdkVersion
        targetSdkVersion configAndroid.targetSdkVersion
        versionCode configAndroid.versionCode
        versionName configAndroid.versionName
        multiDexEnabled true
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"

        ndk {
            abiFilters 'armeabi', 'x86'
        }

    }
    //安装错误  解决
//    splits {
//        abi {
//            enable true
//            reset()
//            include 'x86', 'armeabi-v7a', 'x86_64'
//            universalApk true
//        }
//    }

    signingConfigs {
        release {
            storeFile file('demo.keystore')//签名文件路径，
            storePassword 'demo123' //密码
            keyAlias 'demo'
            keyPassword 'demodemo'  //密码
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }
    buildTypes {

        debug {
            testCoverageEnabled false
            minifyEnabled false
            buildConfigField "boolean", "INCLUDE_OLD_SDK", "true"
        }

        release {
            minifyEnabled false
            signingConfig signingConfigs.release
            buildConfigField "boolean", "INCLUDE_OLD_SDK", "true"
        }
    }
    compileOptions {
        sourceCompatibility configAndroid.javaSourceVersion
        targetCompatibility configAndroid.javaTargetVersion
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
    }


    configurations.all {
        resolutionStrategy.eachDependency { DependencyResolveDetails details ->
            def requested = details.requested
            if (requested.group == 'com.android.support') {
                if (!requested.name.startsWith("multidex")) {
                    details.useVersion '27.0.2'
                }
            }
        }
    }

    lintOptions {
        abortOnError false
    }
    def DEFAULT_FLAVOR_DIMENSION = 'kl_car'
    flavorDimensions DEFAULT_FLAVOR_DIMENSION
    productFlavors {

        demo {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.kaolafm.sdk.demo"
            resValue "string", "app_name", "SdkDemo"
            manifestPlaceholders = [
                    APP_ID : "in2193",
                    APP_KEY: "09843cde4e4638b3083529f8a42b4a43",
                    CHANNEL: "demo"
            ]
        }

        ivi {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.ivi.radio"
            resValue "string", "app_name", "威马"
            manifestPlaceholders = [
                    APP_ID : "kl1500",
                    APP_KEY: "856ab4eef8435a3fb4b879ce274ab898",
                    CHANNEL: "ivi"
            ]
        }

        ceshizhuangyong {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.edog.car.ceshizhuanyong_kradio"
            resValue "string", "app_name", "SdkDemo测试专用"
            manifestPlaceholders = [
                    APP_ID : "ye8192",
                    APP_KEY: "f6dff42133bf06810a52a1d392b9906b",
                    CHANNEL: "ceshizhuanyong_kradio"
            ]
        }

        /ceshi {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.edog.car.ceshi"
            resValue "string", "app_name", "SdkDemo测试"
            manifestPlaceholders = [
                    APP_ID : "ts5733",
                    APP_KEY: "3c5651fad2aa78f12803f0707b0a7e9f",
                    CHANNEL: "ceshi"
            ]
        }/

        kradio {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.kaolafm.kradio"
            resValue "string", "app_name", "SdkDemoK-radio"
            manifestPlaceholders = [
                    APP_ID : "cl3091",
                    APP_KEY: "ec47d20de91b463834d6f51992301605",
                    CHANNEL: "K-radio"
            ]
        }
        kradioTest {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.kaolafm.kradio.k_radio_horizontal"
            resValue "string", "app_name", "K-radio测试"
            manifestPlaceholders = [
                    APP_ID : "ye8192",
                    APP_KEY: "f6dff42133bf06810a52a1d392b9906b",
                    CHANNEL: "K-radio"
            ]
        }
        weilai {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.nio.liveradio"
            resValue "string", "app_name", "蔚来直播"
            manifestPlaceholders = [
                    APP_ID : "iv5583",
                    APP_KEY: "7bf809852068fc3df7093f1fc34f8199",
                    CHANNEL: "weilaizhibo"
            ]
        }
        weilaidiantai {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.nextev.mediacenter"
            resValue "string", "app_name", "蔚来电台"
            manifestPlaceholders = [
                    APP_ID : "vt6362",
                    APP_KEY: "c5d662b5cf2f11fe3f8e77d4ea6a1d03",
                    CHANNEL: "weilaidiantai"
            ]
        }
        dayun {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.chinatsp.radio"
            resValue "string", "app_name", "大运"
            manifestPlaceholders = [
                    APP_ID : "dl4085",
                    APP_KEY: "7c4af52fbbec0fba38610d6a4797ec42",
                    CHANNEL: "dayun"
            ]
        }
        hongqi {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.xiaoma.radio"
            resValue "string", "app_name", "红旗小马"
            manifestPlaceholders = [
                    APP_ID : "rr3843",
                    APP_KEY: "46a0c5fdb12c3a07ea2ed37e18a39ef7",
                    CHANNEL: "hongqixiaoma"
            ]
        }
        dianka {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.fuxi.music"
            resValue "string", "app_name", "电咖"
            manifestPlaceholders = [
                    APP_ID : "uk1862",
                    APP_KEY: "63c0e4b3a488f0878c6b60bd637e2ffa",
                    CHANNEL: "dianka"
            ]
        }
        dianka2 {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.fuxi.pid.music"
            resValue "string", "app_name", "电咖2"
            manifestPlaceholders = [
                    APP_ID : "la6037",
                    APP_KEY: "cdd2a81a55ff62e1956965070055db82",
                    CHANNEL: "dianka2"
            ]
        }
        /benchi {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.kaolafm.kradio.k_radio_horizontal.benci"
            resValue "string", "app_name", "奔驰"
            manifestPlaceholders = [
                    APP_ID : "rq5825",
                    APP_KEY: "75105653ae83b7f4739ec75ef8e8fcde",
                    CHANNEL: "benci"
            ]
        }/
        hezhong {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "of.radio.hz"
            resValue "string", "app_name", "合众"
            manifestPlaceholders = [
                    APP_ID : "kj1417",
                    APP_KEY: "40c56580998fe7b69cb72758dfd030e4",
                    CHANNEL: "hezhong"
            ]
        }
        xinte {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.hazens"
            resValue "string", "app_name", "新特"
            manifestPlaceholders = [
                    APP_ID : "tm9908",
                    APP_KEY: "431d5b0b5183e4757aa0465b10fca525",
                    CHANNEL: "xinte"
            ]
        }
        sailin {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.saleenauto.fm"
            resValue "string", "app_name", "赛麟"
            manifestPlaceholders = [
                    APP_ID : "eq7588",
                    APP_KEY: "d64446f337b391ec2377028c3e6425de",
                    CHANNEL: "saleen"
            ]
        }
        jinghuai {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.coagent.jac.inch104.onlinefm"
            resValue "string", "app_name", "江淮"
            manifestPlaceholders = [
                    APP_ID : "mn7184",
                    APP_KEY: "a10b6da832fb37e68978917ed1540357",
                    CHANNEL: "jinghuai"
            ]
        }

        xiaomabenteng {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.xiaoma.xting.benteng"
            resValue "string", "app_name", "小马奔腾"
            manifestPlaceholders = [
                    APP_ID : "av4252",
                    APP_KEY: "17a9f4b271dc2b7784694525ee682e9d",
                    CHANNEL: "xiaomabenteng"
            ]
        }

        desaixiwei {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.desay_svautomotive.svonlineradio"
            resValue "string", "app_name", "德赛西威"
            manifestPlaceholders = [
                    APP_ID : "io0440",
                    APP_KEY: "55553b4c49e61372bb5c4d8fa51022f6",
                    CHANNEL: "desay"
            ]
        }
        dongfengqichen {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.hsae.vehicleradio"
            resValue "string", "app_name", "东方启辰"
            manifestPlaceholders = [
                    APP_ID : "sw0574",
                    APP_KEY: "d8cb11a713bf2e242952c73b7900ee5e",
                    CHANNEL: "dongfengqichen"
            ]
        }

        dongfengqichen532 {
            dimension DEFAULT_FLAVOR_DIMENSION
            applicationId "com.hsae.vehicleradio"
            resValue "string", "app_name", "东风启辰"
            manifestPlaceholders = [
                    APP_ID      : "sw0574",
                    APP_KEY     : "d8cb11a713bf2e242952c73b7900ee5e",
                    CHANNEL     : "dongfengqichen532"
            ]
        }
    }
}

//configurations.all {
//    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
//}
dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'
    implementation dependent["appcompat-v7"]
    implementation 'com.android.support.constraint:constraint-layout:1.1.2'
    implementation 'com.android.support:recyclerview-v7:27.0.2'
    implementation dependent["glide"]
    annotationProcessor dependent["glide-compiler"]
    implementation 'com.jakewharton:butterknife:8.8.1'
    annotationProcessor 'com.jakewharton:butterknife-compiler:8.8.1'
    implementation 'com.lcodecorex:tkrefreshlayout:1.0.7'
    implementation 'com.github.tbruyelle:rxpermissions:0.10.2'
//    implementation(dependent["openSDK"]) {
//        changing = true
//    }
    implementation project(':sdk')
    //网宿云存储，用于上传录音文件
    implementation 'com.netease.nimlib:basesdk:5.1.1'
    //网易即时通讯基础组件
    implementation 'com.netease.nimlib:chatroom:5.1.1'
    //网易聊天室
    implementation 'com.elvishew:xlog:1.6.1'
    implementation 'com.orhanobut:logger:2.2.0'
//    implementation project(":log")
    implementation dependent["rxlifecycle2"]
    implementation dependent["rxlifecycle2-components"]
    implementation 'com.android.support:multidex:1.0.3'
}
buildscript {

    repositories {
        //阿里云的maven镜像，提升下载依赖库的速度，理论上只有这个就可以了，下面的都不需要
        maven{url 'http://maven.aliyun.com/nexus/content/groups/public/'}
        google()
        jcenter()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.3'
        classpath 'org.greenrobot:greendao-gradle-plugin:3.2.2'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
//        classpath files('BuildSrc/BuildSrc.jar')
        classpath 'com.hujiang.aspectjx:gradle-android-plugin-aspectjx:2.0.4'
    }
}

allprojects {
    repositories {
        //阿里云的maven镜像，提升下载依赖库的速度，理论上只有这个就可以了，下面的都不需要
        maven{url 'http://maven.aliyun.com/nexus/content/groups/public/'}
        google()
        jcenter()
        maven { url "https://jitpack.io" }
        mavenCentral()
        maven {
//            url "https://dl.bintray.com/tingban/maven/"
//            url "file:///Users/<USER>/AndroidStudioProjects/maven-repository"
//            url "http://nexus.kaolafm.com/nexus/content/repositories/releases/"
            url "http://pub.nexus.kaolafm.com:8082/repository/github-self/"
        }
    }
}