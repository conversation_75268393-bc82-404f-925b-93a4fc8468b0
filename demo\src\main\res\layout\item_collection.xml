<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_collection_item_img"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:scaleType="centerCrop" />
    <TextView
        android:id="@+id/tv_collection_item_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="8首"
        android:textSize="10sp"
        />

    <TextView
        android:id="@+id/tv_collection_item_listen"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lines="1"
        android:padding="3dp"
        android:textColor="@color/colorBlack"
        android:textSize="10sp"
        app:layout_constraintBottom_toBottomOf="@id/iv_collection_item_img"
        app:layout_constraintEnd_toEndOf="@id/iv_collection_item_img"
        tools:text="100万" />

    <TextView
        android:id="@+id/tv_collection_item_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="大王叫我来巡山"
        android:textColor="@color/colorBlack"
        app:layout_constraintStart_toEndOf="@id/iv_collection_item_img"
        android:paddingTop="5dp"
        android:layout_marginStart="5dp"
        android:lines="1"
        />
    <TextView
        android:id="@+id/tv_collection_item_des"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        tools:text="alkdjflkajflkajfdlajfd"
        app:layout_constraintStart_toEndOf="@id/iv_collection_item_img"
        app:layout_constraintTop_toBottomOf="@id/tv_collection_item_name"
        android:paddingTop="10dp"
        android:layout_marginStart="5dp"
        />

</android.support.constraint.ConstraintLayout>