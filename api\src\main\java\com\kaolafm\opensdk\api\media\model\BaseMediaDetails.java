package com.kaolafm.opensdk.api.media.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 媒体类基类，目前主要是PGC和专辑的基类
 *
 * <AUTHOR>
 * @date 2019/3/6
 */
public class BaseMediaDetails implements Parcelable {


    /**
     * id : 1200000000365
     * name : 新闻频道
     * img : http://img.kaolafm.net/mz/images/201711/6fc36184-eee8-473a-bf32-49a09e931162/default.jpg
     * followedNum : 0
     * isOnline : 1
     * listenNum : 1
     * desc : 新闻Radio
     * commentNum : 0
     * isSubscribe : 0
     * type : 3
     * host : []
     * keyWords : []
     */

    @SerializedName("id")
    private long id;

    @SerializedName("name")
    private String name;

    @SerializedName("img")
    private String img;

    @SerializedName("followedNum")
    private int followedNum;

    @SerializedName("isOnline")
    private int isOnline;

    @SerializedName("listenNum")
    private int listenNum;

    @SerializedName("desc")
    private String desc;

    @Deprecated
    @SerializedName("commentNum")
    private int commentNum;

    @SerializedName("isSubscribe")
    private int isSubscribe;

    @SerializedName("type")
    private int type;

    //AI 电台此字段废弃了
    @SerializedName("host")
    private List<Host> host;

    @SerializedName("keyWords")
    private List<String> keyWords;

    @Deprecated
    @SerializedName("sourceLogo")
    private String sourceLogo;

    @Deprecated
    @SerializedName("sourceName")
    private String sourceName;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public int getFollowedNum() {
        return followedNum;
    }

    public void setFollowedNum(int followedNum) {
        this.followedNum = followedNum;
    }

    public int getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(int isOnline) {
        this.isOnline = isOnline;
    }

    public int getListenNum() {
        return listenNum;
    }

    public void setListenNum(int listenNum) {
        this.listenNum = listenNum;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(int commentNum) {
        this.commentNum = commentNum;
    }

    public int getIsSubscribe() {
        return isSubscribe;
    }

    public void setIsSubscribe(int isSubscribe) {
        this.isSubscribe = isSubscribe;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public List<Host> getHost() {
        return host;
    }

    public void setHost(List<Host> host) {
        this.host = host;
    }

    public List<String> getKeyWords() {
        return keyWords;
    }

    public void setKeyWords(List<String> keyWords) {
        this.keyWords = keyWords;
    }

    public String getSourceLogo() {
        return sourceLogo;
    }

    public void setSourceLogo(String sourceLogo) {
        this.sourceLogo = sourceLogo;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public BaseMediaDetails() {
    }

    protected BaseMediaDetails(Parcel in) {
        this.id = in.readLong();
        this.name = in.readString();
        this.img = in.readString();
        this.followedNum = in.readInt();
        this.isOnline = in.readInt();
        this.listenNum = in.readInt();
        this.desc = in.readString();
        this.commentNum = in.readInt();
        this.isSubscribe = in.readInt();
        this.type = in.readInt();
        this.host = in.createTypedArrayList(Host.CREATOR);
        this.keyWords = in.createStringArrayList();
        this.sourceLogo = in.readString();
        this.sourceName = in.readString();
    }

    public static final Creator<BaseMediaDetails> CREATOR = new Creator<BaseMediaDetails>() {
        @Override
        public BaseMediaDetails createFromParcel(Parcel in) {
            return new BaseMediaDetails(in);
        }

        @Override
        public BaseMediaDetails[] newArray(int size) {
            return new BaseMediaDetails[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(this.id);
        dest.writeString(this.name);
        dest.writeString(this.img);
        dest.writeInt(this.followedNum);
        dest.writeInt(this.isOnline);
        dest.writeInt(this.listenNum);
        dest.writeString(this.desc);
        dest.writeInt(this.commentNum);
        dest.writeInt(this.isSubscribe);
        dest.writeInt(this.type);
        dest.writeTypedList(this.host);
        dest.writeStringList(this.keyWords);
        dest.writeString(this.sourceLogo);
        dest.writeString(this.sourceName);
    }

    @Override
    public String toString() {
        return "BaseMediaDetails{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", img='" + img + '\'' +
                ", followedNum=" + followedNum +
                ", isOnline=" + isOnline +
                ", listenNum=" + listenNum +
                ", desc='" + desc + '\'' +
                ", commentNum=" + commentNum +
                ", isSubscribe=" + isSubscribe +
                ", type=" + type +
                ", host=" + host +
                ", keyWords=" + keyWords +
                ", sourceLogo=" + sourceLogo +
                ", sourceName=" + sourceName +
                '}';
    }
}
