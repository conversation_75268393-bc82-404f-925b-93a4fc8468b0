<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="10dp">

    <ImageView
        android:id="@+id/iv_item_category_icon"
        android:layout_width="70dp"
        android:layout_height="70dp" />

    <TextView
        android:id="@+id/tv_item_category_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lines="1"
        android:paddingStart="10dp"
        android:textColor="@color/colorBlack"
        android:textSize="16sp"
        app:layout_constraintStart_toEndOf="@id/iv_item_category_icon"
        tools:text="名称" />

    <TextView
        android:id="@+id/tv_item_category_des"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingStart="10dp"
        android:textColor="@color/color_black_50_transparent"
        android:textSize="12sp"
        app:layout_constraintStart_toEndOf="@id/iv_item_category_icon"
        app:layout_constraintTop_toBottomOf="@id/tv_item_category_name"
        tools:text="描述" />

</android.support.constraint.ConstraintLayout>
