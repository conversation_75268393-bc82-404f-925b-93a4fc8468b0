package com.kaolafm.opensdk.api.personalise;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.personalise.internal.Status;
import com.kaolafm.opensdk.api.personalise.model.InterestTag;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.HashMap;
import java.util.List;

import io.reactivex.Single;
import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * 个性化推荐相关请求
 *
 * <AUTHOR>
 * @date 2019/4/28
 */
public class PersonalizedRequest extends BaseRequest {

    private final PersonalizedService mPersonalizedService;

    public PersonalizedRequest() {
        mPersonalizedService = obtainRetrofitService(PersonalizedService.class);
    }

    /**
     * 获取用户标签
     *
     * @param callback 回调，返回兴趣标签列表
     */
    public void getInterestTagList(HttpCallback<List<InterestTag>> callback) {
        boolean isLogin = AccessTokenManager.getInstance().getKaolaAccessToken().isLogin();
        Single<BaseResult<List<InterestTag>>> interestTagList = isLogin ? mPersonalizedService.getInterestTagListLogined() : mPersonalizedService.getInterestTagListUnlogined();
        doHttpDeal(interestTagList, BaseResult::getResult, callback);
    }

    /**
     * 获取个人兴趣标签。如果已经有保存的标签，返回结果里面会包含选中信息，即{@link InterestTag#isSelected()} = true 表示已经保存的选中标签。
     * @param callback
     */
    public void getPersonalInterestTagList(HttpCallback<List<InterestTag>> callback) {
        Single<BaseResult<List<InterestTag>>> zip = Single.zip(mPersonalizedService.getInterestTagListLogined(), mPersonalizedService.getPersonalTags(), (baseResult, baseResult2) -> {
            List<InterestTag> tags = baseResult.getResult();
            List<String> selectedTags = baseResult2.getResult();
            if (!ListUtil.isEmpty(tags) && !ListUtil.isEmpty(selectedTags)) {
                HashMap<String, Boolean> map = new HashMap<>();
                for (String selectedTag : selectedTags) {
                    map.put(selectedTag, true);
                }
                for (InterestTag interestTag : tags) {
                    boolean b = map.get(interestTag.getName()) != null;
                    interestTag.setSelected(b);
                }
            }
            baseResult.setResult(tags);
            return baseResult;
        });
        doHttpDeal(zip, BaseResult::getResult,  callback);
    }

    /**
     * 保存用户属性
     *
     * @param year     选填 出生年份
     * @param gender   选填 性别  0表示男；1表示女
     * @param callback 回调，返回兴趣标签列表
     */
    public void saveUserAttribute(String year, int gender, HttpCallback<List<InterestTag>> callback) {
        doHttpDeal(mPersonalizedService.saveUserAttribute(year, gender), BaseResult::getResult, callback);
    }

    /**
     * 保存多个兴趣标签
     *
     * @param tags     选填 标签，多个标签用，隔开
     * @param callback 回调，保存成功返回true
     */
    public void saveInterestTags(String tags, HttpCallback<Boolean> callback) {
        doHttpDeal(mPersonalizedService.saveInterestTag(tags), this::status, callback);
    }

    /**
     * 获取个人选中的兴趣标签。
     *
     * @param callback 回调，兴趣标签列表
     */
    public void getPersonalTags(HttpCallback<List<String>> callback) {
        doHttpDeal(mPersonalizedService.getPersonalTags(), BaseResult::getResult, callback);
    }

    /**
     * 保存厂商用户信息。必须听伴登录才能调用该接口
     *
     * @param phoneNumber 必填 手机号
     * @param avatar      选填 头像
     * @param nickName    选填 昵称
     * @param gender      选填 性别 0表示男；1表示女
     * @param age         选填 年龄
     * @param city        选填 城市
     * @param callback    回调，保存成功返回true
     */
    public void saveThirdUser(String phoneNumber, String avatar, String nickName, int gender, int age,
                              String city, HttpCallback<Boolean> callback) {
        HashMap<String, Object> params = new HashMap<>(6);
        params.put("phoneNumber", phoneNumber);
        putNullParam(params, "userIcon", avatar);
        putNullParam(params, "nickName", nickName);
        putNullParam(params, "gender", gender < 0 ? null : gender);
        putNullParam(params, "age", age < 0 ? null : age);
        putNullParam(params, "userCity", city);
        RequestBody requestBody = RequestBody
                .create(MediaType.parse("application/json"), mGsonLazy.get().toJson(params));
        doHttpDeal(mPersonalizedService.saveThirdUser(requestBody), this::status, callback);
    }

    /**
     * 保存车机设备信息
     *
     * @param gender    选填 性别 0表示男；1表示女
     * @param birthYear 选填 出生年份
     * @param carType   选填 车型
     * @param callback  回调，保存成功返回true
     */
    public void saveDeviceInfo(int gender, String birthYear, String carType, HttpCallback<Boolean> callback) {
        HashMap<String, Object> params = new HashMap<>(3);
        putNullParam(params, "gender", gender < 0 ? null : gender);
        putNullParam(params, "birthYear", birthYear);
        putNullParam(params, "carType", carType);
        RequestBody requestBody = RequestBody
                .create(MediaType.parse("application/json"), mGsonLazy.get().toJson(params));
        doHttpDeal(mPersonalizedService.saveDeviceInfo(requestBody), this::status, callback);
    }

    private boolean status(BaseResult<Status> baseResult) {
        Status status = baseResult.getResult();
        return status != null && status.getStatus() == Status.SUCCESS;
    }

}
