apply plugin: 'com.android.library'
apply plugin: 'build-jar'
apply plugin: 'org.greenrobot.greendao'
def and = rootProject.ext.android
def dependent = rootProject.ext.dependencies
def VERSION_CODE = and.versionCode
def VERSION_NAME = and.versionName
android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion

    defaultConfig {
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode VERSION_CODE
        versionName VERSION_NAME
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility and.javaSourceVersion
        targetCompatibility and.javaTargetVersion
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    //配置数据库相关信息
    greendao {
        schemaVersion 1 //数据库版本号
        daoPackage 'com.kaolafm.opensdk.db.greendao'
        targetGenDir 'src/main/java'//设置DaoMaster、DaoSession、Dao目录
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api dependent["annotations"]
    api(dependent["retrofit2"]) {
        exclude module: 'okhttp'
        exclude module: 'okio'
    }
    api(dependent["retrofit2-gson"]) {
        exclude module: 'gson'
        exclude module: 'okhttp'
        exclude module: 'okio'
        exclude module: 'retrofit'
    }
    api(dependent["retrofit2-rxjava2"]) {
        exclude module: 'rxjava'
        exclude module: 'okhttp'
        exclude module: 'retrofit'
        exclude module: 'okio'
    }
    api dependent["okhttp3"]
    api(dependent["rxandroid2"]) {
        exclude module: 'rxjava'
    }
    api dependent["rxjava2"]
    api dependent["greenDao"]

    compileOnly(dependent["rxlifecycle2-components"]) {
        exclude module: 'rxjava'
        exclude module: 'appcompat-v7'
    }
    api dependent["gson"]
    api dependent["dagger2"]
    annotationProcessor dependent["dagger2-compiler"]
    embed project(path:':utils', configuration:"default")
    annotationProcessor project(":buildSrc")
//    api project(':utils')
}
