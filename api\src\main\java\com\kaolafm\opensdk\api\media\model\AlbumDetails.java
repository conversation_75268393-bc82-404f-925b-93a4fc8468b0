package com.kaolafm.opensdk.api.media.model;

import android.os.Parcel;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: AlbumDetailsResult.java                                               
 *                                                                  *
 * Created in 2018/8/10 下午3:43                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public final class AlbumDetails extends BaseMediaDetails {

    /**
     * id : 1100000000416
     * name : 仨贱客
     * img : http://img.kaolafm.net/mz/images/201505/7e72e6ce-0315-494d-8fc0-6d7c00289ac0/default.jpg
     * followedNum : 4624277
     * countNum : 1757
     * isOnline : 1
     * desc : 内涵情景搞笑喜剧，无节操无下限！
     * 一档由车语传媒独播的情景喜剧搞笑节目。三个人，玩儿角色扮演；三张嘴，演绎大千生活；仨贱客，我们Cosplay的是生活，用心制作的高端无节操情景广播喜剧。
     * listenNum : 443360989
     * sortType : -1
     * hasCopyright : 1
     * host : [{"name":"小胆","des":"东北逆生长帅哥，前东北DJ大佬，彩铃天王！叼根烟能讲26个小时段子，活着的本身就是一个笑话接着一个笑话。","img":"http://img.kaolafm.net/mz/images/201604/bc48d175-f018-45ef-be01-18ae2becff0f/default.jpg"},{"name":"小雪","des":"三观不健全，伪文艺伪重口懂得欣赏小清（huang）新（gua），含蓄内敛逼格低，讲求高雅、细腻与积极向上，致力于成为一名土豪。 单身。","img":"http://img.kaolafm.net/mz/images/201604/68fdc194-a500-4969-af4a-3fabc2c3db44/default.jpg"},{"name":"小宁","des":"一个貌似南方人的东北小男银，人送外号\u201c斯文败类\u201d。原是学视频出身，后来转去祸祸广播界。吃货一只~手抖大师。贱~不是本事，是天赋！","img":"http://img.kaolafm.net/mz/images/201604/03d2f0b4-0620-4e7f-8e51-88e2e3d6979c/default.jpg"}]
     * produce :
     * status : 更新中
     * updateDay : 每天更新
     * copyrightLabel :
     * keyWords : ["搞笑","笑话段子","无节操","仨贱客","情景广播剧"]
     * commentNum : 825
     * lastCheckDate : 1533830400000
     * type : 0
     * isSubscribe : 0
     */

    public static final int BUY_STATUS_NOT_PURCHASE = 0;//未购买
    public static final int BUY_STATUS_PURCHASE = 1;//已购

    public static final int BUY_TYPE_FREE = 0;//免费
    public static final int BUY_TYPE_ALBUM = 1;//专辑购买
    public static final int BUY_TYPE_AUDIO = 2;//单曲购买

    @SerializedName("countNum")
    private int countNum;

    @SerializedName("sortType")
    private int sortType;

    @Deprecated
    @SerializedName("hasCopyright")
    private int hasCopyright;

    @SerializedName("produce")
    private String produce;

    @SerializedName("status")
    private String status;

    @SerializedName("updateDay")
    private String updateDay;

    @Deprecated
    @SerializedName("copyrightLabel")
    private String copyrightLabel;

    @Deprecated
    @SerializedName("lastCheckDate")
    private long lastCheckDate;

    /** 是否vip专辑 0-否，1-是 */
    @SerializedName("vip")
    private int vip;

    /** 是否精品 0-否，1-是 */
    @SerializedName("fine")
    private int fine;

    /** 购买类型 0-免费，1-专辑购买，2-单曲购买 */
    @SerializedName("buyType")
    private int buyType;

    /** 是否购买 0-未购买；1-已购买 */
    @SerializedName("buyStatus")
    private int buyStatus;

    /** 支付方式 */
    @SerializedName("payMethod")
    private List<AlbumDetailsPayMethod> payMethod;

    /** 1-断点续播；2-根据时间判断是否断点续播 **/
    @SerializedName("breakPointContinue")
    private String breakPointContinue;

    /** 是否可以订阅 只有为1的时候不能订阅 */
    @SerializedName("noSubscribe")
    private int noSubscribe;

    /** 是否单曲付费 1 是 0 否*/
    @SerializedName("songNeedPay")
    private int songNeedPay;

    public int getSongNeedPay() {
        return songNeedPay;
    }

    public void setSongNeedPay(int songNeedPay) {
        this.songNeedPay = songNeedPay;
    }

    public int getCountNum() {
        return countNum;
    }

    public void setCountNum(int countNum) {
        this.countNum = countNum;
    }

    public int getSortType() {
        return sortType;
    }

    public void setSortType(int sortType) {
        this.sortType = sortType;
    }

    public int getHasCopyright() {
        return hasCopyright;
    }

    public void setHasCopyright(int hasCopyright) {
        this.hasCopyright = hasCopyright;
    }

    public String getProduce() {
        return produce;
    }

    public void setProduce(String produce) {
        this.produce = produce;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUpdateDay() {
        return updateDay;
    }

    public void setUpdateDay(String updateDay) {
        this.updateDay = updateDay;
    }

    public String getCopyrightLabel() {
        return copyrightLabel;
    }

    public void setCopyrightLabel(String copyrightLabel) {
        this.copyrightLabel = copyrightLabel;
    }

    public long getLastCheckDate() {
        return lastCheckDate;
    }

    public void setLastCheckDate(long lastCheckDate) {
        this.lastCheckDate = lastCheckDate;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    public int getFine() {
        return fine;
    }

    public void setFine(int fine) {
        this.fine = fine;
    }

    public int getBuyType() {
        return buyType;
    }

    public void setBuyType(int buyType) {
        this.buyType = buyType;
    }

    public int getBuyStatus() {
        return buyStatus;
    }

    public void setBuyStatus(int buyStatus) {
        this.buyStatus = buyStatus;
    }

    public List<AlbumDetailsPayMethod> getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(List<AlbumDetailsPayMethod> payMethod) {
        this.payMethod = payMethod;
    }

    public String getBreakPointContinue() {
        return breakPointContinue;
    }

    public void setBreakPointContinue(String breakPointContinue) {
        this.breakPointContinue = breakPointContinue;
    }

    public int getNoSubscribe() {
        return noSubscribe;
    }

    public void setNoSubscribe(int noSubscribe) {
        this.noSubscribe = noSubscribe;
    }

    @Override
    public String toString() {
        return "AlbumDetails{" +
                "countNum=" + countNum +
                ", sortType=" + sortType +
                ", hasCopyright=" + hasCopyright +
                ", produce='" + produce + '\'' +
                ", status='" + status + '\'' +
                ", updateDay='" + updateDay + '\'' +
                ", copyrightLabel='" + copyrightLabel + '\'' +
                ", lastCheckDate=" + lastCheckDate +
                ", vip=" + vip +
                ", fine=" + fine +
                ", buyType=" + buyType +
                ", buyStatus=" + buyStatus +
                ", payMethod=" + payMethod +
                ", breakPointContinue=" + breakPointContinue +
                ", noSubscribe=" + noSubscribe +
                '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        dest.writeInt(this.countNum);
        dest.writeInt(this.sortType);
        dest.writeInt(this.hasCopyright);
        dest.writeString(this.produce);
        dest.writeString(this.status);
        dest.writeString(this.updateDay);
        dest.writeString(this.copyrightLabel);
        dest.writeLong(this.lastCheckDate);
        dest.writeInt(this.vip);
        dest.writeInt(this.noSubscribe);
        dest.writeInt(this.fine);
        dest.writeInt(this.buyType);
        dest.writeInt(this.buyStatus);
        dest.writeTypedList(this.payMethod);
        dest.writeString(this.breakPointContinue);
    }

    protected AlbumDetails(Parcel in) {
        super(in);
        this.countNum = in.readInt();
        this.sortType = in.readInt();
        this.hasCopyright = in.readInt();
        this.produce = in.readString();
        this.status = in.readString();
        this.updateDay = in.readString();
        this.copyrightLabel = in.readString();
        this.lastCheckDate = in.readLong();
        this.vip = in.readInt();
        this.noSubscribe = in.readInt();
        this.fine = in.readInt();
        this.buyType = in.readInt();
        this.buyStatus = in.readInt();
        this.payMethod = new ArrayList<>();
        in.readTypedList(this.payMethod, AlbumDetailsPayMethod.CREATOR);
        this.breakPointContinue = in.readString();
    }

    public static final Creator<AlbumDetails> CREATOR = new Creator<AlbumDetails>() {
        @Override
        public AlbumDetails createFromParcel(Parcel source) {
            return new AlbumDetails(source);
        }

        @Override
        public AlbumDetails[] newArray(int size) {
            return new AlbumDetails[size];
        }
    };



}
