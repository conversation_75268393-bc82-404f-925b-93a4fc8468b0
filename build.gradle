// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: "config.gradle"

buildscript {

    repositories {
        //阿里云的maven镜像，提升下载依赖库的速度，理论上只有这个就可以了，下面的都不需要
        maven{url 'http://maven.aliyun.com/nexus/content/groups/public/'}
        google()
        jcenter()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.3'
        classpath 'org.greenrobot:greendao-gradle-plugin:3.2.2'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
//        classpath files('BuildSrc/BuildSrc.jar')
        classpath 'com.hujiang.aspectjx:gradle-android-plugin-aspectjx:2.0.4'
    }
}

allprojects {
    repositories {
        //阿里云的maven镜像，提升下载依赖库的速度，理论上只有这个就可以了，下面的都不需要
        maven{url 'http://maven.aliyun.com/nexus/content/groups/public/'}
        google()
        jcenter()
        maven { url "https://jitpack.io" }
        mavenCentral()
        maven {
//            url "https://dl.bintray.com/tingban/maven/"
//            url "file:///Users/<USER>/AndroidStudioProjects/maven-repository"
//            url "http://nexus.kaolafm.com/nexus/content/repositories/releases/"
            url "http://pub.nexus.kaolafm.com:8082/repository/github-self/"
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}