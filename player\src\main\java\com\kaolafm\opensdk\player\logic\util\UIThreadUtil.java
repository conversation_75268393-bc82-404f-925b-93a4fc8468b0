package com.kaolafm.opensdk.player.logic.util;

import android.nfc.Tag;
import android.os.Looper;
import android.util.Log;

import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class UIThreadUtil {

    public interface UiThread {
        void onSuccess();
    }

    public static void runUIThread(UiThread mUiThread) {
        if (isMainThread()) {
            mUiThread.onSuccess();
            return;
        }
        Observable.fromArray(new String[]{})
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<String>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                    }

                    @Override
                    public void onNext(String o) {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onComplete() {
                        mUiThread.onSuccess();
                    }
                });
    }

    private static boolean isMainThread() {
        return Looper.getMainLooper().getThread() == Thread.currentThread();
    }

}
