package com.kaolafm.opensdk.player.logic.model.item.model;

public class RadioInfoData {
    /**
     * 电台id
     */
    private long radioId;

    /**
     * 专辑订阅数
     */
    private long followedNum;
    /**
     * 总期数
     */
    private long countNum;

    /**
     * 收听数
     */
    private long listenNum;

    /**
     * 电台名称
     */
    private String radioName;

    /**
     * 这个无法描述
     */
    private String clockId;

    /**
     * 是否为第三方源 1为是第三方 0为不是
     */
    private int isThirdParty;

    /**
     * 内容类型 标题
     */
    private String radioSubTag;

    /**
     * 内容类型, 0:分类,1:专辑,2:台宣,3:在线广播,4:歌曲,5:个推,6:地域,7:直播
     */
    private int radioSubTagType;

    /**
     * 内容主标题名称
     */
    private String mainTitleName;

    /**
     * 内容副标题名称
     */
    private String subheadName;

    /**
     * 推荐callback
     */
    private String callBack;

    private String source;

    private long categoryId;


    private String radioPic;

    private int radioType = -1;

    private int adZoneChooseType = 0;

    private int adZoneId = -1;


    public long getRadioId() {
        return radioId;
    }

    public void setRadioId(long radioId) {
        this.radioId = radioId;
    }

    public long getFollowedNum() {
        return followedNum;
    }

    public void setFollowedNum(long followedNum) {
        this.followedNum = followedNum;
    }

    public long getCountNum() {
        return countNum;
    }

    public void setCountNum(long countNum) {
        this.countNum = countNum;
    }

    public long getListenNum() {
        return listenNum;
    }

    public void setListenNum(long listenNum) {
        this.listenNum = listenNum;
    }

    public String getRadioName() {
        return radioName;
    }

    public void setRadioName(String radioName) {
        this.radioName = radioName;
    }

    public String getClockId() {
        return clockId;
    }

    public void setClockId(String clockId) {
        this.clockId = clockId;
    }

    public int getIsThirdParty() {
        return isThirdParty;
    }

    public void setIsThirdParty(int isThirdParty) {
        this.isThirdParty = isThirdParty;
    }

    public String getRadioSubTag() {
        return radioSubTag;
    }

    public void setRadioSubTag(String radioSubTag) {
        this.radioSubTag = radioSubTag;
    }

    public int getRadioSubTagType() {
        return radioSubTagType;
    }

    public void setRadioSubTagType(int radioSubTagType) {
        this.radioSubTagType = radioSubTagType;
    }

    public String getMainTitleName() {
        return mainTitleName;
    }

    public void setMainTitleName(String mainTitleName) {
        this.mainTitleName = mainTitleName;
    }

    public String getSubheadName() {
        return subheadName;
    }

    public void setSubheadName(String subheadName) {
        this.subheadName = subheadName;
    }

    public String getCallBack() {
        return callBack;
    }

    public void setCallBack(String callBack) {
        this.callBack = callBack;
    }

    public long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(long categoryId) {
        this.categoryId = categoryId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRadioPic() {
        return radioPic;
    }

    public void setRadioPic(String radioPic) {
        this.radioPic = radioPic;
    }

    public int getRadioType() {
        return radioType;
    }

    public void setRadioType(int radioType) {
        this.radioType = radioType;
    }

    public int getAdZoneChooseType() {
        return adZoneChooseType;
    }

    public void setAdZoneChooseType(int adZoneChooseType) {
        this.adZoneChooseType = adZoneChooseType;
    }

    public int getAdZoneId() {
        return adZoneId;
    }

    public void setAdZoneId(int adZoneId) {
        this.adZoneId = adZoneId;
    }
}
