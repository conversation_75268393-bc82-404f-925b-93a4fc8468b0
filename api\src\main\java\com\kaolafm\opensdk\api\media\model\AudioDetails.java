package com.kaolafm.opensdk.api.media.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: AudioDetails.java                                               
 *                                                                  *
 * Created in 2018/8/13 上午10:17                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class AudioDetails implements Parcelable {


    /**
     * audioId : 1000018856226
     * audioName : 二货一箩筐
     * audioPic : http://img.kaolafm.net/mz/images/201703/cba97a7f-5049-49c6-baf1-9c607e55f720/default.jpg
     * audioDes : 二货怎么会如此的多呢？因为世界和平的需要,总有一些人要做出选择去发光发热为别人创造快乐。
     * albumId : 1100000012708
     * albumName : 爱上晕段子
     * albumPic : http://img.kaolafm.net/mz/images/201703/cba97a7f-5049-49c6-baf1-9c607e55f720/default.jpg
     * orderNum : 1104
     * mp3PlayUrl32 : http://image.kaolafm.net/mz/mp3_32/201806/1daceed1-c40b-475a-8aae-4aafa9fbf33e.mp3
     * mp3PlayUrl64 : http://image.kaolafm.net/mz/mp3_64/201806/1daceed1-c40b-475a-8aae-4aafa9fbf33e.mp3
     * aacPlayUrl : http://image.kaolafm.net/mz/aac_32/201806/1daceed1-c40b-475a-8aae-4aafa9fbf33e.aac
     * aacPlayUrl32 : http://image.kaolafm.net/mz/aac_32/201806/1daceed1-c40b-475a-8aae-4aafa9fbf33e.aac
     * aacPlayUrl64 : http://image.kaolafm.net/mz/aac_64/201806/1daceed1-c40b-475a-8aae-4aafa9fbf33e.aac
     * aacPlayUrl128 : http://image.kaolafm.net/mz/aac_128/201806/1daceed1-c40b-475a-8aae-4aafa9fbf33e.aac
     * aacPlayUrl320: ""
     * host : [{"name":"爱野","des":"DJ爱野，曾就职于中央人民广播电台央广都市担任主持人，主持过多档娱乐脱口秀、心灵鸡汤类节目，深受听众粉丝的喜爱。现为考拉FM签约主播。\r\n","img":"http://img.kaolafm.net/mz/images/201309/be07d754-f4fd-40a6-a287-2b55246bef30/default.jpg"}]
     * aacFileSize : 1460071
     * mp3FileSize32 : 729658
     * mp3FileSize64 : 1459873
     * updateTime : 1528127381000
     * clockId :
     * duration : 182500
     * originalDuration : 182517
     * listenNum : 45346
     * likedNum : 1
     * hasCopyright : 1
     * commentNum : 1
     * trailerStart : 0
     * trailerEnd : 0
     * categoryId : 143
     * source : null
     * isListened : 0
     * icon : null
     * contentType:2 //内容类型, 0:分类,1:专辑,2:台宣,3:在线广播,4:歌曲,5:个推,6:地域,7:直播
     * contentTypeName:"台宣",//内容类型名称
     * mainTitleName:"新闻电台", //内容主标题名称
     * subheadName:"新闻FM－ID－1".//内容副标题名称
     * adZoneId:
     */

    public static final int BUY_TYPE_FREE = 0;//免费
    public static final int BUY_TYPE_AUDITION = 1;//试听
    public static final int BUY_TYPE_AUDIO = 2;//单曲购买
    public static final int BUY_TYPE_ALBUM = 3;//专辑购买
    public static final int BUY_TYPE_VIP = 4;//vip购买

    @SerializedName("audioId")
    private long audioId;

    @SerializedName("audioName")
    private String audioName;

    @SerializedName("audioPic")
    private String audioPic;

    @SerializedName("audioDes")
    private String audioDes;

    @SerializedName("albumId")
    private long albumId;

    @SerializedName("albumName")
    private String albumName;

    @SerializedName("albumPic")
    private String albumPic;

    @SerializedName("orderNum")
    private int orderNum;

    @SerializedName("mp3PlayUrl32")
    private String mp3PlayUrl32;

    @SerializedName("mp3PlayUrl64")
    private String mp3PlayUrl64;

    @SerializedName("aacPlayUrl")
    private String aacPlayUrl;

    @SerializedName("aacPlayUrl32")
    private String aacPlayUrl32;

    @SerializedName("aacPlayUrl64")
    private String aacPlayUrl64;

    @SerializedName("aacPlayUrl128")
    private String aacPlayUrl128;

    @SerializedName("aacPlayUrl320")
    private String aacPlayUrl320;

    @SerializedName("aacFileSize")
    private int aacFileSize;

    @SerializedName("mp3FileSize32")
    private int mp3FileSize32;

    @SerializedName("mp3FileSize64")
    private int mp3FileSize64;

    @SerializedName("updateTime")
    private long updateTime;

    @Deprecated
    @SerializedName("clockId")
    private String clockId;

    @SerializedName("duration")
    private int duration;

    @Deprecated
    @SerializedName("originalDuration")
    private int originalDuration;

    @SerializedName("listenNum")
    private int listenNum;

    @Deprecated
    @SerializedName("likedNum")
    private int likedNum;

    @Deprecated
    @SerializedName("hasCopyright")
    private int hasCopyright;

    @Deprecated
    @SerializedName("commentNum")
    private int commentNum;

    @SerializedName("trailerStart")
    private int trailerStart;

    @SerializedName("trailerEnd")
    private int trailerEnd;

    @SerializedName("categoryId")
    private int categoryId;

    @Deprecated
    @SerializedName("source")
    private String source;

    @SerializedName("isListened")
    private int isListened;

    @Deprecated
    @SerializedName("icon")
    private String icon;

    @SerializedName("host")
    private List<Host> host;

    @Deprecated
    @SerializedName("isThirdParty")
    private int isThirdParty;

    /**内容类型, 0:分类,1:专辑,2:台宣,3:在线广播,4:歌曲,5:个推,6:地域,7:直播 */
    @SerializedName("contentType")
    private int contentType;

    /**内容类型名称 */
    @SerializedName("contentTypeName")
    private String contentTypeName;

    /**内容主标题名称 */
    @SerializedName("mainTitleName")
    private String mainTitleName;

    /** 内容副标题名称*/
    @SerializedName("subheadName")
    private String subheadName;

    /**
     * 是否还有下一页。0表示没有，1表示有。
     */
    @SerializedName("hasNextPage")
    private int hasNextPage;
    /**
     * 推荐callback
     */
    @SerializedName("callBack")
    private String callBack;

    @Deprecated
    @SerializedName("sourceLogo")
    private String sourceLogo;

    @Deprecated
    @SerializedName("sourceName")
    private String sourceName;

    /**若存在该字段，表示播放完当前单曲时，客户端应根据该字段向广告系统请求广告。*/
    @Deprecated
    private long adZoneId = -1;

    /** 播放id AI 电台不需要此字段*/
    @SerializedName("playUrlId")
    private String playUrlId;

    /** 购买类型 0-免费，1-试听，2-单曲购买 3-专辑购买 4-vip*/
    @Deprecated
    @SerializedName("buyType")
    private int buyType;

    /** 是否购买 0-未购买；1-已购买 */
    @Deprecated
    @SerializedName("buyStatus")
    private int buyStatus;

    /** 支付方式 */
    @SerializedName("payMethod")
    private List<AlbumDetailsPayMethod> payMethod;

    @SerializedName("albumIsFine")
    private int albumIsFine = 0;

    @SerializedName("albumIsVip")
    private int albumIsVip = 0;

    @SerializedName("songNeedPay")
    private int songNeedPay = 0;

    @SerializedName("audition")
    private int audition = 0;

    public int getSongNeedPay() {
        return songNeedPay;
    }

    public void setSongNeedPay(int songNeedPay) {
        this.songNeedPay = songNeedPay;
    }

    public int getAudition() {
        return audition;
    }

    public void setAudition(int audition) {
        this.audition = audition;
    }

    public long getAudioId() {
        return audioId;
    }

    public void setAudioId(long audioId) {
        this.audioId = audioId;
    }

    public String getAudioName() {
        return audioName;
    }

    public void setAudioName(String audioName) {
        this.audioName = audioName;
    }

    public String getAudioPic() {
        return audioPic;
    }

    public void setAudioPic(String audioPic) {
        this.audioPic = audioPic;
    }

    public String getAudioDes() {
        return audioDes;
    }

    public void setAudioDes(String audioDes) {
        this.audioDes = audioDes;
    }

    public long getAlbumId() {
        return albumId;
    }

    public void setAlbumId(long albumId) {
        this.albumId = albumId;
    }

    public String getAlbumName() {
        return albumName;
    }

    public void setAlbumName(String albumName) {
        this.albumName = albumName;
    }

    public String getAlbumPic() {
        return albumPic;
    }

    public void setAlbumPic(String albumPic) {
        this.albumPic = albumPic;
    }

    public int getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(int orderNum) {
        this.orderNum = orderNum;
    }

    public String getMp3PlayUrl32() {
        return mp3PlayUrl32;
    }

    public void setMp3PlayUrl32(String mp3PlayUrl32) {
        this.mp3PlayUrl32 = mp3PlayUrl32;
    }

    public String getMp3PlayUrl64() {
        return mp3PlayUrl64;
    }

    public void setMp3PlayUrl64(String mp3PlayUrl64) {
        this.mp3PlayUrl64 = mp3PlayUrl64;
    }

    public String getAacPlayUrl() {
        return aacPlayUrl;
    }

    public void setAacPlayUrl(String aacPlayUrl) {
        this.aacPlayUrl = aacPlayUrl;
    }

    public String getAacPlayUrl32() {
        return aacPlayUrl32;
    }

    public void setAacPlayUrl32(String aacPlayUrl32) {
        this.aacPlayUrl32 = aacPlayUrl32;
    }

    public String getAacPlayUrl64() {
        return aacPlayUrl64;
    }

    public void setAacPlayUrl64(String aacPlayUrl64) {
        this.aacPlayUrl64 = aacPlayUrl64;
    }

    public String getAacPlayUrl128() {
        return aacPlayUrl128;
    }

    public void setAacPlayUrl128(String aacPlayUrl128) {
        this.aacPlayUrl128 = aacPlayUrl128;
    }

    public String getAacPlayUrl320() {
        return aacPlayUrl320;
    }

    public void setAacPlayUrl320(String aacPlayUrl320) {
        this.aacPlayUrl320 = aacPlayUrl320;
    }

    public int getAacFileSize() {
        return aacFileSize;
    }

    public void setAacFileSize(int aacFileSize) {
        this.aacFileSize = aacFileSize;
    }

    public int getMp3FileSize32() {
        return mp3FileSize32;
    }

    public void setMp3FileSize32(int mp3FileSize32) {
        this.mp3FileSize32 = mp3FileSize32;
    }

    public int getMp3FileSize64() {
        return mp3FileSize64;
    }

    public void setMp3FileSize64(int mp3FileSize64) {
        this.mp3FileSize64 = mp3FileSize64;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public String getClockId() {
        return clockId;
    }

    public void setClockId(String clockId) {
        this.clockId = clockId;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public int getOriginalDuration() {
        return originalDuration;
    }

    public void setOriginalDuration(int originalDuration) {
        this.originalDuration = originalDuration;
    }

    public int getListenNum() {
        return listenNum;
    }

    public void setListenNum(int listenNum) {
        this.listenNum = listenNum;
    }

    public int getLikedNum() {
        return likedNum;
    }

    public void setLikedNum(int likedNum) {
        this.likedNum = likedNum;
    }

    public int getHasCopyright() {
        return hasCopyright;
    }

    public void setHasCopyright(int hasCopyright) {
        this.hasCopyright = hasCopyright;
    }

    public int getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(int commentNum) {
        this.commentNum = commentNum;
    }

    public int getTrailerStart() {
        return trailerStart;
    }

    public void setTrailerStart(int trailerStart) {
        this.trailerStart = trailerStart;
    }

    public int getTrailerEnd() {
        return trailerEnd;
    }

    public void setTrailerEnd(int trailerEnd) {
        this.trailerEnd = trailerEnd;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public int getIsListened() {
        return isListened;
    }

    public void setIsListened(int isListened) {
        this.isListened = isListened;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public List<Host> getHost() {
        return host;
    }

    public void setHost(List<Host> host) {
        this.host = host;
    }

    public int getIsThirdParty() {
        return isThirdParty;
    }

    public void setIsThirdParty(int isThirdParty) {
        this.isThirdParty = isThirdParty;
    }

    public int getContentType() {
        return contentType;
    }

    public void setContentType(int contentType) {
        this.contentType = contentType;
    }

    public String getContentTypeName() {
        return contentTypeName;
    }

    public void setContentTypeName(String contentTypeName) {
        this.contentTypeName = contentTypeName;
    }

    public String getMainTitleName() {
        return mainTitleName;
    }

    public void setMainTitleName(String mainTitleName) {
        this.mainTitleName = mainTitleName;
    }

    public String getSubheadName() {
        return subheadName;
    }

    public void setSubheadName(String subheadName) {
        this.subheadName = subheadName;
    }

    public int getHasNextPage() {
        return hasNextPage;
    }

    public void setHasNextPage(int hasNextPage) {
        this.hasNextPage = hasNextPage;
    }

    public String getCallBack() {
        return callBack;
    }

    public void setCallBack(String callBack) {
        this.callBack = callBack;
    }


    public String getSourceLogo() {
        return sourceLogo;
    }

    public void setSourceLogo(String sourceLogo) {
        this.sourceLogo = sourceLogo;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public long getAdZoneId() {
        return adZoneId;
    }

    public void setAdZoneId(long adZoneId) {
        this.adZoneId = adZoneId;
    }

    public String getPlayUrlId() {
        return playUrlId;
    }

    public void setPlayUrlId(String playUrlId) {
        this.playUrlId = playUrlId;
    }

    public int getBuyType() {
        return buyType;
    }

    public void setBuyType(int buyType) {
        this.buyType = buyType;
    }

    public int getBuyStatus() {
        return buyStatus;
    }

    public void setBuyStatus(int buyStatus) {
        this.buyStatus = buyStatus;
    }

    public List<AlbumDetailsPayMethod> getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(List<AlbumDetailsPayMethod> payMethod) {
        this.payMethod = payMethod;
    }

    public int getAlbumIsFine() {
        return albumIsFine;
    }

    public void setAlbumIsFine(int albumIsFine) {
        this.albumIsFine = albumIsFine;
    }

    public int getAlbumIsVip() {
        return albumIsVip;
    }

    public void setAlbumIsVip(int albumIsVip) {
        this.albumIsVip = albumIsVip;
    }

    @Override
    public String toString() {
        return "AudioDetails{" +
                "audioId=" + audioId +
                ", audioName='" + audioName + '\'' +
                ", audioPic='" + audioPic + '\'' +
                ", audioDes='" + audioDes + '\'' +
                ", albumId=" + albumId +
                ", albumName='" + albumName + '\'' +
                ", albumPic='" + albumPic + '\'' +
                ", orderNum=" + orderNum +
                ", mp3PlayUrl32='" + mp3PlayUrl32 + '\'' +
                ", mp3PlayUrl64='" + mp3PlayUrl64 + '\'' +
                ", aacPlayUrl='" + aacPlayUrl + '\'' +
                ", aacPlayUrl32='" + aacPlayUrl32 + '\'' +
                ", aacPlayUrl64='" + aacPlayUrl64 + '\'' +
                ", aacPlayUrl128='" + aacPlayUrl128 + '\'' +
                ", aacPlayUrl320='" + aacPlayUrl320 + '\'' +
                ", aacFileSize=" + aacFileSize +
                ", mp3FileSize32=" + mp3FileSize32 +
                ", mp3FileSize64=" + mp3FileSize64 +
                ", updateTime=" + updateTime +
                ", clockId='" + clockId + '\'' +
                ", duration=" + duration +
                ", originalDuration=" + originalDuration +
                ", listenNum=" + listenNum +
                ", likedNum=" + likedNum +
                ", hasCopyright=" + hasCopyright +
                ", commentNum=" + commentNum +
                ", trailerStart=" + trailerStart +
                ", trailerEnd=" + trailerEnd +
                ", categoryId=" + categoryId +
                ", source='" + source + '\'' +
                ", isListened=" + isListened +
                ", icon='" + icon + '\'' +
                ", host=" + host +
                ", isThirdParty=" + isThirdParty +
                ", contentType=" + contentType +
                ", contentTypeName='" + contentTypeName + '\'' +
                ", mainTitleName='" + mainTitleName + '\'' +
                ", subheadName='" + subheadName + '\'' +
                ", hasNextPage=" + hasNextPage +
                ", callBack='" + callBack + '\'' +
                ", sourceLogo='" + sourceLogo + '\'' +
                ", sourceName='" + sourceName + '\'' +
                ", adZoneId=" + adZoneId + '\'' +
                ", playUrlId='" + playUrlId + '\'' +
                ", buyType=" + buyType + '\'' +
                ", buyStatus=" + buyStatus + '\'' +
                ", payMethod=" + payMethod + '\'' +
                ", albumIsFine=" + albumIsFine +
                ", albumIsVip=" + albumIsVip + '\'' +
                '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(this.audioId);
        dest.writeString(this.audioName);
        dest.writeString(this.audioPic);
        dest.writeString(this.audioDes);
        dest.writeLong(this.albumId);
        dest.writeString(this.albumName);
        dest.writeString(this.albumPic);
        dest.writeInt(this.orderNum);
        dest.writeString(this.mp3PlayUrl32);
        dest.writeString(this.mp3PlayUrl64);
        dest.writeString(this.aacPlayUrl);
        dest.writeString(this.aacPlayUrl32);
        dest.writeString(this.aacPlayUrl64);
        dest.writeString(this.aacPlayUrl128);
        dest.writeInt(this.aacFileSize);
        dest.writeInt(this.mp3FileSize32);
        dest.writeInt(this.mp3FileSize64);
        dest.writeLong(this.updateTime);
        dest.writeString(this.clockId);
        dest.writeInt(this.duration);
        dest.writeInt(this.originalDuration);
        dest.writeInt(this.listenNum);
        dest.writeInt(this.likedNum);
        dest.writeInt(this.hasCopyright);
        dest.writeInt(this.commentNum);
        dest.writeInt(this.trailerStart);
        dest.writeInt(this.trailerEnd);
        dest.writeInt(this.categoryId);
        dest.writeString(this.source);
        dest.writeInt(this.isListened);
        dest.writeString(this.icon);
        dest.writeTypedList(this.host);
        dest.writeInt(this.isThirdParty);
        dest.writeInt(this.contentType);
        dest.writeString(this.contentTypeName);
        dest.writeString(this.mainTitleName);
        dest.writeString(this.subheadName);
        dest.writeInt(this.hasNextPage);
        dest.writeString(this.callBack);
        dest.writeString(this.sourceLogo);
        dest.writeString(this.sourceName);
        dest.writeLong(this.adZoneId);
        dest.writeString(this.playUrlId);
        dest.writeInt(this.buyType);
        dest.writeInt(this.buyStatus);
        dest.writeTypedList(this.payMethod);
        dest.writeInt(this.albumIsFine);
        dest.writeInt(this.albumIsVip);
    }

    public AudioDetails() {
    }

    protected AudioDetails(Parcel in) {
        this.audioId = in.readLong();
        this.audioName = in.readString();
        this.audioPic = in.readString();
        this.audioDes = in.readString();
        this.albumId = in.readLong();
        this.albumName = in.readString();
        this.albumPic = in.readString();
        this.orderNum = in.readInt();
        this.mp3PlayUrl32 = in.readString();
        this.mp3PlayUrl64 = in.readString();
        this.aacPlayUrl = in.readString();
        this.aacPlayUrl32 = in.readString();
        this.aacPlayUrl64 = in.readString();
        this.aacPlayUrl128 = in.readString();
        this.aacFileSize = in.readInt();
        this.mp3FileSize32 = in.readInt();
        this.mp3FileSize64 = in.readInt();
        this.updateTime = in.readLong();
        this.clockId = in.readString();
        this.duration = in.readInt();
        this.originalDuration = in.readInt();
        this.listenNum = in.readInt();
        this.likedNum = in.readInt();
        this.hasCopyright = in.readInt();
        this.commentNum = in.readInt();
        this.trailerStart = in.readInt();
        this.trailerEnd = in.readInt();
        this.categoryId = in.readInt();
        this.source = in.readString();
        this.isListened = in.readInt();
        this.icon = in.readString();
        this.host = in.createTypedArrayList(Host.CREATOR);
        this.isThirdParty = in.readInt();
        this.hasNextPage = in.readInt();
        this.contentType = in.readInt();
        this.contentTypeName = in.readString();
        this.mainTitleName = in.readString();
        this.subheadName = in.readString();
        this.callBack = in.readString();
        this.sourceLogo = in.readString();
        this.sourceName = in.readString();
        this.adZoneId = in.readLong();
        this.playUrlId = in.readString();
        this.buyType = in.readInt();
        this.buyStatus = in.readInt();
        this.payMethod = new ArrayList<>();
        this.albumIsFine = in.readInt();
        this.albumIsVip = in.readInt();
        in.readTypedList(this.payMethod, AlbumDetailsPayMethod.CREATOR);
    }

    public static final Parcelable.Creator<AudioDetails> CREATOR = new Parcelable.Creator<AudioDetails>() {
        @Override
        public AudioDetails createFromParcel(Parcel source) {
            return new AudioDetails(source);
        }

        @Override
        public AudioDetails[] newArray(int size) {
            return new AudioDetails[size];
        }
    };
}
