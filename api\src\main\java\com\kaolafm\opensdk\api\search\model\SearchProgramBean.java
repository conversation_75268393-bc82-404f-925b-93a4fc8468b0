package com.kaolafm.opensdk.api.search.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 文本搜索返回的节目数据
 *
 * <AUTHOR>
 * @date 2018/8/7
 */

public class SearchProgramBean extends VoiceSearchProgramBean implements Parcelable {

    /**
     {
     "id": 1000026417802,
     "name": "京剧《刘兰芝》选段-赵秀君",
     "img": "http://iovimg.radio.cn/mz/images/202104/48e8cc72-af08-428f-9f90-5ae12ff06198/default.jpg",
     "type": 1,
     "albumName": "角儿来了",
     "source": 34,
     "duration": 492982,
     "playUrl": "http://iovimage.radio.cn/mz/aac_64/202104/bb040912-1168-48b8-96cd-69afcf24bd1a.aac",
     "oldId": 0,
     "sourceName": "Unknown",
     "callback": "18_analyzer_01_1650435502107037",
     "fine": 0,
     "vip": 0,
     "audition": 0,
     "totalNumber": 0,
     "host": [],
     "listenNum": 562,
     "isShowRed": 0,
     "isRequest": 1,
     "originalDuration": 492982,
     "highlight": [
     {
     "field": "name",
     "start": -1,
     "offset": -1,
     "token": "刘兰芳"
     },
     {
     "field": "name",
     "start": -1,
     "offset": -1,
     "token": "刘兰"
     }
     ]
     }
     */

    /** 播放量 */
    @SerializedName("listenNum")
    private Long listenNum;

    /** 高亮展示 */
    @SerializedName("highlight")
    private List<HighLightWord> highlight;
    protected SearchProgramBean() {
        super();
    }
    protected SearchProgramBean(Parcel in) {
        super(in);
        if (in.readByte() == 0) {
            listenNum = null;
        } else {
            listenNum = in.readLong();
        }
        highlight = in.createTypedArrayList(HighLightWord.CREATOR);
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        if (listenNum == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(listenNum);
        }
        dest.writeTypedList(highlight);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<SearchProgramBean> CREATOR = new Creator<SearchProgramBean>() {
        @Override
        public SearchProgramBean createFromParcel(Parcel in) {
            return new SearchProgramBean(in);
        }

        @Override
        public SearchProgramBean[] newArray(int size) {
            return new SearchProgramBean[size];
        }
    };

    public Long getListenNum() {
        return listenNum;
    }

    public void setListenNum(Long listenNum) {
        this.listenNum = listenNum;
    }

    public List<HighLightWord> getHighlight() {
        return highlight;
    }

    public void setHighlight(List<HighLightWord> highlight) {
        this.highlight = highlight;
    }

    @Override
    public String toString() {
        return "SearchProgramBean{" +
                "listenNum=" + listenNum +
                ", highlight=" + highlight +
                ", id=" + id +
                ", name='" + name + '\'' +
                ", img='" + img + '\'' +
                ", comperes=" + comperes +
                ", type=" + type +
                ", albumName='" + albumName + '\'' +
                ", duration=" + duration +
                ", playUrl='" + playUrl + '\'' +
                ", vip=" + vip +
                ", fine=" + fine +
                ", audition=" + audition +
                ", callback='" + callback +
                '}';
    }
}
