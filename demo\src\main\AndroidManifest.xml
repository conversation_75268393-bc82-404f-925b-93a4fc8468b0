<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.kaolafm.opensdk.demo">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <application
        android:name=".DemoApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher_yt"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:configChanges="orientation|screenSize|locale|layoutDirection|keyboard|screenLayout"
        android:networkSecurityConfig="@xml/network_security_config"
        android:theme="@style/AppTheme"
        tools:targetApi="n">
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />
        <activity
            android:name=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".detail.DetailActivity"
            android:label="@string/details" />
        <activity android:name=".operation.column.ColumnActivity" />
        <activity android:name=".operation.column.ColumnDetailActivity" />
        <activity android:name=".operation.category.CategoryActivity" />
        <activity android:name=".login.LoginActivity" />
        <activity android:name=".search.VoiceSearchActivity" />
        <activity android:name=".search.SearchResultActivity" />
        <activity android:name=".search.ProgramDetailActivity" />
        <activity android:name=".operation.category.CategoryInfoActivity" />
        <activity android:name=".operation.category.CategoryMemberActivity" />
        <activity android:name=".subcribe.SubscribeActivity" />
        <activity android:name=".login.KaolaLoginActivity" />
        <activity android:name=".login.QQMusicLoginActivity" />
        <activity android:name=".live.LiveActivity" />
        <activity android:name=".qqmusic.CollectionActivity" />
        <activity android:name=".qqmusic.CollectionListActivity" />
        <activity android:name=".account.LinkAccountActivity" />
        <activity android:name=".qqmusic.QQMusicActivity" />

        <service android:name="com.kaolafm.opensdk.player.core.PlayerService" />

        <meta-data
            android:name="com.kaolafm.open.sdk.AppKey"
            android:value="${APP_KEY}" />
        <meta-data
            android:name="com.kaolafm.open.sdk.AppId"
            android:value="${APP_ID}" />
        <meta-data
            android:name="com.kaolafm.open.sdk.Channel"
            android:value="${CHANNEL}" />
        <meta-data
            android:name="com.kaolafm.open.sdk.CarType"
            android:value="xx5" />
       <!-- <meta-data
             android:name="com.kaolafm.open.sdk.freeContent"
             android:value="true"
             />-->
        <!--  <meta-data-->
<!--            android:name="com.kaolafm.ad.AppId"-->
<!--            android:value="8fbf02809128e437bb29383a6f8d0e2a"-->
<!--            />-->

        <!--
             网易聊天室的 APP key, 可以在这里设置，也可以在 SDKOptions 中提供。
            如果 SDKOptions 中提供了，取 SDKOptions 中的值。
        -->
        <meta-data
            android:name="com.netease.nim.appKey"
            android:value="34e5e3c8a9a3e3a29e72c145ab70b5b2" />
        <meta-data
            android:name="com.kaolafm.open.sdk.qqmusic.AppId"
            android:value="12345700" />
        <meta-data
            android:name="com.kaolafm.open.sdk.qqmusic.AppKey"
            android:value="UyZaTlMrnqSJKNLsoy" />

        <activity android:name=".brandinfo.BrandInfoActivity"></activity>
        <activity android:name=".scene.SceneActivity"></activity>
        <activity android:name=".player.RadioPlayerActivity" />
        <activity android:name=".player.RadioPlayerGetListByAreaActivity" />
        <activity android:name=".player.BroadcastPlayerActivity" />
        <activity android:name=".player.BroadcastPlayerGetLocalByIdActivity" />
        <activity android:name=".player.AlbumPlayerActivity" />
        <activity android:name=".player.AudioPlayerActivity" />
        <activity android:name=".player.QQMusicPlayerActivity" />
        <activity android:name=".history.HistoryActivity" />
        <activity android:name=".player.SubscribePlayerActivity" />
        <activity android:name=".search.KeywordSearchActivity" />
        <activity android:name=".personalise.InterestActivity" />
        <activity android:name=".personalise.UserActivity" />
        <activity android:name=".player.AudioDetailActivity" />
        <activity android:name=".DownloadActivity" />
        <activity android:name=".purchase.VipMealsActivity" />
        <activity android:name=".purchase.VipQRCodeActivity" />
        <activity android:name=".purchase.AlbumQRCodeActivity" />
        <activity android:name=".purchase.AudiosQRCodeActivity" />
        <activity android:name=".purchase.QRCodeStatusActivity" />
        <activity android:name=".purchase.BuyAlbumByCoinActivity" />
        <activity android:name=".purchase.BuyAudiosByCoinActivity" />
        <activity android:name=".purchase.PurchasedActivity" />
        <activity android:name=".purchase.OrderActivity" />
        <activity android:name=".activity.ActivitiesActivity" />
    </application>

</manifest>