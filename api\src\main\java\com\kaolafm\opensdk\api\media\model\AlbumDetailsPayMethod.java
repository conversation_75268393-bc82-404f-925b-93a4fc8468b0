package com.kaolafm.opensdk.api.media.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

public class AlbumDetailsPayMethod implements Parcelable {

    /** 购买方式说明 */
    @SerializedName("payTypeName")
    private String payTypeName;

    /** 购买说明，优惠说明 */
    @SerializedName("buyNotice")
    private String buyNotice;

    /** 支付类型 0 云币, 1 人民币 */
    @SerializedName("payType")
    private int payType;

    /** 原价 单位分 */
    @SerializedName("originPrice")
    private Long originPrice;

    /** 当前价格 单位分 */
    @SerializedName("currentPrice")
    private Long currentPrice;

    public String getPayTypeName() {
        return payTypeName;
    }

    public void setPayTypeName(String payTypeName) {
        this.payTypeName = payTypeName;
    }

    public String getBuyNotice() {
        return buyNotice;
    }

    public void setBuyNotice(String buyNotice) {
        this.buyNotice = buyNotice;
    }

    public int getPayType() {
        return payType;
    }

    public void setPayType(int payType) {
        this.payType = payType;
    }

    public Long getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(Long originPrice) {
        this.originPrice = originPrice;
    }

    public Long getCurrentPrice() {
        return currentPrice;
    }

    public void setCurrentPrice(Long currentPrice) {
        this.currentPrice = currentPrice;
    }

    protected AlbumDetailsPayMethod(Parcel in) {
        payTypeName = in.readString();
        buyNotice = in.readString();
        payType = in.readInt();
        originPrice = in.readLong();
        currentPrice = in.readLong();
    }

    @Override
    public String toString() {
        return "AlbumDetailsPayMethod{" +
                "payTypeName='" + payTypeName + '\'' +
                ", buyNotice='" + buyNotice + '\'' +
                ", payType=" + payType +
                ", originPrice=" + originPrice +
                ", currentPrice=" + currentPrice +
                '}';
    }

    public static final Creator<AlbumDetailsPayMethod> CREATOR = new Creator<AlbumDetailsPayMethod>() {
        @Override
        public AlbumDetailsPayMethod createFromParcel(Parcel in) {
            return new AlbumDetailsPayMethod(in);
        }

        @Override
        public AlbumDetailsPayMethod[] newArray(int size) {
            return new AlbumDetailsPayMethod[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(payTypeName);
        dest.writeString(buyNotice);
        dest.writeInt(payType);
        dest.writeLong(originPrice);
        dest.writeLong(currentPrice);
    }
}
