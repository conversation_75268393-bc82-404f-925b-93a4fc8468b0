package com.kaolafm.opensdk.di.component;

import com.kaolafm.opensdk.KradioSDK;
import com.kaolafm.opensdk.Options;
import com.kaolafm.opensdk.di.module.AppModule;
import com.kaolafm.opensdk.di.module.CommonParamModule;
import com.kaolafm.opensdk.di.module.HttpClientModule;
import com.kaolafm.opensdk.di.module.HttpConfigModule;
import com.kaolafm.opensdk.di.scope.AppScope;

import dagger.Component;

/**
 * Kradio SDK的Component，不包含广告
 * <AUTHOR>
 * @date 2018/7/23
 */
@AppScope
@Component(modules = {
        AppModule.class,
        CommonParamModule.class,//应该放在RequestComponent中的，放这里是因为在OpenSDK中用到了InitRequest要用到公共参数
        HttpClientModule.class,
        HttpConfigModule.class,
})
public interface KradioComponent extends CoreComponent<SessionComponent>, Injector<KradioSDK>{

    @Component.Builder
    interface Builder extends CoreComponent.Builder<KradioComponent, Options> {
    }

}
