package com.kaolafm.gradle.plugin.processors

import com.android.build.gradle.api.LibraryVariant
import com.android.build.gradle.internal.pipeline.TransformTask
import com.android.build.gradle.tasks.BundleAar
import com.android.build.gradle.tasks.InvokeManifestMerger
import com.kaolafm.gradle.plugin.OnePublishPlugin
import com.kaolafm.gradle.plugin.model.AndroidArchiveLibrary
import com.kaolafm.gradle.plugin.model.FlavorArtifact
import com.kaolafm.gradle.plugin.model.SDKFlavor
import com.kaolafm.gradle.plugin.tasks.BuildJarTask
import com.kaolafm.gradle.plugin.tasks.BuildProguardTask
import com.kaolafm.gradle.plugin.tasks.LibraryManifestMerger
import com.kaolafm.gradle.plugin.utils.ExplodedHelper
import com.kaolafm.gradle.plugin.utils.TaskFactory
import com.kaolafm.gradle.plugin.utils.Util
import com.kaolafm.gradle.plugin.utils.VersionAdapter
import org.gradle.api.Project
import org.gradle.api.Task
import org.gradle.api.artifacts.ResolvedArtifact
import org.gradle.api.artifacts.ResolvedDependency
import org.gradle.api.file.FileTree
import org.gradle.api.internal.artifacts.DefaultResolvedArtifact
import org.gradle.api.internal.tasks.CachingTaskDependencyResolveContext
import org.gradle.api.tasks.Copy
import org.gradle.api.tasks.TaskDependency
import org.gradle.api.tasks.bundling.Zip
/**
 * Processor for variant
 * Created by Vigi on 2017/2/24.
 * Modified by kezong on 2019/05/29
 */
class VariantProcessor {

    private final Project mProject

    private final LibraryVariant mVariant

    private Set<ResolvedArtifact> mResolvedArtifacts = new ArrayList<>()

    private Collection<AndroidArchiveLibrary> mAndroidArchiveLibraries = new ArrayList<>()

    private Collection<File> mJarFiles = new ArrayList<>()

    private Collection<Task> mExplodeTasks = new ArrayList<>()

    private String mGradlePluginVersion

    private VersionAdapter mVersionAdapter
    private BundleAar mBundleTask

    VariantProcessor(Project project, LibraryVariant variant) {
        mProject = project
        mVariant = variant
        mVersionAdapter = new VersionAdapter(project, variant)
        mGradlePluginVersion = mVersionAdapter.gradleVersion
    }

    void addArtifacts(Set<ResolvedArtifact> resolvedArtifacts) {
        mResolvedArtifacts.addAll(resolvedArtifacts)
    }

    void addAndroidArchiveLibrary(AndroidArchiveLibrary library) {
        mAndroidArchiveLibraries.add(library)
    }

    void addUnResolveArtifact(Set<ResolvedDependency> dependencies) {
        if (dependencies != null) {
            dependencies.each {
                def artifact = FlavorArtifact.createFlavorArtifact(mProject, mVariant, it, mGradlePluginVersion)
                mResolvedArtifacts.add(artifact)
            }
        }
    }

    void addJarFile(File jar) {
        mJarFiles.add(jar)
    }

    void processVariant() {
        String taskPath = 'pre' + mVariant.name.capitalize() + 'Build'
        Task prepareTask = mProject.tasks.findByPath(taskPath)
        if (prepareTask == null) {
            throw new RuntimeException("Can not find task ${taskPath}!")
        }
        taskPath = 'bundle' + mVariant.name.capitalize()
        mBundleTask = mProject.tasks.findByPath(taskPath)
        if (mBundleTask == null) {
            taskPath = 'bundle' + mVariant.name.capitalize() + "Aar"
            mBundleTask = mProject.tasks.findByPath(taskPath)
        }
        if (mBundleTask == null) {
            throw new RuntimeException("Can not find task ${taskPath}!")
        }
        processCache()
        processArtifacts(prepareTask, mBundleTask)
        processClassesAndJars(mBundleTask)
        if (mAndroidArchiveLibraries.isEmpty()) {
            return
        }
        processManifest()
        processResourcesAndR()
        processAssets()
        processJniLibs()
        RProcessor rProcessor = new RProcessor(mProject, mVariant, mAndroidArchiveLibraries, mVersionAdapter)
        rProcessor.inject(mBundleTask)
    }

    void rebundleAar(SDKFlavor flavor) {
        def aarPath = mVersionAdapter.aarOutputPath
        // aar unzip file
        def aarUnZipDir = mVersionAdapter.unzipDir
        // aar output dir
        def aarOutputDir = mVersionAdapter.aarOutputDir
        def aarOutputPath = mVersionAdapter.aarOutputPath
        def rebuildTask = createRebuildTask(aarUnZipDir, aarPath, flavor, mBundleTask)
        def reBundleAar = createBundleAarTask(aarUnZipDir, aarOutputDir, aarOutputPath)

        mBundleTask.doLast {
            // support gradle 5.1 && gradle plugin 3.4 before, the outputName is changed
            File file = new File(aarPath)
            if (!file.exists()) {
                mAarOutputPath = mVersionAdapter.aarOutputDir.absolutePath + "/" + mProject.name + ".aar"
                reBundleAar.archiveName = new File(mAarOutputPath).name
            }
        }

        rebuildTask.finalizedBy(reBundleAar)
    }

    private Task createRebuildTask(File unzipDir, String aarPath, SDKFlavor flavor, Task bundleTask) {
        if (flavor != null) {
            BuildJarTask buildJar = TaskFactory.createBuildJar(mProject, flavor.name)
            String classPath = "${unzipDir.path}/classes"
            buildJar.config(flavor, "${unzipDir.path}/classes")
            def rsJarTask = mProject.tasks.findByName("createRsJar${mVariant.name.capitalize()}")
            buildJar.dependsOn bundleTask
            if (rsJarTask != null) {
                println("buildJar.mustRunAfter rsJarTask")
                buildJar.mustRunAfter rsJarTask
            }
            BuildProguardTask proguardTask = TaskFactory.createBuildProguardTask(mProject, flavor.name)
            proguardTask.config(flavor, "${unzipDir.path}/classes.jar")
            buildJar.doFirst {
                unzipDir.deleteOnExit()
                File clazz = new File(classPath)
                clazz.deleteDir()
                FileTree aarFiles = mProject.zipTree(aarPath)
                List<File> jars = new ArrayList<>()
                aarFiles.each {
                    println("it=$it")
                    if (it.name.endsWith(".jar") && it.name != "r-classes.jar") {
                        jars.add(it)
                    }
                }
                println("jars=${jars}")
                mProject.copy {
                    from aarFiles
                    into unzipDir
                    includeEmptyDirs = false
                    exclude {
                        jars.contains(it.file)
                    }
                }
                jars.each { file ->
                    mProject.copy {
                        from mProject.zipTree(file)
                        into classPath
                        includeEmptyDirs = false
                    }
                }
            }
            proguardTask.doLast {
                File file = mProject.file(classPath)
                file.deleteDir()
            }
            proguardTask.dependsOn buildJar
            return proguardTask
        }
    }

    private Task createBundleAarTask(final File fromDir, final File destDir, final String filePath) {
        String taskName = "reBundleAar${mVariant.name.capitalize()}"
        Task task = TaskFactory.createTask(mProject, taskName, Zip.class, {
            it.from fromDir
            it.include "**"
            it.archiveName = new File(filePath).name
            it.destinationDir(destDir)
        })

        task.doFirst {
            Util.deleteEmptyDir(fromDir)
        }

        task.doLast {
            Util.logAnytime("target: $destDir/${new File(filePath).name}")
        }
        return task
    }

    private void processCache() {
        if (Util.compareVersion(mGradlePluginVersion, "3.5.0") >= 0) {
            mVersionAdapter.getLibsDirFile().deleteDir()
            mVersionAdapter.getClassPathDirFiles().first().deleteDir()
        }
        mProject.delete(mProject.buildDir)
    }

    /**
     * exploded artifact files
     */
    private void processArtifacts(Task prepareTask, Task bundleTask) {
        for (final DefaultResolvedArtifact artifact in mResolvedArtifacts) {
            if (OnePublishPlugin.ARTIFACT_TYPE_JAR == artifact.type) {
                addJarFile(artifact.file)
            } else if (OnePublishPlugin.ARTIFACT_TYPE_AAR == artifact.type) {
                AndroidArchiveLibrary archiveLibrary = new AndroidArchiveLibrary(mProject, artifact, mVariant.name)
                addAndroidArchiveLibrary(archiveLibrary)
                Set<Task> dependencies
                if (artifact.buildDependencies instanceof TaskDependency) {
                    dependencies = artifact.buildDependencies.getDependencies()
                } else {
                    CachingTaskDependencyResolveContext context = new CachingTaskDependencyResolveContext()
                    artifact.buildDependencies.visitDependencies(context)
                    if (context.queue.size() == 0) {
                        dependencies = new HashSet<>()
                    } else {
                        dependencies = context.queue.getFirst().getDependencies()
                    }
                }
                archiveLibrary.getRootFolder().deleteDir()
                final def zipFolder = archiveLibrary.getRootFolder()
                zipFolder.mkdirs()
                def group = artifact.getModuleVersion().id.group.capitalize()
                def name = artifact.name.capitalize()
                String taskName = "explode${group}${name}${mVariant.name.capitalize()}"
                Task explodeTask = mProject.tasks.findByPath(taskName)
                if (explodeTask == null) {
                    explodeTask = mProject.tasks.create(name: taskName, type: Copy) {
                        from mProject.zipTree(artifact.file.absolutePath)
                        into zipFolder
                    }
                }

                if (dependencies.size() == 0) {
                    explodeTask.dependsOn(prepareTask)
                } else {
                    explodeTask.dependsOn(dependencies.first())
                }
                Task javacTask = mVersionAdapter.getJavaCompileTask()
                javacTask.dependsOn(explodeTask)
                bundleTask.dependsOn(explodeTask)
                mExplodeTasks.add(explodeTask)
            }
        }
    }

    /**
     * merge manifest
     */
    private void processManifest() {
        Task processManifestTask = mVersionAdapter.getProcessManifest()
        File manifestOutputBackup
        if (mGradlePluginVersion != null && Util.compareVersion(mGradlePluginVersion, "3.3.0") >= 0) {
            manifestOutputBackup = mProject.file("${mProject.buildDir.path}/intermediates/library_manifest/${mVariant.name}/AndroidManifest.xml")
        } else {
            manifestOutputBackup = mProject.file(processManifestTask.getManifestOutputDirectory().absolutePath + '/AndroidManifest.xml')
        }
        InvokeManifestMerger manifestsMergeTask = TaskFactory.createTask(mProject, "merge${mVariant.name.capitalize()}Manifest", LibraryManifestMerger.class, null)
        manifestsMergeTask.setGradleVersion(mProject.getGradle().getGradleVersion())
        manifestsMergeTask.setGradlePluginVersion(mGradlePluginVersion)
        manifestsMergeTask.setVariantName(mVariant.name)
        manifestsMergeTask.setMainManifestFile(manifestOutputBackup)
        List<File> list = new ArrayList<>()
        for (archiveLibrary in mAndroidArchiveLibraries) {
            list.add(archiveLibrary.getManifest())
        }
        manifestsMergeTask.setSecondaryManifestFiles(list)
        manifestsMergeTask.setOutputFile(manifestOutputBackup)
        manifestsMergeTask.dependsOn processManifestTask
        manifestsMergeTask.doFirst {
            List<File> existFiles = new ArrayList<>()
            manifestsMergeTask.getSecondaryManifestFiles().each {
                if (it.exists()) {
                    existFiles.add(it)
                }
            }
            manifestsMergeTask.setSecondaryManifestFiles(existFiles)
        }

        mExplodeTasks.each { it ->
            manifestsMergeTask.dependsOn it
        }

        processManifestTask.finalizedBy manifestsMergeTask
    }

    private Task handleClassesMergeTask(final boolean isMinifyEnabled) {
        final Task task = TaskFactory.create(mProject, "mergeClasses${mVariant.name.capitalize()}")
        task.doFirst {
            def dustDir = mVersionAdapter.getClassPathDirFiles().first()
            if (isMinifyEnabled) {
                ExplodedHelper.processClassesJarInfoClasses(mProject, mAndroidArchiveLibraries, dustDir)
                ExplodedHelper.processLibsIntoClasses(mProject, mAndroidArchiveLibraries, mJarFiles, dustDir)
            } else {
                ExplodedHelper.processClassesJarInfoClasses(mProject, mAndroidArchiveLibraries, dustDir)
            }
        }
        return task
    }

    private Task handleJarMergeTask() {
        final Task task = TaskFactory.create(mProject, "mergeJars${mVariant.name.capitalize()}")
        task.doFirst {
            ExplodedHelper.processLibsIntoLibs(mProject, mAndroidArchiveLibraries, mJarFiles, mVersionAdapter.getLibsDirFile())
        }
        return task
    }

    /**
     * merge classes and jars
     */
    private void processClassesAndJars(Task bundleTask) {
        boolean isMinifyEnabled = mVariant.getBuildType().isMinifyEnabled()
        if (isMinifyEnabled) {
            //merge proguard file
            for (archiveLibrary in mAndroidArchiveLibraries) {
                List<File> thirdProguardFiles = archiveLibrary.proguardRules
                for (File file : thirdProguardFiles) {
                    if (file.exists()) {
                        Util.logInfo('add proguard file: ' + file.absolutePath)
                        mProject.android.getDefaultConfig().proguardFile(file)
                    }
                }
            }
        }

        String taskPath = mVersionAdapter.getSyncLibJarsTaskPath()
        TransformTask syncLibTask = mProject.tasks.findByPath(taskPath)
        if (syncLibTask == null) {
            throw new RuntimeException("Can not find task ${taskPath}!")
        }

        Task javacTask = mVersionAdapter.getJavaCompileTask()
        Task mergeClasses = handleClassesMergeTask(isMinifyEnabled)
        syncLibTask.dependsOn(mergeClasses)
        mExplodeTasks.each { it ->
            mergeClasses.dependsOn it
        }
        mergeClasses.dependsOn(javacTask)
        if (!isMinifyEnabled) {
            Task mergeJars = handleJarMergeTask()
            mergeJars.mustRunAfter(syncLibTask)
            bundleTask.dependsOn(mergeJars)
            mExplodeTasks.each { it ->
                mergeJars.dependsOn it
            }
            mergeJars.dependsOn(javacTask)
        }
    }

    /**
     * merge R.txt(actually is to fix issue caused by provided configuration) and res
     *
     * Here I have to inject res into "main" instead of "variant.name".
     * To avoid the res from embed dependencies being used, once they have the same res Id with main res.
     *
     * Now the same res Id will cause a build exception: Duplicate resources, to encourage you to change res Id.
     * Adding "android.disableResourceValidation=true" to "gradle.properties" can do a trick to skip the exception, but is not recommended.
     */
    private void processResourcesAndR() {
        String taskPath = 'generate' + mVariant.name.capitalize() + 'Resources'
        Task resourceGenTask = mProject.tasks.findByPath(taskPath)
        if (resourceGenTask == null) {
            throw new RuntimeException("Can not find task ${taskPath}!")
        }

        resourceGenTask.doFirst {
            for (archiveLibrary in mAndroidArchiveLibraries) {
                mProject.android.sourceSets.each {
                    if (it.name == mVariant.name) {
                        Util.logInfo("Merge resource，Library res：${archiveLibrary.resFolder}")
                        it.res.srcDir(archiveLibrary.resFolder)
                    }
                }
            }
        }

        mExplodeTasks.each { it ->
            resourceGenTask.dependsOn(it)
        }
    }

    /**
     * merge assets
     *
     * AaptOptions.setIgnoreAssets and AaptOptions.setIgnoreAssetsPattern will work as normal
     */
    private void processAssets() {
        Task assetsTask = mVersionAdapter.getMergeAssets()
        if (assetsTask == null) {
            throw new RuntimeException("Can not find task in variant.getMergeAssets()!")
        }

        assetsTask.doFirst {
            for (archiveLibrary in mAndroidArchiveLibraries) {
                if (archiveLibrary.assetsFolder != null && archiveLibrary.assetsFolder.exists()) {
                    mProject.android.sourceSets.each {
                        if (it.name == mVariant.name) {
                            it.assets.srcDir(archiveLibrary.assetsFolder)
                        }
                    }
                }
            }
        }

        mExplodeTasks.each { it ->
            assetsTask.dependsOn it
        }
    }

    /**
     * merge jniLibs
     */
    private void processJniLibs() {
        String taskPath = 'merge' + mVariant.name.capitalize() + 'JniLibFolders'
        Task mergeJniLibsTask = mProject.tasks.findByPath(taskPath)
        if (mergeJniLibsTask == null) {
            throw new RuntimeException("Can not find task ${taskPath}!")
        }

        mergeJniLibsTask.doFirst {
            for (archiveLibrary in mAndroidArchiveLibraries) {
                if (archiveLibrary.jniFolder != null && archiveLibrary.jniFolder.exists()) {
                    mProject.android.sourceSets.each {
                        if (it.name == mVariant.name) {
                            it.jniLibs.srcDir(archiveLibrary.jniFolder)
                        }
                    }
                }
            }
        }

        mExplodeTasks.each { it ->
            mergeJniLibsTask.dependsOn it
        }
    }
}
