package com.kaolafm.opensdk.api.recommend;


import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.recommend.model.BaseSceneListData;
import com.kaolafm.opensdk.api.recommend.model.SceneDataList;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2019-07-18.
 * 负反馈请求, 使用opensdk网络请求.
 */

public class RecRequest extends BaseRequest {

    private RecApiService mRecApiService;

    public RecRequest() {
        mUrlManager.putDomain(RecRequestConstant.DOMAIN_NAME_MINUS_FEED_BACK, RecRequestConstant.AI_RADIO_FEED_BACK);
        mRecApiService = obtainRetrofitService(RecApiService.class);
    }

    /**
     * 获取场景电台列表
     *
     * @param code
     * @param callback
     */
    public void getSceneRadioList(String code, String size, String deviceId,Map<String,String> extraParams, HttpCallback<BaseSceneListData<List<SceneDataList>>> callback) {
        HashMap<String, String> tempMap = new HashMap<>();
        tempMap.put("code", code);
        tempMap.put("size", size);

        // 搜索接口的appid devicesid openid 参数的I是大写，app 接口中是小写。。所以需要更改。
        tempMap.put("appId",  mProfileLazy.get().getAppId());
        tempMap.put("deviceId",  deviceId);
        tempMap.put("openId", AccessTokenManager.getInstance().getKaolaAccessToken().getOpenId());
//        CommonRequestParamsUtil.getCommonParams()
        tempMap.putAll(extraParams);
        doHttpDeal(mRecApiService.getSceneRadioList(tempMap), callback);
    }

}
