package com.kaolafm.ad.timer;

import android.support.annotation.NonNull;

import java.util.Arrays;

/**
 * 定时任务task，可以在LinkedHashSet中保证有序且唯一。
 * <AUTHOR>
 * @date 2020-02-06
 */
public class AdvertTask implements Task, Comparable {

    private int id;

    private long timeDuration;

    private long timestamp;

    public AdvertTask(int id, long timestamp) {
        this.id = id;
        this.timestamp = timestamp;
    }

    public AdvertTask(int id, long timeDuration, long timestamp) {
        this.id = id;
        this.timeDuration = timeDuration;
        this.timestamp = timestamp;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setTimeDuration(long timeDuration) {
        this.timeDuration = timeDuration;
    }

    public long getTimeDuration() {
        return timeDuration;
    }

    public int getId() {
        return id;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AdvertTask that = (AdvertTask) o;
        return id == that.id && timestamp == that.timestamp;
    }

    @Override
    public int hashCode() {
        return Arrays.hashCode(new Object[]{id, timestamp});
    }

    @Override
    public int compareTo(@NonNull Object o) {
        return (int) ((timestamp - ((AdvertTask) o).timestamp) / 1000);
    }
}
