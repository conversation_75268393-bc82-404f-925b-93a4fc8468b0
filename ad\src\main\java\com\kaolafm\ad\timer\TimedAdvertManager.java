package com.kaolafm.ad.timer;

import com.kaolafm.ad.api.internal.AdInternalRequest;
import com.kaolafm.ad.api.model.AdvertisingDetails;
import com.kaolafm.ad.db.manager.AdvertDBManager;
import com.kaolafm.ad.di.component.AdvertSubcomponent;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.ad.util.DownloadUtil;
import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.di.component.ComponentKit;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.http.socket.SocketListener;

import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import javax.inject.Inject;

/**
 * 定时广告管理类.
 *
 * <AUTHOR>
 * @date 2020-02-06
 */
public class TimedAdvertManager {

    private static volatile TimedAdvertManager mInstance;

    private Set<AdvertTask> mTasks = new LinkedHashSet<>();

    @Inject
    @AppScope
    AdvertDBManager mAdvertDBManager;

    @Inject
    @AppScope
    AdInternalRequest mInternalRequest;

    @Inject
    @AppScope
    Timer mTimer;

    private TimedAdvertManager() {
        AdvertSubcomponent subcomponent = ComponentKit.getInstance().getSubcomponent();
        subcomponent.inject(this);
    }

    public static TimedAdvertManager getInstance() {
        if (mInstance == null) {
            synchronized (TimedAdvertManager.class) {
                if (mInstance == null) {
                    mInstance = new TimedAdvertManager();
                }
            }
        }
        return mInstance;
    }


    public void start(int zoneId, String picWidth, String picHeight) {
        mTimer.start();
        mInternalRequest.getTimedAdvertList(String.valueOf(zoneId), picWidth, picHeight, "3,4,5", null, new SocketListener<List<AdvertisingDetails>>() {
            @Override
            public void onSuccess(List<AdvertisingDetails> adCreatives) {
                updateTimer(adCreatives);
            }

            @Override
            public void onError(ApiException exception) {
            }
        });

    }

    private void updateTimer(List<AdvertisingDetails> adCreatives) {
        if (ListUtil.isEmpty(adCreatives)) {
            return;
        }
        Set<AdvertTask> oldTasks = new LinkedHashSet<>(mTasks);
        mTasks.clear();
        for (AdvertisingDetails details : adCreatives) {
            List<String> times = details.getAdPlayTimestamps();
            //立即曝光
            if (ListUtil.isEmpty(times)) {
                AdvertisingManager.getInstance().expose(details);
                continue;
            }
            boolean canSave = false;
            for (String time : times) {
                long timestamp = DateUtil.string2Millis(time);
                long timeDuration = timestamp - DateUtil.getServerTime();
                if (timeDuration > 0) {
                    addTask(new AdvertTask(details.getCreativeId().intValue(), timeDuration, timestamp));
                    canSave = true;
                }
            }
            if (canSave) {
                DownloadUtil.download(details);
                mAdvertDBManager.insert(details);
            }
        }
        cancelOldTask(oldTasks);
    }

    /**
     * 更新数据后，取消已经设置过且最新数据中没有的task
     * @param oldTasks
     */
    private void cancelOldTask(Set<AdvertTask> oldTasks) {
        oldTasks.removeAll(mTasks);
        for (AdvertTask oldTask : oldTasks) {
            mTimer.removeTask(oldTask);
        }
    }

    /**
     * 添加一个定时任务
     *
     * @param task
     */
    public void addTask(AdvertTask task) {
        mTimer.addTask(task);
        mTasks.add(task);
    }

    /**
     * 添加一个定时任务集合
     *
     * @param tasks
     */
    public void addTasks(List<AdvertTask> tasks) {
        mTasks.addAll(tasks);
        for (AdvertTask task : tasks) {
            addTask(task);
        }
    }

    /**
     * 移除一个定时任务
     *
     * @param timestamp 指定的时间戳
     */
    public void removeTask(long timestamp) {
        Iterator<AdvertTask> iterator = mTasks.iterator();
        while (iterator.hasNext()) {
            AdvertTask task = iterator.next();
            if (task.getTimestamp() == timestamp) {
                iterator.remove();
                mTimer.removeTask(task);
            }
        }
    }

    public void removeTask(AdvertTask task) {
        mTasks.remove(task);
        mTimer.removeTask(task);
    }

    public Timer getTimer() {
        return mTimer;
    }

    /**
     * 清除定时任务。
     * 只取消定时任务
     */
    public void clear() {
        //在stop里就已经经数据库的缓存删除了
        mTimer.stop();
        mTasks.clear();
    }

    /**
     * 中止定时广告并停止长连接
     * 会清除定时缓存。
     */
    public void stop() {
        clear();
        mAdvertDBManager.deleteTimedAdvert();
        mInternalRequest.stopSocket();
    }
}
