package com.kaolafm.opensdk.demo.player;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.media.AudioManager;
import android.os.Bundle;
import android.support.v7.widget.DividerItemDecoration;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.text.method.ScrollingMovementMethod;
import android.view.View;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemSelectedListener;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.SeekBar.OnSeekBarChangeListener;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.google.gson.GsonBuilder;
import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.opensdk.api.history.HistoryRequest;
import com.kaolafm.opensdk.api.subscribe.SubscribeRequest;
import com.kaolafm.opensdk.api.subscribe.SubscribeStatus;
import com.kaolafm.opensdk.demo.AntiShake;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.PlayerStateListenerWrapper;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.demo.detail.StringAdapter;
import com.kaolafm.opensdk.demo.detail.StringAdapter.Item;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IGeneralListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListControl;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.ToneQualityHelper;
import com.lcodecore.tkrefreshlayout.RefreshListenerAdapter;
import com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout;


import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR> Yan
 * @date 2018/12/6
 */

public abstract class BasePlayerActivity extends BaseActivity {

    public static final String KEY_ID = "id";

    public static final String KEY_TYPE = "type";

    public static final String KEY_DATE = "date";

    public static final String KEY_AREA_CODE = "areaCode";

    public static final String KEY_AREA_CITY_NAME = "areaCityName";

    @BindView(R.id.iv_detail_play_next)
    ImageView mIvDetailPlayNext;

    @BindView(R.id.iv_detail_play_pause)
    ImageView mIvDetailPlayPause;

    @BindView(R.id.iv_detail_play_pre)
    ImageView mIvDetailPlayPre;

    @BindView(R.id.sb_detail_progress)
    SeekBar mSbDetailProgress;

    @BindView(R.id.spinner_player_change_quality)
    Spinner mSpinnerPlayerChangeQuality;

    @BindView(R.id.switch_audio_focus)
    Switch mSwitchAudioFocus;

    @BindView(R.id.trf_detail_playlist)
    TwinklingRefreshLayout mTrfDetailPlaylist;

    @BindView(R.id.iv_detail_cover)
    ImageView ivCover;

    @BindView(R.id.tv_details_content)
    TextView tvDetails;

    @BindView(R.id.rv_detail_playlist)
    RecyclerView mRecyclerView;

    @BindView(R.id.btn_detail_subscribe)
    Button btnSubscribe;

    protected StringAdapter mAdapter;

    protected PlayerStateListenerWrapper mPlayerStateListener;

    private LinearLayoutManager mLayoutManager;

//    protected OnDownloadProgressListener mOnDownloadProgressListener;

    protected Long[] mIds;
    /**
     * 当前页面显示数据的id，电台、专辑、在线广播、QQ音乐、直播等。
     */
    protected long mId;

    protected int mType;
    protected String mDate;
    protected String mAreaCode;
    protected String mAreaCityName;

    protected boolean isSubscribed;

    private int mCurrentFocus = AudioManager.AUDIOFOCUS_NONE;

    private SharedPreferences mSharedPreferences;

    protected IGeneralListener iGeneralListener = new IGeneralListener() {
        @Override
        public void getPlayListError(int i) {
            showToast("getPlayListError, error code = "+i);
        }

        @Override
        public void playUrlError(int i) {
            showToast("playUrlError, error code = "+i);
        }
    };
    private OnAudioFocusChangeInter onAudioFocusChangeInter = new OnAudioFocusChangeInter() {

        @Override
        public void onAudioFocusChange(int focusChange) {
            if(focusChange == AudioManager.AUDIOFOCUS_GAIN){
                mSwitchAudioFocus.setChecked(true);
            }else {
                mSwitchAudioFocus.setChecked(false);
            }
            showToast("onAudioFocusChange, focusChange = "+focusChange);
        }
    };

    @Override
    public int getLayoutId() {
        return R.layout.activity_player;
    }

    @Override
    public void initArgs() {
        super.initArgs();
        String ids = getIntent().getStringExtra(KEY_ID);
        String[] idsArr = ids.split(",");
        Long[] idsArrLong = new Long[idsArr.length];
        for(int i=0; i<idsArr.length; i++){
            idsArrLong[i] = Long.parseLong(idsArr[i]);
        }
        mIds = idsArrLong;
        mId = Long.parseLong(idsArr[0]);
        mType = getIntent().getIntExtra(KEY_TYPE, 0);
        mDate = getIntent().getStringExtra(KEY_DATE);
        mAreaCode = getIntent().getStringExtra(KEY_AREA_CODE);
        mAreaCityName = getIntent().getStringExtra(KEY_AREA_CITY_NAME);
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        PlayerManager.getInstance().addAudioFocusListener(onAudioFocusChangeInter);
        PlayerManager.getInstance().addGeneralListener(iGeneralListener);

        mLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        mRecyclerView.setLayoutManager(mLayoutManager);
        mRecyclerView.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
        mAdapter = new StringAdapter();
        //item的点击事件
        mAdapter.setOnItemClickListener((view, viewType, item, position) -> playItem(item));
        //item中的按钮的点击事件
        mAdapter.setOnBtnClickListener(item -> {
            String itemStr = new GsonBuilder()
                    .serializeNulls()
                    .setPrettyPrinting()
                    .create().toJson(item);
            Intent intent = new Intent(BasePlayerActivity.this, AudioDetailActivity.class);
            intent.putExtra(AudioDetailActivity.KEY_DETAIL, itemStr);
            startActivity(intent);
        });
        mRecyclerView.setAdapter(mAdapter);
        tvDetails.setMovementMethod(ScrollingMovementMethod.getInstance());

        mSharedPreferences = getSharedPreferences("AudioFocusLogic", Context.MODE_PRIVATE);
        boolean canUse = mSharedPreferences.getBoolean("Default", true);
        mSwitchAudioFocus.setChecked(canUse);
        //设置是否使用SDK内播放器音频焦点逻辑
//        mPlayerManager.setCanUseDefaultAudioFocusLogic(canUse);

        mTrfDetailPlaylist.setEnableRefresh(false);
        mTrfDetailPlaylist.setEnableLoadmore(true);

        initListener();

        mSpinnerPlayerChangeQuality.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                String toastMsg = "";
                switch (position) {
                    case 0: {
                        ToneQualityHelper.getInstance().setToneQuality(PlayerConstants.ToneQuality.LOW_TONE_QUALITY);
                        toastMsg = "切换到低音质";
                    }
                    break;
                    case 1: {
                        ToneQualityHelper.getInstance().setToneQuality(PlayerConstants.ToneQuality.MIDDLE_TONE_QUALITY);
                        toastMsg = "切换到中音质";
                    }
                    break;
                    case 2: {
                        ToneQualityHelper.getInstance().setToneQuality(PlayerConstants.ToneQuality.HIGH_TONE_QUALITY);
                        toastMsg = "切换到高音质";
                    }
                    break;
                    case 3: {
                        ToneQualityHelper.getInstance().setToneQuality(PlayerConstants.ToneQuality.HIGHER_TONE_QUALITY);
                        toastMsg = "切换到超高音质";
                    }
                    break;
                    default: {
                    }
                    break;
                }
                showToast(toastMsg);
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
        mSpinnerPlayerChangeQuality.setSelection(1);
    }

    @Override
    public void initData() {
        select(getCurrentPosition());
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        PlayerManager.getInstance().removeAudioFocusListener(onAudioFocusChangeInter);
        PlayerManager.getInstance().removeGeneralListener(iGeneralListener);
    }

    private void initListener() {
        mPlayerStateListener = new PlayerStateListenerWrapper() {
            @Override
            public void onIdle(PlayItem playItem) {
                super.onIdle(playItem);
                if (mIvDetailPlayPause != null) {
                    mIvDetailPlayPause.setActivated(false);
                }
                if (playItem != null && mSbDetailProgress != null) {
                    mSbDetailProgress.setMax(playItem.getDuration());
                }

            }

            @Override
            public void onPlayerPlaying(PlayItem playItem) {
                super.onPlayerPlaying(playItem);
                if (mIvDetailPlayPause != null) {
                    mIvDetailPlayPause.setActivated(true);
                }
                select(getCurrentPosition());
                saveHistory(playItem);
            }

            @Override
            public void onPlayerPaused(PlayItem playItem) {
                super.onPlayerPaused(playItem);
                if (mIvDetailPlayPause != null) {
                    mIvDetailPlayPause.setActivated(false);
                }
            }

            @Override
            public void onProgress(PlayItem playItem, long progress, long total) {
                super.onProgress(playItem, progress, total);
                if (mSbDetailProgress != null) {
                    mSbDetailProgress.setProgress((int) progress);
                }
            }

            @Override
            public void onSeekStart(PlayItem playItem) {

            }

            @Override
            public void onSeekComplete(PlayItem playItem) {

            }

            @Override
            public void onDownloadProgress(PlayItem playItem, long progress, long total) {
                super.onDownloadProgress(playItem, progress, total);
                if ((progress > 0 && progress <= total && mSbDetailProgress != null) ){
                    int max = mSbDetailProgress.getMax();
                    long secondaryProgress = (progress / total) * max;
                    mSbDetailProgress.setSecondaryProgress((int) secondaryProgress);
                }
            }

            @Override
            public void onPlayerFailed(PlayItem playItem, int what, int extra) {
                super.onPlayerFailed(playItem, what, extra);
                String error = "播放失败, code = "+what;
                if(what == extra && what == 50811){
                    error +=", 付费节目，购买后即可畅听！";
                }
                showToast(error);
            }

            @Override
            public void onPlayerEnd(PlayItem playItem) {
                super.onPlayerEnd(playItem);
            }
        };

        mSwitchAudioFocus.setOnCheckedChangeListener(
                (buttonView, isChecked) -> {
                    if(isChecked){
                        boolean b = PlayerManager.getInstance().requestAudioFocus();
                        if(b){
                            PlayerManager.getInstance().play();
                        }
                        showToast("请求音频焦点结果:"+b);
                    }else {
                        boolean b = PlayerManager.getInstance().abandonAudioFocus();
                        if(b){
                            PlayerManager.getInstance().pause();
                        }
                        showToast("释放音频焦点结果:"+b);
                    }
                    mSharedPreferences.edit().putBoolean("Default", isChecked).apply();
                });

        mTrfDetailPlaylist.setOnRefreshListener(new RefreshListenerAdapter() {
            @Override
            public void onLoadMore(TwinklingRefreshLayout refreshLayout) {
                super.onLoadMore(refreshLayout);
                loadMore();
            }

            @Override
            public void onRefresh(TwinklingRefreshLayout refreshLayout) {
                super.onRefresh(refreshLayout);
                refresh();
            }
        });

        mSbDetailProgress.setOnSeekBarChangeListener(new OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    seek(progress);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });

    }

    protected void select(int curPosition) {
        if (curPosition >= 0 && curPosition < mLayoutManager.getItemCount()) {
            if (mRecyclerView != null) {
                mRecyclerView.smoothScrollToPosition(curPosition);
                mAdapter.setSelected(curPosition);
            }
        }
    }
    protected void select() {
        IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
        if (playListControl == null) {
            PlayerLogUtil.log(getClass().getSimpleName(), "playPlayListPosition", "playPlayListPosition playListControl is null");
            return;
        }
        select(playListControl.getCurPosition());
    }


    @OnClick({R.id.iv_detail_play_pre, R.id.iv_detail_play_pause, R.id.iv_detail_play_next,
            R.id.btn_detail_subscribe})
    public void onViewClicked(View view) {
        if (AntiShake.check(view.getId())) {
            return;
        }
        switch (view.getId()) {
            case R.id.iv_detail_play_pre:
                playPre();
                break;
            case R.id.iv_detail_play_pause:
                switchPlayPause();
                break;
            case R.id.iv_detail_play_next:
                playNext();
                break;
            case R.id.btn_detail_subscribe:
                subscribe(!isSubscribed);
                break;
            default:
                break;
        }
    }

    /**
     * 播放上一首
     */
    protected abstract void playPre();

    /**
     * 切换暂停和播放
     */
    protected abstract void switchPlayPause();

    /**
     * 播放下一首
     */
    protected abstract void playNext();

    /**
     * 订阅/取消订阅
     */
    protected void subscribe(boolean isSubscribe) {
        //订阅
        if (isSubscribe) {
            new SubscribeRequest().subscribe(mId, new HttpCallback<SubscribeStatus>() {
                @Override
                public void onSuccess(SubscribeStatus subscribeStatus) {
                    if (subscribeStatus.getStatus() == SubscribeStatus.STATE_SUCCESS) {
                        btnSubscribe.setText("取消订阅");
                        isSubscribed = true;
                        showToast("订阅成功");
                    }
                }

                @Override
                public void onError(ApiException exception) {
                    showError("订阅失败", exception);
                }
            });
        }
        //取消订阅
        else {
            new SubscribeRequest().unsubscribe(mId, new HttpCallback<Boolean>() {
                @Override
                public void onSuccess(Boolean aBoolean) {
                    if (aBoolean) {
                        showToast("取消订阅成功");
                        btnSubscribe.setText("订阅");
                        isSubscribed = false;
                    } else {
                        showToast("取消订阅失败");
                    }
                }

                @Override
                public void onError(ApiException exception) {
                    showError("取消订阅失败", exception);
                }
            });
        }
    }

    /**
     * 下拉加载
     */
    protected abstract void refresh();

    /**
     * 加载更多
     */
    protected abstract void loadMore();

    /**
     * 点击条目播放
     */
    protected abstract void playItem(Item item);

    /**
     * 拖动到指定位置
     */
    protected abstract void seek(int progress);

    /**
     * 获取当前正在播放的位置
     */
    protected int getCurrentPosition() {
        IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
        if (playListControl == null) {
            PlayerLogUtil.log(getClass().getSimpleName(), "playPlayListPosition", "playPlayListPosition playListControl is null");
            return -1;
        }
//        PlayItem curPlayItem = playListControl.getCurPlayItem();
//        if (curPlayItem != null) {
//            long audioId = curPlayItem.getAudioId();
//            for (int i = 0, size = mAdapter.getItemCount(); i < size; i++) {
//                Item itemData = mAdapter.getItemData(i);
//                if (itemData.id == audioId) {
//                    return i;
//                }
//            }
//        }
        return playListControl.getCurPosition();
    }


    /**
     * 显示信息详情和封面
     */
    protected void showDetail(Object info, String cover) {
        if (tvDetails == null) {
            return;
        }
        String infoStr = new GsonBuilder()
                .setPrettyPrinting()
                .serializeNulls()
                .create().toJson(info);
        tvDetails.setText(infoStr);
        Glide.with(this).load(cover).into(ivCover);
    }

    //保存收听历史
    private void saveHistory(PlayItem playItem) {
        if (playItem != null) {
            PlayItem curPlayItem = PlayerManager.getInstance().getCurPlayItem();
            if (curPlayItem != null) {
                int type = curPlayItem.getType();
                //只保存专辑和PGC的收听历史
//                boolean isSaveType = PlayerConstants.RESOURCES_TYPE_ALBUM == type
//                        //一键播放电台类型属于PGC，播放类型是一键播放类型，不保存历史
//                        || (PlayerConstants.RESOURCES_TYPE_RADIO == type
//                        && PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE != curPlayItem.getType()
//                        && PlayerConstants.RESOURCES_TYPE_PURCHASE_ONE_KEY_LISTENER_TYPE != curPlayItem.getType());
//                if (isSaveType) {
                    long audioId = playItem.getAudioId();
                    int duration = playItem.getDuration();
                    int playedTime = playItem.getPosition();
                    long id = Long.valueOf(PlayerConstants.RESOURCES_TYPE_ALBUM == type ? playItem.getAlbumId() : curPlayItem.getRadioId());
                    long timestamp = DateUtil.getServerTime();
                    new HistoryRequest().saveListeningHistory(String.valueOf(type), id, audioId, playedTime, duration, timestamp,
                            new HttpCallback<Boolean>() {
                                @Override
                                public void onSuccess(Boolean aBoolean) {
                                    PlayerLogUtil.log("PlayerManager.saveHistory", "saveListeningHistory.onSuccess", "saveHistory status: " + aBoolean);
                                }

                                @Override
                                public void onError(ApiException e) {
                                    PlayerLogUtil.log("PlayerManager.saveHistory", "saveListeningHistory.onSuccess", "saveHistory failed: " + e);
                                    e.printStackTrace();
                                }
                            });
//                }
            }
        }
    }
}
