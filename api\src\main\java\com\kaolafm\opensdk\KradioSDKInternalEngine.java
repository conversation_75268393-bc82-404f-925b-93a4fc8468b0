package com.kaolafm.opensdk;

import android.app.Application;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.account.profile.KaolaProfile;
import com.kaolafm.opensdk.account.profile.KaolaProfileManager;
import com.kaolafm.opensdk.account.profile.QQMusicProfileManger;
import com.kaolafm.opensdk.account.token.RealAccessTokenManager;
import com.kaolafm.opensdk.api.BuildConfig;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.core.VerifyActivation;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.model.ReportParameter;

import javax.inject.Inject;

/**
 * Kradio SDK 内部入口。真正执行Kradio SDK 初始化/激活逻辑的地方。
 * <AUTHOR>
 * @date 2020-03-05
 */
public class KradioSDKInternalEngine<T extends Options> extends BaseEngine<T, KaolaProfileManager> {

    protected Application mApplication;

    @Inject
    @AppScope
    QQMusicProfileManger mMusicProfileManger;

    @Inject
    @AppScope
    RealAccessTokenManager mAccessTokenManager;

    @Inject
    @AppScope
    VerifyActivation mVerifyActivation;

    @Override
    protected void internalInit(Application application, T options, HttpCallback<Boolean> callback) {
        mApplication = application;
        loadProfile();
        loadAccessToken();
        initReport();
    }

    private void loadAccessToken() {
        mAccessTokenManager.loadCurrentAccessToken();
    }

    private void loadProfile() {
        mProfileManager.loadProfile();
        mMusicProfileManger.loadCurrentProfile();
    }

//    private void initKlSdkVehicle() {
//        ReportHelper.getInstance().initBySdk();
//    }

    /**
     * 传入经度
     * @param longitude
     */
    @Override
    public void setLongitude(String longitude) {
        super.setLongitude(longitude);
        ReportHelper.getInstance().setLon(longitude);
    }

    /**
     * 传入纬度
     * @param latitude
     */
    @Override
    public void setLatitude(String latitude) {
        super.setLatitude(latitude);
        ReportHelper.getInstance().setLat(latitude);
    }

    @Override
    public void setLocation(String lng, String lat) {
        super.setLocation(lng, lat);
        ReportHelper.getInstance().setLon(lng);
        ReportHelper.getInstance().setLat(lat);
    }

    private void initReport() {
        if (!isActivated()) {
            return;
        }
        /*
        这行貌似是区别新版sdk和1.5.x版本行为的，但是如果是initBySdk，会导致所有启动事件不上报
        老的代码没没动，新加了一个直接添加启动事件的方法ReportParameterManager.forceSetAppStartType
         */
        //ReportHelper.getInstance().initBySdk();  com.kaolafm.report.ReportHelper.isUseBySDK 默认为true。kradio需要设置为false。

        ReportParameter reportParameter = new ReportParameter();
        reportParameter.setDeviceId(mProfileManager.getProfile().getDeviceId());
        reportParameter.setChannel(mProfileManager.getProfile().getPackageName());
        reportParameter.setAppid(mProfileManager.getAppId());
        reportParameter.setLib_version(BuildConfig.VERSION_NAME);
        reportParameter.setOpenid(mAccessTokenManager.getKaolaAccessToken().getOpenId());
        reportParameter.setUid(mAccessTokenManager.getKaolaAccessToken().getUserId());
        ReportHelper reportHelper = ReportHelper.getInstance();
        reportHelper.init(mApplication, reportParameter);
        if (!StringUtil.isEmpty(mProfileManager.getProfile().getLat())) {
            reportHelper.setLat(mProfileManager.getProfile().getLat());
            reportHelper.setLon(mProfileManager.getProfile().getLng());
        }
    }

    @Override
    public boolean isActivated() {
        if(mVerifyActivation == null){
            return false;
        }
        return mVerifyActivation.isActivate();
    }

    @Override
    public void config(Application application, T options, HttpCallback<Boolean> callback) {
        init(application, options, null);
        activate(callback);
    }

    @Override
    protected void internalActivate(HttpCallback<Boolean> callback) {
        mVerifyActivation.activate(new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                initReport();
                if (callback != null) {
                    callback.onSuccess(aBoolean);
                }
            }

            @Override
            public void onError(ApiException exception) {
                if (callback != null) {
                    callback.onError(exception);
                }
            }
        });
    }

    @Override
    public void release() {
        ReportHelper.getInstance().release();
        mApplication = null;
    }

    public String getDeviceId() {
        return mProfileManager.getDeviceId();
    }



    public void setDeviceId(String deviceId) {
        mProfileManager.setDeviceId(deviceId);
    }
}
