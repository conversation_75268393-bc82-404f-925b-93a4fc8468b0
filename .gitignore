*.iml
.gradle
/local.properties
/.idea/workspace.xml
/.idea/libraries
.DS_Store
/build
/captures
.externalNativeBuild

# built application files
#*.apk
*.ap_

# files for the dex VM
*.dex

# Java class files
*.class

# generated files
bin/
gen/
gen-external-apklibs/
out/
target/
classes/
obj/
greendao/

# Local configuration file (sdk path, etc)
*local.properties

# Intellij project files
#启用iml 文件， 避免第一次导入项目不能自动依赖的问题
*.iml
*.ipr
*.iws
.idea/
build.println
build-println.xml

#others
*lint.xml
/library

maven-repository/
pom.xml
/BuildSrc/build/
/BuildSrc/.gradle/
/sdk/proguardMapping-SDK-dev.txt