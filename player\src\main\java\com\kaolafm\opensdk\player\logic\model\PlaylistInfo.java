package com.kaolafm.opensdk.player.logic.model;

/**
 * <AUTHOR> on 2019/3/18.
 */

public class PlaylistInfo extends CustomPlayerBuilder {

    /**
     * 专辑名称
     */
    private String mAlbumName;

    private String mAlbumPic;

    private String mBreakPointContinue;

    private String mPageIndex = "1";

    private int mAllSize = 0;

    private boolean hasNextPage = false;

    private boolean hasPrePage = false;
    /**
     * 下一页
     */
    private int mNextPage;

    /**
     * 上一页
     */
    private int mPrePage;

    /**
     * 专辑订阅数
     */
    private long followedNum;
    /**
     * 总期数
     */
    private long countNum;

    /**
     * 收听数
     */
    private long listenNum;

    private String broadcastChannel;

    /** 广播回放的状态 1开启节目，0关闭节目*/
    private int programEnable;

    private String sourceLogo;

    private String sourceName;

    private String mTempId;

    private String mTempChildId;

    private int radioType = -1;

    private int adZoneChooseType = 0;

    private int adZoneId = -1;
    /** 是否可以订阅 只有为1的时候不能订阅 */
    private int noSubscribe;

    public int getNoSubscribe() {
        return noSubscribe;
    }

    public void setNoSubscribe(int noSubscribe) {
        this.noSubscribe = noSubscribe;
    }

    public String getPageIndex() {
        return mPageIndex;
    }

    public void setPageIndex(String pageIndex) {
        this.mPageIndex = pageIndex;
    }

    public boolean isHasNextPage() {
        return hasNextPage;
    }

    public void setHasNextPage(boolean hasNextPage) {
        this.hasNextPage = hasNextPage;
    }

    public boolean isHasPrePage() {
        return hasPrePage;
    }

    public void setHasPrePage(boolean hasPrePage) {
        this.hasPrePage = hasPrePage;
    }

    public int getAllSize() {
        return mAllSize;
    }

    public void setAllSize(int allSize) {
        this.mAllSize = allSize;
    }

    public String getAlbumName() {
        return mAlbumName;
    }

    public void setAlbumName(String albumName) {
        this.mAlbumName = albumName;
    }

    public String getAlbumPic() {
        return mAlbumPic;
    }

    public void setAlbumPic(String albumPic) {
        this.mAlbumPic = albumPic;
    }

    public long getFollowedNum() {
        return followedNum;
    }

    public void setFollowedNum(long followedNum) {
        this.followedNum = followedNum;
    }

    public long getCountNum() {
        return countNum;
    }

    public void setCountNum(long countNum) {
        this.countNum = countNum;
    }

    public long getListenNum() {
        return listenNum;
    }

    public void setListenNum(long listenNum) {
        this.listenNum = listenNum;
    }

    public String getBroadcastChannel() {
        return broadcastChannel;
    }

    public void setBroadcastChannel(String broadcastChannel) {
        this.broadcastChannel = broadcastChannel;
    }

    public int getProgramEnable() {
        return programEnable;
    }

    public void setProgramEnable(int programEnable) {
        this.programEnable = programEnable;
    }

    public String getSourceLogo() {
        return sourceLogo;
    }

    public void setSourceLogo(String sourceLogo) {
        this.sourceLogo = sourceLogo;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public int getNextPage() {
        return mNextPage;
    }

    public void setNextPage(int nextPage) {
        if (nextPage > mNextPage) {
            this.mNextPage = nextPage;
        }
    }

    public void resetNextPage(int nextPage) {
        this.mNextPage = nextPage;
    }

    public int getPrePage() {
        return mPrePage;
    }

    public void setPrePage(int prePage) {
        if (mPrePage == 0 || prePage < mPrePage) {
            this.mPrePage = prePage;
        }
    }

    public void resetPrePage(int prePage) {
        this.mPrePage = prePage;
    }

    public String getTempId() {
        return mTempId;
    }

    public void setTempId(String tempId) {
        this.mTempId = tempId;
    }

    public String getTempChildId() {
        return mTempChildId;
    }

    public void setTempChildId(String tempChildId) {
        this.mTempChildId = tempChildId;
    }

    public int getRadioType() {
        return radioType;
    }

    public void setRadioType(int radioType) {
        this.radioType = radioType;
    }

    public int getAdZoneChooseType() {
        return adZoneChooseType;
    }

    public void setAdZoneChooseType(int adZoneChooseType) {
        this.adZoneChooseType = adZoneChooseType;
    }

    public int getAdZoneId() {
        return adZoneId;
    }

    public void setAdZoneId(int adZoneId) {
        this.adZoneId = adZoneId;
    }

    public String getBreakPointContinue() {
        return mBreakPointContinue;
    }

    public void setBreakPointContinue(String mBreakPointContinue) {
        this.mBreakPointContinue = mBreakPointContinue;
    }
}