<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="?android:attr/activatedBackgroundIndicator"
    android:padding="10dp">
    <TextView
        android:id="@+id/tv_player_item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="我好像在哪里见过你"
        android:maxLines="1"
        android:textSize="14sp"
        android:textColor="@color/colorBlack"
        />

    <TextView
        android:paddingRight="10dp"
        android:id="@+id/tv_player_item_pay_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="付费"
        app:layout_constraintTop_toBottomOf="@id/tv_player_item_title"
        android:textSize="12sp"
        android:maxLines="2"
        android:textColor="@color/color_black_50_transparent"
        android:layout_marginTop="5dp"
        />

    <TextView
        android:id="@+id/tv_player_item_des"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="给80后的歌"
        app:layout_constraintLeft_toRightOf="@id/tv_player_item_pay_status"
        app:layout_constraintTop_toBottomOf="@id/tv_player_item_title"
        android:textSize="12sp"
        android:maxLines="2"
        android:textColor="@color/color_black_50_transparent"
        android:layout_marginTop="5dp"
        />
    <ImageView
        android:id="@+id/iv_player_item_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:src="@drawable/ic_right_arrow"
        />

</android.support.constraint.ConstraintLayout>