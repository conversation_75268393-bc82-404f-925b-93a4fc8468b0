package com.kaolafm.opensdk.demo.qqmusic;

import android.content.Intent;
import android.os.Bundle;
import android.support.v7.widget.DividerItemDecoration;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;

import com.kaolafm.opensdk.api.music.qq.QQMusicRequest;
import com.kaolafm.opensdk.api.music.qq.model.Song;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.demo.detail.DetailActivity;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.List;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2018/10/26
 */

public class CollectionListActivity extends BaseActivity {

    public static final String SONGMENU_NAME = "songmenu_name";

    public static final String SONGMENU_ID = "songmenu_id";

    @BindView(R.id.rv_collection_list)
    RecyclerView mRvCollectionList;

    private CollectionListAdapter mListAdapter;

    private long mSongMenuId;

    private String mSongMenuName;

    @Override
    public int getLayoutId() {
        return R.layout.activity_collection_list;
    }

    @Override
    public void initArgs() {
        Intent intent = getIntent();
        if (intent != null) {
            mSongMenuName = intent.getStringExtra(SONGMENU_NAME);
            mSongMenuId = intent.getLongExtra(SONGMENU_ID, 0);
        }
        setTitle(mSongMenuName);
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        mRvCollectionList.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
        mRvCollectionList.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
        mListAdapter = new CollectionListAdapter();
        mListAdapter.setOnItemClickListener((view, viewType, song, position) -> {
            startActivity(new Intent(CollectionListActivity.this, DetailActivity.class));
//            MusicPlayerManager.getInstance().play(song);
        });
        mRvCollectionList.setAdapter(mListAdapter);
    }

    @Override
    public void initData() {
        new QQMusicRequest().getSongListOfSongMenu(mSongMenuId, new HttpCallback<List<Song>>() {
            @Override
            public void onSuccess(List<Song> songs) {
                mListAdapter.setDataList(songs);
            }

            @Override
            public void onError(ApiException exception) {

            }
        });
    }
}
