package com.kaolafm.opensdk.http.core;

import android.util.Log;

import com.kaolafm.base.utils.SeCoreUtils;
import com.kaolafm.base.utils.StringUtil;

import java.io.IOException;

import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;

public class HttpDeCryptUtil {
    private static String TAG = HttpDeCryptUtil.class.getSimpleName();
    private static String[] needDecryptPath = {
            "/v3/ford/internal/getVerifyCodeV2",
            "/v3/ford/internal/sms/loginV2",
            "/v3/ford/authuser/userinfoV2"
    };


//            if(oldPath.contains(needEncryptPath[1])){
//        newPath = "/v3/ford/internal/sms/loginV2";
//    } else if(oldPath.contains(needEncryptPath[0])){
//        newPath = "/v3/ford/internal/getVerifyCodeV2";
//    } else if(oldPath.contains(needEncryptPath[2])){
//        newPath = "/v3/ford/authuser/userinfoV2";

    private static boolean matchPath(Request req){
        if(req == null){
            Log.i(TAG, "matchPath req = null");
            return false;
        }

        if(req.url() == null){
            Log.i(TAG, "matchPath req.url = null");
            return false;
        }
        String path = req.url().encodedPath();

        if(StringUtil.isEmpty(path)){
            Log.i(TAG, "matchPath req = null or empty");
            return false;
        }

        //Log.i("EnDecryptUtil", "DE matchPath path = "+path);

        for (String s : needDecryptPath) {
            if (path.contains(s)) {
                Log.i(TAG, "matchPath match path = "+path);
                return true;
            }
        }
        return false;
    }

    static Response decryptResponseIfNeed(Response res){
        if(res == null){
            return null;
        }
        if(matchPath(res.request())){
//            Log.i("EnDecryptUtil", "DE raw req="+res.request().toString());
            try {
                return  res.newBuilder().body(newResponseBody(res)).build();
            } catch (Exception e) {
                Log.i(TAG, "decryptResponseIfNeed", e);
                e.printStackTrace();
            }
        }

        return res;
    }
    private static MediaType jsonType = MediaType.parse("application/json; charset=utf-8");
    private static ResponseBody newResponseBody(Response res) throws IOException {
        if(res == null){
            Log.i(TAG, "newResponseBody res = null");
        }
        if(res.body() == null){
            Log.i(TAG, "newResponseBody res.body = null");
            return null;
        }

        String body = res.body().string();
        if(StringUtil.isEmpty(body)){
            Log.i(TAG, "newResponseBody res.body = empty");
        }

        if(body.startsWith("\"")){
            body = body.substring(1);
        }
        if(body.endsWith("\"")){
            body = body.substring(0, body.length() -1);
        }

        Log.i(TAG, "newResponseBody body.len ="+body.length());
//        Log.i("EnDecryptUtil", "DE raw res="+body);
        String decryptedBody = SeCoreUtils.decrypt(body);
        Log.i(TAG, "newResponseBody decryptedBody.len ="+decryptedBody.length());
//        Log.i("EnDecryptUtil", "DE decrypted res="+decryptedBody);

        ResponseBody newBody = ResponseBody.create(jsonType, decryptedBody);
        return newBody;
    }
}
