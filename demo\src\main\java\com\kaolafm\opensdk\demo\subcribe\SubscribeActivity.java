package com.kaolafm.opensdk.demo.subcribe;

import android.content.Intent;
import android.os.Bundle;
import android.support.v7.app.AlertDialog;
import android.support.v7.app.AlertDialog.Builder;
import android.support.v7.widget.DividerItemDecoration;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.Button;
import android.widget.EditText;
import butterknife.BindView;
import butterknife.OnClick;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.subscribe.SubscribeInfo;
import com.kaolafm.opensdk.api.subscribe.SubscribeRequest;
import com.kaolafm.opensdk.api.subscribe.SubscribeStatus;
import com.kaolafm.opensdk.demo.BaseActivity;
import com.kaolafm.opensdk.demo.R;
import com.kaolafm.opensdk.demo.detail.DetailActivity;
import com.kaolafm.opensdk.demo.player.AlbumPlayerActivity;
import com.kaolafm.opensdk.demo.player.AudioPlayerActivity;
import com.kaolafm.opensdk.demo.player.BasePlayerActivity;
import com.kaolafm.opensdk.demo.player.BroadcastPlayerActivity;
import com.kaolafm.opensdk.demo.player.RadioPlayerActivity;
import com.kaolafm.opensdk.demo.player.SubscribePlayerActivity;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import java.util.ArrayList;
import java.util.List;

public class SubscribeActivity extends BaseActivity {

    @BindView(R.id.btn_get_subscribe_list)
    Button mBtnGetSubscribeList;

    @BindView(R.id.btn_is_subscribe)
    Button mBtnIsSubscribe;

    @BindView(R.id.btn_subscribe)
    Button mBtnSubscribe;

    @BindView(R.id.btn_subscribe_play)
    Button mBtnSubscribePlay;

    @BindView(R.id.btn_unsubscribe)
    Button mBtnUnsubscribe;

    @BindView(R.id.rv_subscribe_content_list)
    RecyclerView mRvSubscribeContentList;

    private SubscribeAdapter mSubscribeAdapter;

    private SubscribeRequest mSubscribeRequest;

    @Override
    public int getLayoutId() {
        return R.layout.activity_subscribe;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        setTitle("订阅");
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        mRvSubscribeContentList.setLayoutManager(linearLayoutManager);
        mRvSubscribeContentList.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
        mSubscribeAdapter = new SubscribeAdapter();
        mSubscribeAdapter.setOnItemClickListener((view, viewType, subscribeItem, position) -> {
            Intent intent = new Intent();
            intent.putExtra(BasePlayerActivity.KEY_ID, String.valueOf(subscribeItem.getId()));
            intent.putExtra(BasePlayerActivity.KEY_TYPE, String.valueOf(subscribeItem.getType()));
            Class clazz;
            switch (subscribeItem.getResType()) {
                case ResType.TYPE_ALBUM:
                    clazz = AlbumPlayerActivity.class;
                    break;
                case ResType.TYPE_AUDIO:
                    clazz = AudioPlayerActivity.class;
                    break;
                case ResType.TYPE_RADIO:
                    clazz = RadioPlayerActivity.class;
                    break;
                case ResType.TYPE_BROADCAST:
                    clazz = BroadcastPlayerActivity.class;
                    break;
                default:
                    clazz = DetailActivity.class;
            }
            intent.setClass(this, clazz);
            startActivity(intent);
        });
        mRvSubscribeContentList.setAdapter(mSubscribeAdapter);
    }

    @Override
    public void initData() {
        mSubscribeRequest = new SubscribeRequest().bindLifecycle(bindToLifecycle());
    }

    @OnClick({R.id.btn_get_subscribe_list, R.id.btn_subscribe, R.id.btn_unsubscribe, R.id.btn_is_subscribe,
            R.id.btn_subscribe_play})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.btn_get_subscribe_list:
                getSubscribeList();
                break;
            case R.id.btn_subscribe:
            case R.id.btn_unsubscribe:
            case R.id.btn_is_subscribe:
                operateSubscribe(view.getId());
                break;
            case R.id.btn_subscribe_play:
                oneButtonPlay();
                break;
            default:
        }
    }

    private void getSubscribeList() {
        mSubscribeRequest.getSubscribeList(1, 20, new HttpCallback<BasePageResult<List<SubscribeInfo>>>() {
            @Override
            public void onSuccess(BasePageResult<List<SubscribeInfo>> basePageResult) {
                if (basePageResult != null) {
                    showSubscribeList(basePageResult.getDataList());
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取订阅列表失败", exception);
            }
        });
    }

    private void showSubscribeList(List<SubscribeInfo> dataList) {
        if (dataList != null) {
            ArrayList<SubscribeInfo> itemList = new ArrayList<>();
            Disposable disposable = Observable.fromIterable(dataList)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(subscribeInfo -> {
                        itemList.add(subscribeInfo);
                    }, throwable -> Log.e("SubscribeActivity", "accept: "+throwable), () -> mSubscribeAdapter.setDataList(itemList));
        }
    }


    private void operateSubscribe(int viewId) {
        EditText editText = new EditText(this);
        editText.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
        editText.setHint("请输入id");
        AlertDialog alertDialog = new Builder(this)
                .setView(editText)
                .setPositiveButton("确定", (dialog, which) -> {
                    String idStr = editText.getText().toString().trim();
                    if (!TextUtils.isEmpty(idStr)) {
                        long id = Long.valueOf(idStr);
                        switch (viewId) {
                            case R.id.btn_subscribe:
                                subscribe(id);
                                break;
                            case R.id.btn_unsubscribe:
                                unsubscribe(id);
                                break;
                            case R.id.btn_is_subscribe:
                                isSubscribe(id);
                                break;
                                default:
                        }
                    }
                })
                .setNegativeButton("取消", (dialog, which) -> dialog.dismiss())
                .create();
        alertDialog.show();
    }

    private void isSubscribe(long id) {
        mSubscribeRequest.isSubscribed(id, new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean success) {
                showToast(success? "已经订阅":"未订阅");
            }

            @Override
            public void onError(ApiException exception) {
                showError("查询是否订阅错误", exception);
            }
        });
    }

    private void unsubscribe(long id) {
        mSubscribeRequest.unsubscribe(id, new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean success) {
                showToast(success?"取消订阅成功":"取消订阅失败");
            }

            @Override
            public void onError(ApiException exception) {
                showError("取消订阅错误", exception);
            }
        });
    }

    private void subscribe(long id) {
        mSubscribeRequest.subscribe(id, new HttpCallback<SubscribeStatus>() {
            @Override
            public void onSuccess(SubscribeStatus subscribeStatus) {
                int status = subscribeStatus.getStatus();
                switch (status) {
                    case SubscribeStatus.STATE_FAILURE:
                        showToast("订阅失败");
                        break;
                    case SubscribeStatus.STATE_SUCCESS:
                        showToast("订阅成功");
                        break;
                    default:
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("订阅错误", exception);
            }
        });
    }

    public void oneButtonPlay() {
        startActivity(new Intent(this, SubscribePlayerActivity.class));

    }

}
