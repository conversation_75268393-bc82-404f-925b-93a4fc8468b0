<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:kaola="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/live_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/main_bg">

    <android.support.constraint.Guideline
        android:id="@+id/live_image_top_guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.1178" />

    <android.support.constraint.Guideline
        android:id="@+id/live_image_second_guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.2557" />

    <ImageView
        android:id="@+id/live_image"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="center"
        android:src="@drawable/live_info_error_img"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.7"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/live_finish_image"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="fitXY"
        android:src="@drawable/live_finish_cover"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/live_image"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/live_top_gradient"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/live_image_top_gradient"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.25"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/live_image" />

    <ImageView
        android:id="@+id/live_bottom_gradient"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/live_image_bottom_gradient"
        app:layout_constraintBottom_toBottomOf="@id/live_image"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.25"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <ImageView
        android:id="@+id/live_minimum"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/color_main_button_click_selector"
        android:scaleType="center"
        android:src="@drawable/down_white_arrow_selector"
        app:layout_constraintBottom_toTopOf="@id/live_image_top_guideline"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.07"
        app:layout_constraintHorizontal_bias="0.0422"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/live_stop"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/color_main_button_click_selector"
        android:scaleType="center"
        android:src="@drawable/live_close_exit"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/live_image_top_guideline"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.07"
        app:layout_constraintHorizontal_bias="0.9406"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/live_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:textColor="#FFFFFFFF"
        android:textSize="19sp"
        app:layout_constraintBottom_toTopOf="@id/live_image_top_guideline"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/live_player"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="#FFFFFFFF"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@id/live_image_second_guideline"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/live_image"
        app:layout_constraintVertical_bias="0.3712" />

    <TextView
        android:id="@+id/live_listening_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:textColor="#66FFFFFF"
        android:textSize="13sp"
        app:layout_constraintBottom_toBottomOf="@id/live_image_second_guideline"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/live_image"
        app:layout_constraintVertical_bias="0.5508" />

    <FrameLayout
        android:id="@+id/live_screen_bullet_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/live_image"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/live_image" />

    <android.support.constraint.ConstraintLayout
        android:id="@+id/live_listen_button_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHorizontal_bias="0.8572"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/live_leave_a_message"
        app:layout_constraintTop_toBottomOf="@id/live_image"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.0816">

        <ImageView
            android:id="@+id/live_listen_message_image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/live_listen_message_selector"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kaolafm.opensdk.demo.live.ui.PlayingIndicator
            android:id="@+id/live_listen_anim_image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:bar_color="#F6F6F6"
            app:bar_num="5"
            app:duration="3000"
            app:is_two_way="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="W,1:1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintHeight_default="percent"
            app:layout_constraintHeight_percent="0.7547"
            app:min_height="5dp"
            app:step_num="10" />

        <TextView
            android:id="@+id/live_listen_message_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/live_listen_message"
            android:textColor="#FFFFFFFF"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </android.support.constraint.ConstraintLayout>

    <android.support.constraint.ConstraintLayout
        android:id="@+id/live_cancel_button_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintHorizontal_bias="0.1428"
        app:layout_constraintLeft_toRightOf="@id/live_leave_a_message"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_image"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.0816">

        <ImageView
            android:id="@+id/live_cancel_message_image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/live_cancel_message_selector"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/live_cancel_message_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/live_cancel_message"
            android:textColor="#FFFFFFFF"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </android.support.constraint.ConstraintLayout>

    <ImageView
        android:id="@+id/live_record_anim_image"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/live_record_sound_update"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_image"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.12" />


    <com.kaolafm.opensdk.demo.live.ui.CircleProgressImageView
        android:id="@+id/live_leave_a_message"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/live_leave_a_message"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_image"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.1"
        kaola:back_color="#FF545A6A"
        kaola:progress_color="#FF06B5D2"
        kaola:progress_width="4dp" />

    <TextView
        android:id="@+id/live_login_prompt_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_leave_a_message"
        app:layout_constraintVertical_bias="0.1538" />


    <TextView
        android:id="@+id/live_countdown_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFFFF"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@id/live_leave_a_message"
        app:layout_constraintLeft_toLeftOf="@id/live_leave_a_message"
        app:layout_constraintRight_toRightOf="@id/live_leave_a_message"
        app:layout_constraintTop_toTopOf="@id/live_leave_a_message" />

    <ImageView
        android:id="@+id/live_speak_image"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/live_leave_message_speak"
        android:textColor="#FFFFFFFF"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/live_leave_a_message"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintLeft_toLeftOf="@id/live_leave_a_message"
        app:layout_constraintRight_toRightOf="@id/live_leave_a_message"
        app:layout_constraintTop_toTopOf="@id/live_leave_a_message"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.0602" />

    <android.support.constraint.ConstraintLayout
        android:id="@+id/live_not_start_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_image">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/live_start_interval"
            android:textColor="#FFFFFF"
            android:textSize="19sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2368" />

        <TextView
            android:id="@+id/live_not_start_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFFFFFFF"
            android:textSize="32sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.4736" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/live_go_listen_other"
            android:textColor="#66FFFFFF"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.7" />

    </android.support.constraint.ConstraintLayout>

    <android.support.constraint.ConstraintLayout
        android:id="@+id/live_finish_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_image">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/live_finished"
            android:textColor="#FFFFFFFF"
            android:textSize="24sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.3105" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/live_go_listen_other"
            android:textColor="#66FFFFFF"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.5368" />
    </android.support.constraint.ConstraintLayout>

    <android.support.constraint.ConstraintLayout
        android:id="@+id/live_error_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_image">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/live_load_error"
            android:textColor="#FFFFFFFF"
            android:textSize="19sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2789" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:text="@string/live_go_listen_other"
            android:textColor="#66FFFFFF"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.4842" />
    </android.support.constraint.ConstraintLayout>

    <android.support.constraint.ConstraintLayout
        android:id="@+id/live_coming_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/live_image">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/live_coming"
            android:textColor="#FFFFFF"
            android:textSize="19sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2789" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/live_go_listen_other"
            android:textColor="#66FFFFFF"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.4842" />

    </android.support.constraint.ConstraintLayout>

</android.support.constraint.ConstraintLayout>

