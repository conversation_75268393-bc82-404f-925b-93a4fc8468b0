package com.kaolafm.opensdk.player.logic.model.item;

import android.os.Parcel;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.player.logic.model.item.model.AlbumInfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.InfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.OfflineInfoData;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayUrlData;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * 专辑- 播放对象
 */
public class AlbumPlayItem extends PlayItem {

    /**
     * 播放url相关数据
     */
    private PlayUrlData mPlayUrlData;

    /**
     * 离线相关数据
     */
    private OfflineInfoData mOfflineInfoData;

    /**
     * 信息相关数据
     */
    private InfoData mInfoData;

    /**
     * 所属专辑 信息
     */
    private AlbumInfoData mAlbumInfoData;


    public AlbumPlayItem() {
        mInfoData = new InfoData();
        mOfflineInfoData = new OfflineInfoData();
        mPlayUrlData = new PlayUrlData();
        mAlbumInfoData = new AlbumInfoData();
    }

    @Override
    public String getRadioId() {
        return String.valueOf(mInfoData.getAlbumId());
    }

    @Override
    public String getAlbumId() {
        return String.valueOf(mInfoData.getAlbumId());
    }

    @Override
    public String getTitle() {
        return mInfoData.getTitle();
    }

    @Override
    public String getPicUrl() {
        String picUrl = mInfoData.getAudioPic();
        if (StringUtil.isEmpty(picUrl)) {
            picUrl = mInfoData.getAlbumPic();
        }
        return picUrl;
    }

    @Override
    public String getHost() {
        return mInfoData.getHosts();
    }

    @Override
    public String getAlbumTitle() {
        return mInfoData.getAlbumName();
    }

    @Override
    public int getType() {
        return PlayerConstants.RESOURCES_TYPE_ALBUM;
    }

    @Override
    public String getUpdateTime() {
        return mInfoData.getUpdateTime();
    }

    public PlayUrlData getPlayUrlData() {
        return mPlayUrlData;
    }

    public void setPlayUrlData(PlayUrlData playUrlData) {
        this.mPlayUrlData = playUrlData;
    }

    public InfoData getInfoData() {
        return mInfoData;
    }

    public void setInfodata(InfoData infoData) {
        this.mInfoData = infoData;
    }

    @Override
    public String getSourceName() {
        return mInfoData.getSourceName();
    }
    @Override
    public String getSourceLogo() {
        return mInfoData.getSourceLogo();
    }

    public OfflineInfoData getOfflineInfoData() {
        return mOfflineInfoData;
    }

    public void setOfflineInfoData(OfflineInfoData offlineInfoData) {
        this.mOfflineInfoData = offlineInfoData;
    }

    public AlbumInfoData getAlbumInfoData() {
        return mAlbumInfoData;
    }

    public void setAlbumInfoData(AlbumInfoData albumInfoData) {
        this.mAlbumInfoData = albumInfoData;
    }

    private AlbumPlayItem(Parcel parcel) {

    }

    public static final Creator<AlbumPlayItem> CREATOR = new Creator<AlbumPlayItem>() {

        @Override
        public AlbumPlayItem createFromParcel(Parcel source) {
            return new AlbumPlayItem(source);
        }

        @Override
        public AlbumPlayItem[] newArray(int size) {
            return new AlbumPlayItem[size];
        }
    };
}
