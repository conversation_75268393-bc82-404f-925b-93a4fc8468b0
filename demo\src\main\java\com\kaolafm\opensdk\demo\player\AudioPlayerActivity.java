package com.kaolafm.opensdk.demo.player;

import android.os.Bundle;
import android.util.Log;
import android.view.View;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.media.AlbumRequest;
import com.kaolafm.opensdk.api.media.AudioRequest;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.subscribe.SubscribeRequest;
import com.kaolafm.opensdk.api.subscribe.SubscribeStatus;
import com.kaolafm.opensdk.demo.detail.DetailActivity;
import com.kaolafm.opensdk.demo.detail.StringAdapter.Item;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Yan
 * @date 2018/12/10
 */

public class AudioPlayerActivity extends BasePlayerActivity {
    private boolean isHavePre = true;
    private boolean isHaveNext = true;
    private int mNextPage = -1;
    private int mPrePage = -1;

    private long mAlbumId;
    private long mCurAudioId;
    private boolean isLoadMore = true;


    @Override
    public void initView(Bundle savedInstanceState) {
        super.initView(savedInstanceState);
        setTitle("单曲播放器页面");
        mTrfDetailPlaylist.setEnableRefresh(true);
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().addPlayListControlStateCallback(mPlayListStateListener);
    }

    @Override
    public void initData() {
        //播放单曲会自动播放单曲所在专辑
        mCurAudioId = mId;
        if(PlayerManager.getInstance().getCurPlayItem().getType() == PlayerConstants.RESOURCES_TYPE_ALBUM){
            showListInfo(false, PlayerManager.getInstance().getPlayList());
            select();
        }
        PlayerManager.getInstance().start(new PlayerBuilder().setId(String.valueOf(mId)).setType(PlayerConstants.RESOURCES_TYPE_AUDIO));
//        PlayerManager.getInstance().getPlayItemFromAudioId(mId, new PlayerManager.GetPlayItemListener() {
//            @Override
//            public void success(PlayItem playitem) {
//                mAlbumId = Long.parseLong(playitem.getAlbumId());
//                getCurPlayList(mAlbumId);
//            }
//
//            @Override
//            public void error(ApiException exception) {
//                showError("获取播单列表错误", exception);
//            }
//        });
        if(mIds.length<=1){
            getAudioDetail(mId);
        }else {
            getAudioDetails(mIds);
        }

        getSubscribeState();
    }

    @Override
    protected void subscribe(boolean isSubscribe) {
        //订阅
        if (isSubscribe) {
            new SubscribeRequest().subscribe(mCurAudioId, new HttpCallback<SubscribeStatus>() {
                @Override
                public void onSuccess(SubscribeStatus subscribeStatus) {
                    if (subscribeStatus.getStatus() == SubscribeStatus.STATE_SUCCESS) {
                        btnSubscribe.setText("取消订阅");
                        isSubscribed = true;
                        showToast("订阅成功");
                    }
                }

                @Override
                public void onError(ApiException exception) {
                    showError("订阅失败", exception);
                }
            });
        }
        //取消订阅
        else {
            new SubscribeRequest().unsubscribe(mCurAudioId, new HttpCallback<Boolean>() {
                @Override
                public void onSuccess(Boolean aBoolean) {
                    if (aBoolean) {
                        showToast("取消订阅成功");
                        btnSubscribe.setText("订阅");
                        isSubscribed = false;
                    } else {
                        showToast("取消订阅失败");
                    }
                }

                @Override
                public void onError(ApiException exception) {
                    showError("取消订阅失败", exception);
                }
            });
        }
    }

    /**
     * 获取订阅状态
     */
    private void getSubscribeState() {
        new SubscribeRequest().isSubscribed(mCurAudioId, new HttpCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                isSubscribed = aBoolean;
                btnSubscribe.setText(aBoolean ? "取消订阅" : "订阅");
                btnSubscribe.setEnabled(true);
                btnSubscribe.setVisibility(View.VISIBLE);
            }

            @Override
            public void onError(ApiException exception) {
                btnSubscribe.setText(exception.getMessage());
            }
        });
    }

    private void getAudioDetail(long audioId) {
        new AudioRequest().getAudioDetails(audioId, new HttpCallback<AudioDetails>() {
            @Override
            public void onSuccess(AudioDetails audioDetails) {
                if(audioDetails == null){
                    return;
                }
                showDetail(audioDetails, audioDetails.getAudioPic());
                mAlbumId = audioDetails.getAlbumId();
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取单个单曲详情错误", exception);
            }
        });
    }

    private void getAudioDetails(Long[] audioId) {
        new AudioRequest().getAudioDetails(audioId, new HttpCallback<List<AudioDetails>>() {
            @Override
            public void onSuccess(List<AudioDetails> audioDetails) {
                showDetail(audioDetails, "");
                if(audioDetails!= null && audioDetails.size()>0){
                    mAlbumId = audioDetails.get(0).getAlbumId();
                }
            }

            @Override
            public void onError(ApiException exception) {
                showError("获取多个单曲详情错误", exception);
            }
        });
    }

    @Override
    protected void playPre() {
        isLoadMore = false;
        PlayerManager.getInstance().playPre();
    }

    @Override
    protected void switchPlayPause() {
        PlayerManager.getInstance().switchPlayerStatus();
    }

    @Override
    protected void playNext() {
        isLoadMore = true;
        PlayerManager.getInstance().playNext();
    }

    @Override
    protected void refresh() {
//        getAlbumPlaylist(false);
        isLoadMore = false;
        PlayerManager.getInstance().loadPrePage(new IPlayListGetListener() {
            @Override
            public void onDataGet(PlayItem playItem, List<PlayItem> playItemList) {
                if(mTrfDetailPlaylist != null){
                    mTrfDetailPlaylist.finishRefreshing();
                }
//                showListInfo(false, playItemList);
            }

            @Override
            public void onDataGetError(int errorCode) {
                if(mTrfDetailPlaylist != null){
                    mTrfDetailPlaylist.finishRefreshing();
                }
            }
        });
    }

    @Override
    protected void loadMore() {
//        getAlbumPlaylist(true);
        isLoadMore = true;
        PlayerManager.getInstance().loadNextPage(new IPlayListGetListener() {
            @Override
            public void onDataGet(PlayItem playItem, List<PlayItem> playItemList) {
                if(mTrfDetailPlaylist != null){
                    mTrfDetailPlaylist.finishLoadmore();
                }
//                showListInfo(true, playItemList);
            }

            @Override
            public void onDataGetError(int errorCode) {
                if(mTrfDetailPlaylist != null){
                    mTrfDetailPlaylist.finishLoadmore();
                }
            }
        });
    }

    @Override
    protected void playItem(Item item) {
        PlayerManager.getInstance().startPlayItemInList(item.playItem);
    }

    @Override
    protected void seek(int progress) {
        PlayerManager.getInstance().seek(progress);
    }

    /**
     * 获取专辑播单
     * 如果PlayItem里面的数据已经满足页面暂时需求，
     * 能保证一致性，就不需要再手动将数据添加到播单中了。
     */
    private void getAlbumPlaylist(boolean isLoadMore) {
        if (isLoadMore && !isHaveNext) {
            //如果加载更多没有下一页了就不在请求
            if (mTrfDetailPlaylist != null) {
                mTrfDetailPlaylist.finishLoadmore();
            }
            showToast("没有更多");
            return;
        }
        if(!isLoadMore && !isHavePre){
            if (mTrfDetailPlaylist != null) {
                mTrfDetailPlaylist.finishRefreshing();
            }
            return;
        }
        //直接请求接口获取播单
        new AlbumRequest().getPlaylist(mAlbumId, AlbumRequest.SORT_ACS, 10, isLoadMore?mNextPage:mPrePage,
                new HttpCallback<BasePageResult<List<AudioDetails>>>() {

                    @Override
                    public void onSuccess(BasePageResult<List<AudioDetails>> result) {
                        if (result != null) {
                            if (result.getHavePre() == 0) {
                                isHavePre = false;
                            }
                            if (result.getHaveNext() == 0) {
                                isHaveNext = false;
                            }
                            mPrePage = result.getPrePage() < mPrePage ? result.getPrePage() : mPrePage;
                            mNextPage = result.getNextPage() > mNextPage ? result.getNextPage() : mNextPage;
                            List<PlayItem> playItemList = PlayListUtils.audioDetailToAlbumPlayItem(result.getDataList(), PlayerManager.getInstance().getPlayListInfo());
                            showListInfo(isLoadMore, playItemList);
                        }
                    }

                    @Override
                    public void onError(ApiException exception) {
                        showError("获取专辑播单错误", exception);
                        if (mTrfDetailPlaylist != null) {
                            mTrfDetailPlaylist.finishLoadmore();
                        }
                    }
                });
    }

    private void getCurPlayList(long albumId){
        //直接请求接口获取播单
        new AlbumRequest().getPlaylist(albumId, mId, AlbumRequest.SORT_ACS, 10, mNextPage,
                new HttpCallback<BasePageResult<List<AudioDetails>>>() {

                    @Override
                    public void onSuccess(BasePageResult<List<AudioDetails>> result) {
                        if (result != null) {
                            if (result.getHavePre() == 0) {
                                isHavePre = false;
                            }
                            if (result.getHaveNext() == 0) {
                                isHaveNext = false;
                            }
                            mPrePage = result.getPrePage() < mPrePage ? result.getPrePage() : mPrePage;
                            mNextPage = result.getNextPage() > mNextPage ? result.getNextPage() : mNextPage;
                            List<PlayItem> playItemList = PlayListUtils.audioDetailToAlbumPlayItem(result.getDataList(), PlayerManager.getInstance().getPlayListInfo());
                            showListInfo(isLoadMore, playItemList);
                        }
                    }

                    @Override
                    public void onError(ApiException exception) {
                        showError("获取专辑播单错误", exception);
                        if (mTrfDetailPlaylist != null) {
                            mTrfDetailPlaylist.finishLoadmore();
                        }
                    }
                });
    }

    private void showListInfo(boolean isLoadMore, List<PlayItem> playItemList){
        if (!ListUtil.isEmpty(playItemList)) {
            List<Item> datas = new ArrayList<>();
            for (int i = 0, size = playItemList.size(); i < size; i++) {
                PlayItem item = playItemList.get(i);
                Item sai = new Item();
                sai.id = item.getAudioId();
                sai.type = DetailActivity.TYPE_ALBUM;
                sai.title = item.getTitle();
                sai.details = item.getAlbumTitle();
                sai.audition = item.getAudition();
                sai.fine = item.getFine();
                sai.buyStatus = item.getBuyStatus();
                sai.item = item;
                sai.playItem = item;
                datas.add(sai);
            }
            if (isLoadMore) {
                mAdapter.addDataList(datas);
            }else {
                List<Item> tempDatas = new ArrayList<>();
                tempDatas.addAll(datas);
                tempDatas.addAll(mAdapter.getDataList());
                mAdapter.clear();
                mAdapter.addDataList(tempDatas);
            }
        }else{
            showToast(isLoadMore? "没有更多":"播单列表为空");
        }
        if (mTrfDetailPlaylist != null) {
            if(isLoadMore){
                mTrfDetailPlaylist.finishLoadmore();
            }else{
                mTrfDetailPlaylist.finishRefreshing();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayerStateListener);
        PlayerManager.getInstance().removePlayListControlStateCallback(mPlayListStateListener);
    }

    private IPlayListStateListener mPlayListStateListener = new IPlayListStateListener() {
        @Override
        public void onPlayListChange(List<PlayItem> list) {
            showToast("单曲所在专辑播单发生变化");
            showListInfo(isLoadMore, list);
        }

        @Override
        public void onPlayListChangeError(int i) {

        }
    };
}
