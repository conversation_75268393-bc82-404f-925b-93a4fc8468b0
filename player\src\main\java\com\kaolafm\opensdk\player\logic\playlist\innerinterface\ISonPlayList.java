package com.kaolafm.opensdk.player.logic.playlist.innerinterface;

import java.util.ArrayList;

public interface ISonPlayList<T> {

    /**
     * 获取子播单全部数据
     * @return
     */
    ArrayList<T> getSongPlayList();

    /**
     * 添加条目
     * @param t
     */
    void addSongPlayItem(T t);

    /**
     * 删除条目
     * @param t
     */
    void removeSongPlayItem(T t);

    /**
     * 当前是否正在播放子播单内容
     * @return
     */
    boolean isPlayingSonList();


}
