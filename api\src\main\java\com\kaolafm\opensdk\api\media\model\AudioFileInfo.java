package com.kaolafm.opensdk.api.media.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

public class AudioFileInfo implements Parcelable {

    /** 文件格式 */
    @SerializedName("fileType")
    private String fileType;

    /** 码率 */
    @SerializedName("bitrate")
    private Integer bitrate;

    /** 播放地址 */
    @SerializedName("playUrl")
    private String playUrl;

    /** 文件大小 */
    @SerializedName("fileSize")
    private Integer fileSize;

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public Integer getBitrate() {
        return bitrate;
    }

    public void setBitrate(Integer bitrate) {
        this.bitrate = bitrate;
    }

    public String getPlayUrl() {
        return playUrl;
    }

    public void setPlayUrl(String playUrl) {
        this.playUrl = playUrl;
    }

    public Integer getFileSize() {
        return fileSize;
    }

    public void setFileSize(Integer fileSize) {
        this.fileSize = fileSize;
    }

    @Override
    public String toString() {
        return "AudioFileInfo{" +
                "fileType='" + fileType + '\'' +
                ", bitrate=" + bitrate +
                ", playUrl='" + playUrl + '\'' +
                ", fileSize=" + fileSize +
                '}';
    }

    protected AudioFileInfo(Parcel in) {
        this.fileType = in.readString();
        this.bitrate = in.readInt();
        this.playUrl = in.readString();
        this.fileSize = in.readInt();
    }

    public static final Creator<AudioFileInfo> CREATOR = new Creator<AudioFileInfo>() {
        @Override
        public AudioFileInfo createFromParcel(Parcel in) {
            return new AudioFileInfo(in);
        }

        @Override
        public AudioFileInfo[] newArray(int size) {
            return new AudioFileInfo[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.fileType);
        dest.writeInt(this.bitrate);
        dest.writeString(this.playUrl);
        dest.writeInt(this.fileSize);
    }
}
