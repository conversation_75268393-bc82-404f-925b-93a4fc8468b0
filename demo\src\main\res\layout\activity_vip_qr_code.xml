<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_kaola_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_vip_meal_id"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        android:text="请输入Vip套餐的id"/>

    <EditText
        android:id="@+id/et_vip_meal_id"
        android:layout_width="200dp"
        android:layout_height="50dp"
        app:layout_constraintTop_toBottomOf="@id/tv_vip_meal_id"
        android:text="31"/>

    <TextView
        android:id="@+id/tv_vip_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/et_vip_meal_id"
        android:text="请输入Vip套餐的价格"/>

    <EditText
        android:id="@+id/et_vip_price"
        android:layout_width="200dp"
        android:layout_height="50dp"
        app:layout_constraintTop_toBottomOf="@id/tv_vip_price"
        android:text="900"/>

    <Button
        android:id="@+id/btn_vip_qr_code_get"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="获取VIP支付二维码"
        app:layout_constraintTop_toBottomOf="@id/et_vip_price" />

    <TextView
        android:id="@+id/info_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/btn_vip_qr_code_get" />

    <ImageView
        android:id="@+id/iv_vip_qr_code"
        android:layout_width="170dp"
        android:layout_height="170dp"
        android:contentDescription="@null"
        app:layout_constraintTop_toBottomOf="@id/info_view" />

    <Button
        android:id="@+id/btn_qr_code_check"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="查询支付结果"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/iv_vip_qr_code" />

</android.support.constraint.ConstraintLayout>
