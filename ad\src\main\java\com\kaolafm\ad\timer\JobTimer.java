package com.kaolafm.ad.timer;

import android.app.job.JobInfo;
import android.app.job.JobScheduler;
import android.content.ComponentName;
import android.content.Context;
import android.os.Build;
import android.os.PersistableBundle;
import android.support.annotation.RequiresApi;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.opensdk.log.Logging;

/**
 * 通过JobService实现的定时，这个偶尔会有延迟，没有找到问题。
 *
 * <AUTHOR>
 * @date 2020-02-06
 */
@RequiresApi(Build.VERSION_CODES.LOLLIPOP)
public class JobTimer extends AbstractTimer {

    private Context mContext;
    private JobScheduler mScheduler;
    private ComponentName mComponentName;

    JobTimer(Context context) {
        this.mContext = context;
    }

    @Override
    public void start() {
        mScheduler = (JobScheduler) mContext.getSystemService(Context.JOB_SCHEDULER_SERVICE);
        mComponentName = new ComponentName(mContext.getPackageName(), TimerJobService.class.getName());
    }

    @Override
    public void addTask(AdvertTask task) {
        int id = task.getId();
        //时间戳作为唯一的jobId
        int timestamp = (int) (task.getTimestamp() / 1000);
        Logging.d("JobTimer", "addTask: id=" + id + ", Timestamp=" + task.getTimestamp());
        JobInfo.Builder builder = new JobInfo.Builder(timestamp, mComponentName);
        //执行的时间间隔。
        builder.setMinimumLatency(task.getTimestamp() - DateUtil.getServerTime());
        builder.setRequiresDeviceIdle(false);
        builder.setRequiresCharging(false);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            builder.setRequiresBatteryNotLow(false);
            builder.setRequiresStorageNotLow(true);
        }
        PersistableBundle bundle = new PersistableBundle();
        bundle.putInt(ID, id);
        bundle.putInt(TIME, timestamp);
        builder.setExtras(bundle);
        JobInfo jobInfo = builder.build();
        int jobId = mScheduler.schedule(jobInfo);
        if (jobId <= 0) {
            mScheduler.cancel(jobId);
        }
    }

    @Override
    public void removeTask(AdvertTask task) {
        mScheduler.cancel((int) (task.getTimestamp() / 1000));
    }


    @Override
    public void stop() {
        mScheduler.cancelAll();
    }
}
