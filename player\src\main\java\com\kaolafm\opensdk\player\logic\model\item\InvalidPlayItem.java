package com.kaolafm.opensdk.player.logic.model.item;

import android.os.Parcel;

import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;

/**
 * 无效的PlayItem
 */
public class InvalidPlayItem extends PlayItem {

    public InvalidPlayItem() {

    }

    @Override
    public String getRadioId() {
        return "0";
    }

    @Override
    public String getAlbumId() {
        return "0";
    }

    @Override
    public String getTitle() {
        return "";
    }

    @Override
    public String getPicUrl() {
        return "";
    }

    @Override
    public String getHost() {
        return "";
    }

    @Override
    public String getAlbumTitle() {
        return "";
    }

    @Override
    public int getType() {
        return PlayerConstants.RESOURCES_TYPE_INVALID;
    }

    private InvalidPlayItem(Parcel parcel) {

    }

    public static final Creator<InvalidPlayItem> CREATOR = new Creator<InvalidPlayItem>() {

        @Override
        public InvalidPlayItem createFromParcel(Parcel source) {
            return new InvalidPlayItem(source);
        }

        @Override
        public InvalidPlayItem[] newArray(int size) {
            return new InvalidPlayItem[size];
        }
    };
}
