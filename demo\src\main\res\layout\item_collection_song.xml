<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="5dp">
    <ImageView
        android:id="@+id/iv_collection_song_item_img"
        android:layout_width="45dp"
        android:layout_height="45dp" />

    <TextView
        android:id="@+id/tv_collection_song_item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@id/iv_collection_song_item_img"
        android:paddingStart="10dp"
        />
    <TextView
        android:id="@+id/tv_collection_song_item_des"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@id/iv_collection_song_item_img"
        app:layout_constraintTop_toBottomOf="@id/tv_collection_song_item_title"
        android:layout_marginTop="10dp"
        android:paddingStart="10dp"
        />

</android.support.constraint.ConstraintLayout>