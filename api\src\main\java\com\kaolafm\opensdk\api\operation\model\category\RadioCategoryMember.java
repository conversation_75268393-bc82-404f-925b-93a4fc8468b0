package com.kaolafm.opensdk.api.operation.model.category;

/**
 * 人工运营分类成员:AI电台.
 */
public class RadioCategoryMember extends CategoryMember {

    /** AI电台id*/
    private long radioId;

    /** AI电台收听数*/
    private int playTimes;

    public long getRadioId() {
        return radioId;
    }

    public void setRadioId(long radioId) {
        this.radioId = radioId;
    }

    public int getPlayTimes() {
        return playTimes;
    }

    public void setPlayTimes(int playTimes) {
        this.playTimes = playTimes;
    }

    @Override
    public String toString() {
        return "RadioCategoryMember{" +
                "radioId=" + radioId +
                ", playTimes=" + playTimes +
                '}';
    }
}
