package com.kaolafm.opensdk.api.activity.model;


/**
 * 活动信息
 */

public class Activity {

    /** 活动id */
    private Integer id;

    /** 活动名称 */
    private String name;

    /** 活动说明 */
    private String description;

    /** 活动状态 0-已结束，1-进行中 */
    private Integer status;

    /** 二维码地址 */
    private String qrCodeUrl;

    /** 二维码下方描述 */
    private String codeDes;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getQrCodeUrl() {
        return qrCodeUrl;
    }

    public void setQrCodeUrl(String qrCodeUrl) {
        this.qrCodeUrl = qrCodeUrl;
    }

    public String getCodeDes() {
        return codeDes;
    }

    public void setCodeDes(String codeDes) {
        this.codeDes = codeDes;
    }

    @Override
    public String toString() {
        return "Activity{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", status=" + status +
                ", qrCodeUrl='" + qrCodeUrl + '\'' +
                ", codeDes='" + codeDes + '\'' +
                '}';
    }
}
