<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_detail_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="详情"
        android:textColor="@color/Blue" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/Green"
        app:layout_constraintTop_toBottomOf="@id/tv_detail_title" />

    <TextView
        android:id="@+id/tv_details_content"
        android:layout_width="match_parent"
        android:layout_height="150dp"
        android:scrollbars="vertical"
        android:textColor="@color/colorBlack"
        android:textSize="10sp"
        app:layout_constraintTop_toBottomOf="@id/tv_detail_title"
        tools:text="详情:详情:详情:详情:详情:详情:详情:详情:详情:详情:详情:详情:详情:详情:详情:详情:详情:" />

    <ImageView
        android:id="@+id/iv_detail_cover"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:contentDescription="@null"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_detail_title" />

    <Button
        android:id="@+id/btn_detail_subscribe"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:text="订阅"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_detail_cover"
        android:enabled="false"
        />

    <ImageView
        android:id="@+id/iv_detail_play_pre"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:src="@drawable/ic_play_pre"
        app:layout_constraintTop_toBottomOf="@id/tv_details_content" />

    <ImageView
        android:id="@+id/iv_detail_play_pause"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="10dp"
        android:src="@drawable/selector_play_pause"
        app:layout_constraintStart_toEndOf="@id/iv_detail_play_pre"
        app:layout_constraintTop_toTopOf="@id/iv_detail_play_pre"
        />

    <ImageView
        android:id="@+id/iv_detail_play_next"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="10dp"
        android:src="@drawable/ic_play_next"
        app:layout_constraintStart_toEndOf="@id/iv_detail_play_pause"
        app:layout_constraintTop_toTopOf="@id/iv_detail_play_pre" />
    <Switch
        android:id="@+id/switch_audio_focus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="音频焦点"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_detail_play_pre"
        android:checked="true"
        />
    <Spinner
        android:id="@+id/spinner_player_change_quality"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        app:layout_constraintRight_toLeftOf="@id/switch_audio_focus"
        app:layout_constraintTop_toTopOf="@id/iv_detail_play_pre"
        android:entries="@array/sound_quality"
        />
    <SeekBar
        android:id="@+id/sb_detail_progress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/iv_detail_play_next"
        tools:max="100"
        tools:progress="10"
        tools:secondaryProgress="50"
        android:maxHeight="7dp"
        android:minHeight="7dp"
        android:progressDrawable="@drawable/seekbar_progress"
        android:thumbOffset="2dp"
        />

    <TextView
        android:id="@+id/tv_detail_playlist_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="播单"
        android:textColor="@color/Blue"
        app:layout_constraintTop_toBottomOf="@id/sb_detail_progress" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/Green"
        app:layout_constraintTop_toBottomOf="@id/tv_detail_playlist_title" />

    <com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout
        android:id="@+id/trf_detail_playlist"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBaseline_toBaselineOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_detail_playlist_title">
        <android.support.v7.widget.RecyclerView
            android:id="@+id/rv_detail_playlist"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout>

</android.support.constraint.ConstraintLayout>
