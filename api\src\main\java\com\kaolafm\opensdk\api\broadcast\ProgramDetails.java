package com.kaolafm.opensdk.api.broadcast;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.gson.annotations.SerializedName;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: ProgramDetails.java                                               
 *                                                                  *
 * Created in 2018/8/13 下午5:08                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class ProgramDetails implements Parcelable {


    /**
     * programId : 19196027
     * broadcastId : 1600000000407
     * nextProgramId : -1
     * preProgramId : -1
     * title : 音乐永远在
     * backLiveUrl : http://pb.x.l.kaolafm.net/play.m3u8?broadcastId=1600000000407&stime=20180813000000&etime=20180813005959
     * playUrl : http://play.x.l.kaolafm.net/live/1600000000407/index.m3u8
     * comperes :
     * beginTime : 00:00:00
     * endTime : 00:59:59
     * startTime : 1534089600000
     * finishTime : 1534093199000
     * status : 2
     * isSubscribe : 0
     * desc :
     * broadcastDesc : 重庆音乐广播是重庆唯一一家专业的音乐频率，调频FM88.1,全天24小时不间断播出.重庆音乐广播以传播先进、时尚、经典音乐文化为己任，满足听众对品质生活的追求，为其提供最贴心的陪伴和服务。
     * broadcastName : 重庆音乐广播
     * broadcastImg : http://img.kaolafm.net/mz/images/201512/79cf6f46-7f9e-4e32-8c04-e39111237858/default.jpg
     * icon :
     */
    /** 节目id*/
    @SerializedName("programId")
    private int programId;

    /** 广播id*/
    @SerializedName("broadcastId")
    private long broadcastId;

    /** 下一期节目id，默认为-1*/
    @SerializedName("nextProgramId")
    private int nextProgramId;

    /** 上一期节目id，默认为-1*/
    @SerializedName("preProgramId")
    private int preProgramId;

    /** 节目名称*/
    @SerializedName("title")
    private String title;

    /** 回听地址*/
    @SerializedName("backLiveUrl")
    private String backLiveUrl;

    /** 直播流地址*/
    @SerializedName("playUrl")
    private String playUrl;

    /** 主播名字*/
    @SerializedName("comperes")
    private String comperes;

    /** 节目展示开始时间，如“11:00”*/
    @SerializedName("beginTime")
    private String beginTime;

    /** 节目展示结束时间，如“12:00”*/
    @SerializedName("endTime")
    private String endTime;

    /** 节目开始时间，单位毫秒*/
    @SerializedName("startTime")
    private long startTime;

    /** 节目结束时间，单位毫秒*/
    @SerializedName("finishTime")
    private long finishTime;

    /** 播放状态，1-直播中，2-回放，3-未开播*/
    @SerializedName("status")
    private int status;

    /**是否预定节目，0-未预定，1-预定 */
    @Deprecated
    @SerializedName("isSubscribe")
    private int isSubscribe;

    /** 节目简介*/
    @SerializedName("desc")
    private String desc;

    /** 广播简介*/
    @SerializedName("broadcastDesc")
    private String broadcastDesc;

    /** 广播名称*/
    @SerializedName("broadcastName")
    private String broadcastName;

    /** 广播图片*/
    @SerializedName("broadcastImg")
    private String broadcastImg;

    /** 节目图标url*/
    @Deprecated
    @SerializedName("icon")
    private String icon;

    /** 广播回放的状态 1开启节目（建议置灰，不可点击，可以提示回放生成中），0关闭节目（建议隐藏）, 2可播放 */
    @SerializedName("programEnable")
    private int programEnable;

    public int getProgramEnable() {
        if (programEnable == 1 && backLiveUrl != null && !"".equals(backLiveUrl.trim())){
            return 2;
        }
        return programEnable;
    }

    public void setProgramEnable(int programEnable) {
        this.programEnable = programEnable;
    }

    public int getProgramId() {
        return programId;
    }

    public void setProgramId(int programId) {
        this.programId = programId;
    }

    public long getBroadcastId() {
        return broadcastId;
    }

    public void setBroadcastId(long broadcastId) {
        this.broadcastId = broadcastId;
    }

    public int getNextProgramId() {
        return nextProgramId;
    }

    public void setNextProgramId(int nextProgramId) {
        this.nextProgramId = nextProgramId;
    }

    public int getPreProgramId() {
        return preProgramId;
    }

    public void setPreProgramId(int preProgramId) {
        this.preProgramId = preProgramId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBackLiveUrl() {
        return backLiveUrl;
    }

    public void setBackLiveUrl(String backLiveUrl) {
        this.backLiveUrl = backLiveUrl;
    }

    public String getPlayUrl() {
        return playUrl;
    }

    public void setPlayUrl(String playUrl) {
        this.playUrl = playUrl;
    }

    public String getComperes() {
        return comperes;
    }

    public void setComperes(String comperes) {
        this.comperes = comperes;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(long finishTime) {
        this.finishTime = finishTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getIsSubscribe() {
        return isSubscribe;
    }

    public void setIsSubscribe(int isSubscribe) {
        this.isSubscribe = isSubscribe;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getBroadcastDesc() {
        return broadcastDesc;
    }

    public void setBroadcastDesc(String broadcastDesc) {
        this.broadcastDesc = broadcastDesc;
    }

    public String getBroadcastName() {
        return broadcastName;
    }

    public void setBroadcastName(String broadcastName) {
        this.broadcastName = broadcastName;
    }

    public String getBroadcastImg() {
        return broadcastImg;
    }

    public void setBroadcastImg(String broadcastImg) {
        this.broadcastImg = broadcastImg;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.programId);
        dest.writeLong(this.broadcastId);
        dest.writeInt(this.nextProgramId);
        dest.writeInt(this.preProgramId);
        dest.writeString(this.title);
        dest.writeString(this.backLiveUrl);
        dest.writeString(this.playUrl);
        dest.writeString(this.comperes);
        dest.writeString(this.beginTime);
        dest.writeString(this.endTime);
        dest.writeLong(this.startTime);
        dest.writeLong(this.finishTime);
        dest.writeInt(this.status);
        dest.writeInt(this.isSubscribe);
        dest.writeString(this.desc);
        dest.writeString(this.broadcastDesc);
        dest.writeString(this.broadcastName);
        dest.writeString(this.broadcastImg);
        dest.writeString(this.icon);
        dest.writeInt(this.programEnable);
    }

    public ProgramDetails() {
    }

    protected ProgramDetails(Parcel in) {
        this.programId = in.readInt();
        this.broadcastId = in.readLong();
        this.nextProgramId = in.readInt();
        this.preProgramId = in.readInt();
        this.title = in.readString();
        this.backLiveUrl = in.readString();
        this.playUrl = in.readString();
        this.comperes = in.readString();
        this.beginTime = in.readString();
        this.endTime = in.readString();
        this.startTime = in.readLong();
        this.finishTime = in.readLong();
        this.status = in.readInt();
        this.isSubscribe = in.readInt();
        this.desc = in.readString();
        this.broadcastDesc = in.readString();
        this.broadcastName = in.readString();
        this.broadcastImg = in.readString();
        this.icon = in.readString();
        this.programEnable = in.readInt();
    }

    public static final Creator<ProgramDetails> CREATOR = new Creator<ProgramDetails>() {
        @Override
        public ProgramDetails createFromParcel(Parcel source) {
            return new ProgramDetails(source);
        }

        @Override
        public ProgramDetails[] newArray(int size) {
            return new ProgramDetails[size];
        }
    };
}
