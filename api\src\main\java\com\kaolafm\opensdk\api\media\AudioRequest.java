package com.kaolafm.opensdk.api.media;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.media.model.AudioPlayInfo;
import com.kaolafm.opensdk.http.core.HttpCallback;
import java.util.List;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: AudioRequest.java                                               
 *                                                                  *
 * Created in 2018/8/13 上午10:15                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class AudioRequest extends BaseRequest {


    private AudioService mService;

    public AudioRequest() {
        mService = obtainRetrofitService(AudioService.class);
    }


    /**
     * 获取单曲详情
     *
     * @param audioId  单曲Id
     * @param callback 回调
     */
    public void getAudioDetails(long audioId, HttpCallback<AudioDetails> callback) {
        doHttpDeal(mService.getAudioDetails(audioId), BaseResult::getResult, callback);
    }

    /**
     * 获取播放信息
     *
     * @param playUrlId  播放id
     * @param callback 回调
     */
    public void getAudioPlayInfo(String playUrlId, HttpCallback<AudioPlayInfo> callback) {
        android.util.Log.i("AudioRequest", "getAudioPlayInfo called with playUrlId: " + playUrlId);

        HttpCallback<AudioPlayInfo> wrappedCallback = new HttpCallback<AudioPlayInfo>() {
            @Override
            public void onSuccess(AudioPlayInfo result) {
                android.util.Log.i("AudioRequest", "getAudioPlayInfo SUCCESS: " + result.toString());
                if (result.getPlayInfoList() != null) {
                    android.util.Log.i("AudioRequest", "playInfoList size: " + result.getPlayInfoList().size());
                    for (int i = 0; i < result.getPlayInfoList().size(); i++) {
                        android.util.Log.i("AudioRequest", "playInfoList[" + i + "]: " + result.getPlayInfoList().get(i).toString());
                    }
                } else {
                    android.util.Log.e("AudioRequest", "playInfoList is NULL!");
                }
                callback.onSuccess(result);
            }

            @Override
            public void onError(com.kaolafm.opensdk.http.error.ApiException exception) {
                android.util.Log.e("AudioRequest", "getAudioPlayInfo ERROR: " + exception.toString());
                callback.onError(exception);
            }
        };

        doHttpDeal(mService.getAudioPlayInfo(playUrlId), BaseResult::getResult, wrappedCallback);
    }

    /**
     * 一次请求多个单曲详情
     *
     * @param audioIds 单曲id数组
     * @param callback 回调
     */
    public void getAudioDetails(Long[] audioIds, HttpCallback<List<AudioDetails>> callback) {
        doHttpDeal(mService.getAudioDetails(StringUtil.array2String(audioIds)), BaseResult::getResult, callback);
    }

    /**
     * 获取当前时间点的报时声音单曲
     * @param callback
     */
    public void getCurrentClockAudio(HttpCallback<AudioDetails> callback) {
        doHttpDeal(mService.getCurrentClockAudio(), BaseResult::getResult, callback);
    }
}
